<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- data/payroll_structure_data.xml -->
    
    <data noupdate="1">
        <!-- 台灣薪資結構類型 -->
        <record id="tw_payroll_structure_type" model="hr.payroll.structure.type">
            <field name="name">台灣員工</field>
            <field name="default_resource_calendar_id" ref="tw_standard_calendar"/>
            <field name="country_id" ref="base.tw"/>
            <field name="wage_type">monthly</field>
        </record>

        <!-- 台灣標準薪資結構 -->
        <record id="tw_payroll_structure_standard" model="hr.payroll.structure">
            <field name="name">台灣標準薪資結構</field>
            <field name="type_id" ref="tw_payroll_structure_type"/>
            <field name="country_id" ref="base.tw"/>
        </record>

        <!-- 薪資規則類別 -->
        <record id="tw_salary_rule_category_basic" model="hr.salary.rule.category">
            <field name="name">基本薪資</field>
            <field name="code">BASIC</field>
        </record>

        <record id="tw_salary_rule_category_allowance" model="hr.salary.rule.category">
            <field name="name">津貼補助</field>
            <field name="code">ALW</field>
        </record>

        <record id="tw_salary_rule_category_overtime" model="hr.salary.rule.category">
            <field name="name">加班費</field>
            <field name="code">OT</field>
        </record>

        <record id="tw_salary_rule_category_deduction" model="hr.salary.rule.category">
            <field name="name">扣除項目</field>
            <field name="code">DED</field>
        </record>

        <record id="tw_salary_rule_category_gross" model="hr.salary.rule.category">
            <field name="name">應發總額</field>
            <field name="code">GROSS</field>
        </record>

        <record id="tw_salary_rule_category_net" model="hr.salary.rule.category">
            <field name="name">實發薪資</field>
            <field name="code">NET</field>
        </record>

        <!-- 薪資規則 -->
        <!-- 基本薪資規則 -->
        <record id="tw_salary_rule_basic" model="hr.salary.rule">
            <field name="name">基本薪資</field>
            <field name="code">BASIC</field>
            <field name="category_id" ref="tw_salary_rule_category_basic"/>
            <field name="sequence">1</field>
            <field name="condition_select">none</field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">result = contract.wage</field>
            <field name="struct_id" ref="tw_payroll_structure_standard"/>
        </record>

        <!-- 職務加給規則 -->
        <record id="tw_salary_rule_position_allowance" model="hr.salary.rule">
            <field name="name">職務加給</field>
            <field name="code">POS_ALW</field>
            <field name="category_id" ref="tw_salary_rule_category_allowance"/>
            <field name="sequence">10</field>
            <field name="condition_select">none</field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">result = contract.position_allowance or 0</field>
            <field name="struct_id" ref="tw_payroll_structure_standard"/>
        </record>

        <!-- 交通津貼規則 -->
        <record id="tw_salary_rule_transport_allowance" model="hr.salary.rule">
            <field name="name">交通津貼</field>
            <field name="code">TRANS_ALW</field>
            <field name="category_id" ref="tw_salary_rule_category_allowance"/>
            <field name="sequence">11</field>
            <field name="condition_select">none</field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">result = contract.transportation_allowance or 0</field>
            <field name="struct_id" ref="tw_payroll_structure_standard"/>
        </record>

        <!-- 伙食津貼規則 -->
        <record id="tw_salary_rule_meal_allowance" model="hr.salary.rule">
            <field name="name">伙食津貼</field>
            <field name="code">MEAL_ALW</field>
            <field name="category_id" ref="tw_salary_rule_category_allowance"/>
            <field name="sequence">12</field>
            <field name="condition_select">none</field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">result = contract.meal_allowance or 0</field>
            <field name="struct_id" ref="tw_payroll_structure_standard"/>
        </record>

        <!-- 標準加班費規則 -->
        <record id="tw_salary_rule_overtime_standard" model="hr.salary.rule">
            <field name="name">加班費 (標準)</field>
            <field name="code">OT_STD</field>
            <field name="category_id" ref="tw_salary_rule_category_overtime"/>
            <field name="sequence">20</field>
            <field name="condition_select">python</field>
            <field name="condition_python">
try:
    result = payslip.payslip_type != 'full'
except:
    result = True
            </field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute"><![CDATA[
# 標準加班費計算 - 每日分段計算（每天前2小時1.34倍，後面1.67倍）
result = 0

if payslip.employee_id and payslip.date_from and payslip.date_to and contract.wage:
    try:
        # 取得期間內的考勤記錄
        attendance_records = env['tw.hr.attendance'].search([
            ('employee_id', '=', payslip.employee_id.id),
            ('check_in_date', '>=', payslip.date_from),
            ('check_in_date', '<=', payslip.date_to),
            ('overtime_hours', '>', 0)
        ])
        
        if attendance_records:
            hourly_rate = contract.wage / 30 / 8  # 月薪轉時薪
            
            # 按日期分組計算
            daily_overtime = {}
            for record in attendance_records:
                date_key = record.check_in_date
                if date_key not in daily_overtime:
                    daily_overtime[date_key] = 0
                daily_overtime[date_key] += record.displayed_overtime_hours
            
            # 對每一天的加班時數進行分段計算
            for date_key, daily_hours in daily_overtime.items():
                if daily_hours > 0:
                    # 每日分段：前2小時1.34倍，超過2小時的部分1.67倍
                    if daily_hours <= 2:
                        result += daily_hours * hourly_rate * 1.34
                    else:
                        result += 2 * hourly_rate * 1.34 + (daily_hours - 2) * hourly_rate * 1.67
    except:
        result = 0
]]></field>
            <field name="struct_id" ref="tw_payroll_structure_standard"/>
        </record>

        <!-- 完整加班費規則 -->
        <record id="tw_salary_rule_overtime_full" model="hr.salary.rule">
            <field name="name">加班費 (完整)</field>
            <field name="code">OT_FULL</field>
            <field name="category_id" ref="tw_salary_rule_category_overtime"/>
            <field name="sequence">21</field>
            <field name="condition_select">python</field>
            <field name="condition_python">
try:
    result = payslip.payslip_type == 'full'
except:
    result = False
            </field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute"><![CDATA[
# 完整版薪資單加班費計算 - 每日分段計算（使用總時數）
result = 0

if payslip.employee_id and payslip.date_from and payslip.date_to and contract.wage:
    try:
        # 取得期間內的考勤記錄
        attendance_records = env['tw.hr.attendance'].search([
            ('employee_id', '=', payslip.employee_id.id),
            ('check_in_date', '>=', payslip.date_from),
            ('check_in_date', '<=', payslip.date_to),
            ('overtime_hours', '>', 0)
        ])
        
        if attendance_records:
            hourly_rate = contract.wage / 30 / 8  # 月薪轉時薪
            
            # 按日期分組計算
            daily_overtime = {}
            for record in attendance_records:
                date_key = record.check_in_date
                if date_key not in daily_overtime:
                    daily_overtime[date_key] = 0
                # 完整版使用實際加班時數（包含隱藏時數）
                daily_overtime[date_key] += record.overtime_hours
            
            # 對每一天的加班時數進行分段計算
            for date_key, daily_hours in daily_overtime.items():
                if daily_hours > 0:
                    # 每日分段：前2小時1.34倍，超過2小時的部分1.67倍
                    if daily_hours <= 2:
                        result += daily_hours * hourly_rate * 1.34
                    else:
                        result += 2 * hourly_rate * 1.34 + (daily_hours - 2) * hourly_rate * 1.67
    except:
        result = 0
]]></field>
            <field name="struct_id" ref="tw_payroll_structure_standard"/>
        </record>

        <!-- 勞保費扣除規則 -->
        <record id="tw_salary_rule_labor_insurance" model="hr.salary.rule">
            <field name="name">勞保費 (個人負擔)</field>
            <field name="code">LABOR_INS</field>
            <field name="category_id" ref="tw_salary_rule_category_deduction"/>
            <field name="sequence">30</field>
            <field name="condition_select">python</field>
            <field name="condition_python">result = contract.labor_insurance</field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">
# 勞保費計算 (簡化版本，實際應依照勞保局費率表)
insurable_salary = min(contract.wage or 0, 45800)  # 2024年勞保投保薪資上限
labor_insurance_rate = 0.095  # 9.5% (個人負擔20%)
result = -(insurable_salary * labor_insurance_rate * 0.2)
            </field>
            <field name="struct_id" ref="tw_payroll_structure_standard"/>
        </record>

        <!-- 健保費扣除規則 -->
        <record id="tw_salary_rule_health_insurance" model="hr.salary.rule">
            <field name="name">健保費 (個人負擔)</field>
            <field name="code">HEALTH_INS</field>
            <field name="category_id" ref="tw_salary_rule_category_deduction"/>
            <field name="sequence">31</field>
            <field name="condition_select">python</field>
            <field name="condition_python">result = contract.health_insurance</field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">
# 健保費計算
insurable_salary = min(contract.wage or 0, 182000)  # 健保投保薪資上限
health_insurance_rate = 0.0517  # 5.17% (個人負擔30%)
result = -(insurable_salary * health_insurance_rate * 0.3)
            </field>
            <field name="struct_id" ref="tw_payroll_structure_standard"/>
        </record>

        <!-- 勞退提繳規則 -->
        <record id="tw_salary_rule_pension_fund" model="hr.salary.rule">
            <field name="name">勞退提繳 (個人負擔)</field>
            <field name="code">PENSION</field>
            <field name="category_id" ref="tw_salary_rule_category_deduction"/>
            <field name="sequence">32</field>
            <field name="condition_select">python</field>
            <field name="condition_python">result = contract.pension_fund</field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">
# 勞退提繳 (個人可自提0-6%)
pensionable_salary = min(contract.wage or 0, 150000)  # 勞退月提繳工資上限
pension_rate = 0.06  # 預設6%自提
result = -(pensionable_salary * pension_rate)
            </field>
            <field name="struct_id" ref="tw_payroll_structure_standard"/>
        </record>

        <!-- 應發總額計算 -->
        <record id="tw_salary_rule_gross" model="hr.salary.rule">
            <field name="name">應發總額</field>
            <field name="code">GROSS</field>
            <field name="category_id" ref="tw_salary_rule_category_gross"/>
            <field name="sequence">90</field>
            <field name="condition_select">none</field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">
result = categories['BASIC'] + categories['ALW'] + categories['OT']
            </field>
            <field name="struct_id" ref="tw_payroll_structure_standard"/>
        </record>

        <!-- 實發薪資計算 -->
        <record id="tw_salary_rule_net" model="hr.salary.rule">
            <field name="name">實發薪資</field>
            <field name="code">NET</field>
            <field name="category_id" ref="tw_salary_rule_category_net"/>
            <field name="sequence">99</field>
            <field name="condition_select">none</field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">
result = categories['GROSS'] + categories['DED']  # DED is negative
            </field>
            <field name="struct_id" ref="tw_payroll_structure_standard"/>
        </record>

        <!-- 其他輸入類型 -->
        <record id="tw_input_type_overtime_hours" model="hr.payslip.input.type">
            <field name="name">加班時數</field>
            <field name="code">OVERTIME_HOURS</field>
        </record>

        <record id="tw_input_type_bonus" model="hr.payslip.input.type">
            <field name="name">績效獎金</field>
            <field name="code">BONUS</field>
        </record>

        <record id="tw_input_type_commission" model="hr.payslip.input.type">
            <field name="name">業務獎金</field>
            <field name="code">COMMISSION</field>
        </record>

        <record id="tw_input_type_absence" model="hr.payslip.input.type">
            <field name="name">缺勤扣款</field>
            <field name="code">ABSENCE</field>
        </record>

        <record id="tw_input_type_late" model="hr.payslip.input.type">
            <field name="name">遲到扣款</field>
            <field name="code">LATE</field>
        </record>

    </data>
</odoo>