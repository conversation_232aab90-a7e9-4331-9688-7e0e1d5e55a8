<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- 定時任務：處理未處理的出勤記錄 -->
        <record id="cron_process_unprocessed_attendances" model="ir.cron">
            <field name="name">處理未處理的出勤記錄</field>
            <field name="model_id" ref="hr_attendance.model_hr_attendance"/>
            <field name="state">code</field>
            <field name="code">model._cron_process_unprocessed_attendances()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="active" eval="True"/>
            <field name="nextcall" eval="(DateTime.now() + timedelta(days=1)).replace(hour=1, minute=0, second=0)"/>
        </record>

        <!-- 定時任務：自動創建加班記錄 - 已移除，功能整合到 tw.hr.attendance -->

        <!-- 定時任務：同步考勤記錄一致性檢查 -->
        <record id="cron_sync_attendance_consistency" model="ir.cron">
            <field name="name">同步考勤記錄一致性檢查</field>
            <field name="model_id" ref="hr_attendance.model_hr_attendance"/>
            <field name="state">code</field>
            <field name="code">model._cron_sync_attendance_consistency()</field>
            <field name="interval_number">6</field>
            <field name="interval_type">hours</field>
            <field name="active" eval="True"/>
            <field name="user_id" ref="base.user_root"/>
        </record>

        <!-- 定時任務：自動同步新的 hr.attendance 記錄 -->
        <record id="cron_auto_sync_new_attendances" model="ir.cron">
            <field name="name">自動同步新的考勤記錄</field>
            <field name="model_id" ref="hr_attendance.model_hr_attendance"/>
            <field name="state">code</field>
            <field name="code">model._cron_auto_sync_new_attendances()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">hours</field>
            <field name="active" eval="True"/>
            <field name="user_id" ref="base.user_root"/>
        </record>
        
    </data>
</odoo>