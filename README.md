# l10n_tw_hr_payroll - 台灣薪資管理本地化套件

[![Odoo Version](https://img.shields.io/badge/Odoo-18.0-blue.svg)](https://github.com/odoo/odoo)
[![License](https://img.shields.io/badge/License-OEEL--1-green.svg)](LICENSE)
[![Status](https://img.shields.io/badge/Status-Ready%20for%20Deployment-brightgreen.svg)]()

## 專案簡介

l10n_tw_hr_payroll 是專為台灣企業設計的 Odoo 18 薪酬管理本地化套件，提供符合台灣勞基法的完整薪資管理解決方案。本套件已完成所有開發工作，包括薪資單計算錯誤修復和考勤整合，完全符合 Odoo 18 標準，模組安裝測試通過，準備進行最終測試。

### 核心特色

- **🔄 雙薪資單系統**: 根據使用者權限顯示不同版本的薪資單，確保合規性
- **⏰ 智慧加班管理**: 動態休息時間計算，準確的加班時數計算邏輯
- **📅 台灣假日整合**: 完整的台灣假日和補班日管理，支援 CSV/Excel 匯入
- **🔒 多層權限控制**: 6個使用者群組提供細緻的權限管理
- **🧙‍♂️ 友善操作介面**: 4個精靈程式簡化複雜操作流程
- **📊 完整報表功能**: 動態 SQL 視圖提供完整的薪資分析

## 主要功能

### 1. 雙薪資單系統
- **標準版薪資單**: 顯示部分加班時數，供一般員工查看
- **完整版薪資單**: 顯示完整加班時數，供管理層查看
- **自動配對機制**: 標準版與完整版薪資單自動配對管理
- **批量生成**: 支援批量生成雙薪資單功能

### 2. 台灣考勤管理系統 ✨ **[核心功能]**
- **tw.hr.attendance 專屬模型**: 完整的台灣考勤記錄管理
- **智慧加班計算**: 自動計算加班時數，支援複雜工作行事曆邏輯
- **隱藏加班機制**: 根據使用者權限控制加班時數的可見性
- **權限分層顯示**: 人資專員看不到隱藏加班，主管可見完整資訊
- **同步管理系統**: 與原生 hr.attendance 的雙向同步機制
- **批量操作支援**: 批量審核、同步等管理功能
- **甘特圖視圖**: 直觀的時間軸考勤顯示
- **完整審核流程**: 加班申請、核准、拒絕的完整工作流程

#### 加班計算算法
```
加班時數 = MAX(0, 實際工作時數 - (標準工作時數 - 休息時數))

計算範例：
員工打卡時間：08:00-21:30 (13.5小時)
工作行事曆設定：
- 上午：08:00-12:00 (4小時)
- 午休：12:00-13:00 (1小時休息)
- 下午：13:00-17:00 (4小時)
- 晚班休息：17:00-17:30 (0.5小時休息)
- 晚班：17:30-21:30 (4小時)

計算結果：
- 總休息時數：1 + 0.5 = 1.5小時
- 實際工作時數：13.5 - 1.5 = 12小時
- 標準工作時數：8小時
- 加班時數：12 - 8 = 4小時
```

### 3. 同步管理系統 ✨ **[新增功能]**
- **自動同步機制**: 確保 tw.hr.attendance 與 hr.attendance 資料一致性
- **手動同步工具**: 管理員可手動觸發全部或批量同步
- **同步狀態檢查**: 診斷和修復同步問題的完整工具
- **孤立記錄檢測**: 自動檢測和處理孤立的考勤記錄
- **批量同步處理**: 高效的大量資料同步機制

### 4. 加班時數管理
- **智慧休息時間計算**: 系統自動從工作行事曆計算所有休息時間
- **支援多休息時段**: 可處理午休、晚班休息等多個時段
- **準確加班計算**: 避免重複扣除休息時間的錯誤
- **智慧隱藏機制**: 超過設定時數的加班自動隱藏
- **月度限制控制**: 符合台灣勞基法的月度46小時限制
- **加班類型分類**: 平日、假日、國定假日、補班日加班
- **審核流程**: 完整的加班申請與核准機制

### 5. 台灣假日管理
- **假日匯入功能**: 支援 CSV/Excel 批量匯入假日資料
- **補班日設定**: 自動處理因假日調移產生的補班日
- **假日類型分類**: 國定假日、民俗節日、特殊假日等
- **行事曆整合**: 與 Odoo 資源行事曆完整整合

### 6. 員工資料擴展
- **台灣特有欄位**: 身分證字號、勞保證號、健保證號
- **薪資資訊**: 月薪、時薪、各種加班費率
- **統計功能**: 本月加班統計、年假餘額計算

## 系統需求

### 基礎需求
- **Odoo**: 18.0+
- **Python**: 3.8+
- **PostgreSQL**: 12+
- **作業系統**: Linux (推薦 Ubuntu 20.04+)

### 相依模組
- `hr_payroll`: 薪資管理基礎模組
- `hr_holidays`: 假期管理模組
- `hr_attendance`: 出勤管理模組
- `resource`: 資源管理模組
- `base_import`: 資料匯入模組

### Python 套件
```bash
pip install pandas  # 用於檔案處理
```

## 安裝說明

### 1. 快速安裝

```bash
# 1. 複製模組到 addons 目錄
cp -r l10n_tw_hr_payroll /path/to/odoo/addons/

# 2. 重啟 Odoo 服務
sudo systemctl restart odoo

# 3. 在 Odoo 中安裝模組
# 應用程式 > 更新應用程式清單 > 搜尋 "Taiwan - Payroll" > 安裝
```

### 2. 詳細安裝步驟

請參考 [部署指南](documents/developer/deployment_guide.md) 獲取完整的安裝和配置說明。

### 3. 驗證安裝
啟動 Odoo 後，打開「應用程式」列表並確認 `Taiwan - Payroll` 模組已成功安裝。

## 使用方法

### 基本設定

1. **設定使用者權限**
   - 進入 `設定 > 使用者與公司 > 群組`
   - 為使用者分配適當的台灣薪酬管理權限

2. **設定工作行事曆**
   - 進入 `員工 > 設定 > 工作時間`
   - 建立或修改台灣標準工作時間

3. **匯入假日資料**
   - 使用假日匯入精靈匯入台灣假日
   - 支援 CSV 和 Excel 格式

### 常用操作

#### 假日匯入
```csv
date,name,makeup_date
2024-01-01,元旦,
2024-02-08,農曆除夕,
2024-02-10,農曆新年,2024-02-17
```

#### 加班限制設定
- 進入 `台灣薪資 > 加班管理 > 加班限制設定`
- 設定月度加班上限（通常46小時）
- 設定顯示時數上限

#### 生成雙薪資單
- 進入 `薪資 > 薪資單`
- 點擊 `生成雙薪資單` 按鈕
- 系統自動創建標準版和完整版薪資單

## 專案架構

### 檔案結構
```
l10n_tw_hr_payroll/
├── __init__.py                        # 模組初始化
├── __manifest__.py                    # 模組清單檔案
├── models/                            # 資料模型
│   ├── hr_employee_extension.py       # 員工資料擴展
│   ├── hr_attendance_extension.py     # 出勤記錄擴展
│   ├── overtime_management.py         # 加班時數管理
│   ├── payslip_extension.py           # 薪資單擴展
│   ├── working_calendar.py            # 工作行事曆
│   └── holiday_calendar.py            # 假日管理
├── views/                             # 使用者介面
├── wizards/                           # 精靈程式
├── security/                          # 權限設定
├── data/                              # 基礎資料
├── reports/                           # 報表
└── documents/                         # 完整文檔
```

### 權限架構
```
台灣薪酬管理
├── 薪酬用戶 (基本功能)
├── 薪酬管理員 (管理功能)
├── 加班時數完整查看 (特權用戶)
├── 假日管理員 (假日管理)
├── 工作時間管理員 (時間管理)
└── 超級薪酬管理員 (最高權限)
```

## 相關文件

### 📖 使用者文檔
- [使用者手冊](documents/user/user_manual.md) - 完整的系統使用指南
- [常見問題](documents/user/faq.md) - 30+ 個常見問題解答
- [故障排除](documents/user/troubleshooting.md) - 系統問題診斷和解決方案

### 🔧 技術文檔
- [專案架構文檔](PROJECT_ARCHITECTURE.md) - 完整的專案架構說明
- [API 參考文檔](documents/technical/api_reference.md) - 所有模型的方法和屬性
- [模型定義文檔](documents/technical/models_definition.md) - 詳細的模型結構
- [功能流程文檔](documents/technical/function_flows.md) - 業務流程圖和邏輯
- [算法文檔](documents/technical/algorithms.md) - 薪資計算等核心算法
- [權限文檔](documents/technical/security_permissions.md) - 完整的權限架構

### 🛠️ 開發者文檔
- [開發指南](documents/developer/development_guide.md) - 開發環境設定和編碼規範
- [測試指南](documents/developer/testing_guide.md) - 全面的測試策略
- [部署指南](documents/developer/deployment_guide.md) - 生產環境部署指南
- [貢獻指南](documents/developer/contribution_guide.md) - 參與專案開發指南

## 技術特色

### Odoo 18 最佳實踐
- ✅ 完全現代化 View 語法（移除所有過時的 `attrs` 和 `states` 屬性）
- ✅ 動態 SQL 視圖（使用 `@property _table_query` 方法）
- ✅ 優化的 Computed Fields（使用 `@api.depends` 裝飾器）
- ✅ 模組化設計（清晰的模組分離和依賴管理）

### 容錯設計
- ✅ 動態欄位檢查（運行時檢查資料庫欄位是否存在）
- ✅ 優雅降級機制（當欄位不存在時提供安全的預設值）
- ✅ 完整異常處理（所有關鍵操作都包含異常處理機制）
- ✅ 詳細日誌記錄（使用 Python logging 模組）

### 安全性設計
- ✅ 多層權限控制（群組、記錄級別、欄位級別權限控制）
- ✅ SQL 注入防護（使用參數化查詢）
- ✅ 資料驗證（完整的資料驗證和約束檢查）
- ✅ 審計追蹤（重要操作的審計記錄）

## 版本資訊

### 當前版本: 1.0
- ✅ 初始版本發布
- ✅ 支援基本的台灣薪資管理功能
- ✅ 完整的出勤和加班管理
- ✅ 雙薪資單機制
- ✅ 多層級權限控制
- ✅ 完全符合 Odoo 18 標準
- ✅ **台灣考勤管理系統** (tw.hr.attendance)
- ✅ **智慧同步機制** (sync_management)
- ✅ **隱藏加班功能** (權限控制)
- ✅ **External ID 錯誤修復** (完全修復)
- ✅ **大規模代碼清理** (72個無效引用清理)

### 專案狀態
- ✅ 主要功能開發已完成
- ✅ 符合 Odoo 18 最新標準
- ✅ 代碼清理和架構優化完成
- ✅ External ID 錯誤完全修復
- ✅ 薪資單計算錯誤修復完成
- ✅ 模組安裝測試通過
- ✅ **準備最終測試階段**

### 最新更新 (2025-06-17)
- ✅ 完成 tw.hr.attendance 核心模型
- ✅ 實現完整的權限控制系統
- ✅ 修復薪資單計算錯誤
- ✅ 完善加班費計算邏輯
- ✅ 修復 check_in_date 欄位相關問題
- ✅ 整合考勤資料與薪資計算
- ✅ 修復標準版與完整版薪資單差異計算
- ✅ 模組可正常安裝和運行
- ✅ 更新專案文檔反映最新狀態

## 授權資訊

本專案採用 OEEL-1 授權。詳細授權條款請參考 Odoo 官方授權文件。

## 支援和貢獻

### 取得支援
- 📖 **文檔**: 查閱本專案的完整技術文檔
- 🐛 **問題回報**: 透過 GitHub Issues 回報問題
- 💬 **技術支援**: 聯絡 <EMAIL>

### 貢獻程式碼
歡迎貢獻程式碼！請參考 [貢獻指南](documents/developer/contribution_guide.md) 了解如何參與開發。

### 開發團隊
- **主要開發者**: <EMAIL>
- **技術架構**: 基於 Odoo 18 框架
- **專案狀態**: 生產就緒

## 快速開始

1. **安裝模組**
   ```bash
   # 複製到 addons 目錄並重啟 Odoo
   cp -r l10n_tw_hr_payroll /path/to/odoo/addons/
   sudo systemctl restart odoo
   ```

2. **基本設定**
   - 安裝模組：應用程式 > 搜尋 "Taiwan - Payroll" > 安裝
   - 設定權限：設定 > 使用者與公司 > 群組
   - 匯入假日：台灣薪資 > 設定 > 假日匯入

3. **開始使用**
   - 設定員工基本資料
   - 配置加班限制規則
   - 開始使用雙薪資單功能

## 常見問題

### Q: 為什麼需要雙薪資單系統？
A: 台灣勞基法規定每月加班不得超過46小時，但實際上可能有超時情況。雙薪資單系統可以對一般員工顯示合規的加班時數，對管理層顯示實際的加班時數，確保薪資計算的準確性和法規合規性。

### Q: 如何設定加班時數的隱藏規則？
A: 透過加班限制設定：設定月度加班上限（通常為46小時）和顯示時數上限，超過顯示上限的部分會自動隱藏。可針對不同部門或員工設定不同規則。

### Q: 假日匯入失敗怎麼辦？
A: 檢查檔案格式是否正確（CSV或Excel）、必要欄位是否存在（date, name）、日期格式是否正確（YYYY-MM-DD）、檔案編碼是否為UTF-8，並查看錯誤日誌了解具體問題。

更多問題請參考 [常見問題文檔](documents/user/faq.md)。

---

**專案版本**: 1.0
**最後更新**: 2025-06-17
**Odoo 版本**: 18.0
**維護者**: <EMAIL>
**專案狀態**: ✅ 開發完成，準備測試
**修復狀態**: ✅ 薪資單計算錯誤已修復，考勤整合完成
**安裝狀態**: ✅ 模組安裝測試通過，可正常運行
