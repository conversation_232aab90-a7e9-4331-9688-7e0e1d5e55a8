# =============================================================================
# 台灣薪酬本地化模組 (l10n_tw_hr_payroll) 
# =============================================================================

# __manifest__.py
{
    'name': '台灣 - 薪酬本地化',
    'version': '18.0.1.0.0',
    'category': 'Human Resources/Payroll/Localizations',
    'license': 'LGPL-3',
    'author': 'Your Company',
    'depends': [
        'hr_payroll',
        'hr_holidays',
        'resource',
        'base_import',
    ],
    'data': [
        # 安全設定
        'security/tw_payroll_groups.xml',
        'security/ir.model.access.csv',
        'security/tw_payroll_security.xml',
        
        # 基礎資料
        'data/holiday_data.xml',
        'data/payroll_structure_data.xml',
        
        # 視圖
        'views/holiday_import_views.xml',
        'views/working_calendar_views.xml',
        'views/overtime_views.xml',
        'views/payslip_views.xml',
        
        # 嚮導
        'wizards/holiday_import_wizard_views.xml',
        'wizards/overtime_limit_wizard_views.xml',
        
        # 報表
        'report/payslip_report_templates.xml',
    ],
    'demo': [
        'demo/demo_data.xml',
    ],
    'installable': True,
    'application': False,
    'auto_install': False,
}

# =============================================================================
# 模組目錄結構
# =============================================================================

"""
l10n_tw_hr_payroll/
├── __init__.py
├── __manifest__.py
├── models/
│   ├── __init__.py
│   ├── holiday_calendar.py          # 假日管理
│   ├── working_calendar.py          # 工作時間管理 (補班)
│   ├── overtime_management.py       # 加班時數管理
│   ├── payslip_extension.py         # 薪資單擴展
│   └── hr_employee_extension.py     # 員工資料擴展
├── security/
│   ├── tw_payroll_groups.xml        # 用戶組定義
│   ├── ir.model.access.csv          # 模型存取權限
│   └── tw_payroll_security.xml      # 記錄級別安全規則
├── wizard/
│   ├── __init__.py
│   ├── holiday_import_wizard.py     # 假日匯入嚮導
│   └── overtime_limit_wizard.py     # 加班時數限制設定嚮導
├── views/
│   ├── holiday_import_views.xml
│   ├── working_calendar_views.xml
│   ├── overtime_views.xml
│   └── payslip_views.xml
├── data/
│   ├── holiday_data.xml             # 預設假日資料
│   └── payroll_structure_data.xml   # 薪資結構資料
├── report/
│   └── payslip_report_templates.xml # 薪資單報表模板
└── demo/
    └── demo_data.xml                # 示例資料
"""