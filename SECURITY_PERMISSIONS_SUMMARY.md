# 台灣薪酬管理模組 - 安全權限配置摘要

## 概述
本文件記錄了 `l10n_tw_hr_payroll` 模組的完整安全權限配置，包括新增的出勤整合功能權限設定。

## 權限群組架構

### 1. 基礎權限群組

#### 薪酬用戶 (`group_tw_payroll_user`)
- **描述**: 基本薪酬功能使用者
- **權限**: 可查看標準薪資單和自己的出勤/加班記錄
- **繼承**: `hr_payroll.group_hr_payroll_user`

#### 薪酬管理員 (`group_tw_payroll_manager`)
- **描述**: 薪酬管理員
- **權限**: 可管理薪資結構和基本設定
- **繼承**: `group_tw_payroll_user`, `hr_payroll.group_hr_payroll_manager`

### 2. 專業權限群組

#### 加班時數完整查看 (`group_overtime_full_access`)
- **描述**: 可查看完整加班時數的特權用戶
- **權限**: 可查看隱藏的加班時數
- **繼承**: `group_tw_payroll_manager`

#### 假日管理員 (`group_holiday_manager`)
- **描述**: 假日管理員
- **權限**: 可管理公眾假日和補班日設定
- **繼承**: `group_tw_payroll_user`

#### 工作時間管理員 (`group_working_time_manager`)
- **描述**: 工作時間管理員
- **權限**: 可管理工作時間和補班日設定
- **繼承**: `group_tw_payroll_manager`

### 3. 新增整合功能群組

#### 出勤整合管理員 (`group_attendance_integration_manager`)
- **描述**: 出勤整合管理員
- **權限**: 可管理出勤與加班記錄的整合功能，包括批量操作和自動處理
- **繼承**: `group_tw_payroll_manager`

#### 部門主管 (`group_department_supervisor`)
- **描述**: 部門主管
- **權限**: 可查看部門員工的出勤和加班整合資料，可核准加班申請
- **繼承**: `group_tw_payroll_user`

#### 超級薪酬管理員 (`group_tw_payroll_super_admin`)
- **描述**: 最高權限管理員
- **權限**: 可查看所有薪酬相關資訊包括隱藏的加班時數和完整的整合功能
- **繼承**: 所有專業群組

## 模型權限配置 (ir.model.access.csv)

### 1. 加班記錄權限 (`tw.hr.attendance (原 tw.overtime.record 已移除)`)
| 群組 | 讀取 | 寫入 | 創建 | 刪除 | 說明 |
|------|------|------|------|------|------|
| 薪酬用戶 | ✓ | ✓ | ✓ | ✗ | 可管理自己的加班記錄 |
| 薪酬管理員 | ✓ | ✓ | ✓ | ✓ | 完整權限 |
| 部門主管 | ✓ | ✓ | ✗ | ✗ | 可查看和修改部門員工記錄 |
| 整合管理員 | ✓ | ✓ | ✓ | ✓ | 完整整合功能權限 |
| 超級管理員 | ✓ | ✓ | ✓ | ✓ | 最高權限 |

### 2. 出勤記錄擴展權限 (`hr.attendance`)
| 群組 | 讀取 | 寫入 | 創建 | 刪除 | 說明 |
|------|------|------|------|------|------|
| 薪酬用戶 | ✓ | ✗ | ✗ | ✗ | 只能查看自己的記錄 |
| 薪酬管理員 | ✓ | ✓ | ✗ | ✗ | 可修改但不能創建/刪除 |
| 整合管理員 | ✓ | ✓ | ✓ | ✗ | 可創建整合記錄 |
| 超級管理員 | ✓ | ✓ | ✓ | ✓ | 完整權限 |

### 3. 員工記錄擴展權限 (`hr.employee`)
| 群組 | 讀取 | 寫入 | 創建 | 刪除 | 說明 |
|------|------|------|------|------|------|
| 薪酬用戶 | ✓ | ✗ | ✗ | ✗ | 只能查看 |
| 薪酬管理員 | ✓ | ✓ | ✗ | ✗ | 可修改員工資料 |
| 整合管理員 | ✓ | ✓ | ✗ | ✗ | 可修改整合相關欄位 |

## 記錄規則 (Record Rules)

### 1. 資料存取限制

#### 員工個人資料規則
- **一般員工**: 只能查看自己的出勤和加班記錄
- **部門主管**: 可查看部門員工的記錄
- **管理員**: 可查看所有記錄

#### 自動創建記錄的特殊限制
- **自動創建的加班記錄**: 一般用戶無法修改關鍵欄位
- **自動處理的出勤記錄**: 限制修改權限
- **整合管理員**: 可修改所有自動創建的記錄

### 2. 公司範圍限制
- 所有記錄都受到公司範圍限制
- 多公司環境下的資料隔離
- 確保資料安全性

## 特殊權限考慮

### 1. 自動創建記錄的權限控制
```xml
<!-- 自動創建的加班記錄限制規則 -->
<record id="tw_overtime_record_auto_created_rule" model="ir.rule">
    <field name="domain_force">[('auto_created', '=', True), ('employee_id.user_id', '=', user.id)]</field>
</record>
```

### 2. 部門主管權限
```xml
<!-- 部門主管可查看部門員工記錄 -->
<record id="tw_overtime_record_dept_supervisor_rule" model="ir.rule">
    <field name="domain_force">[('employee_id.department_id.manager_id.user_id', '=', user.id)]</field>
</record>
```

### 3. 批量操作權限
- 只有整合管理員可執行批量操作
- 包括批量創建加班記錄
- 批量同步出勤資料

## 權限設定原則

### 1. 最小權限原則
- 每個用戶只獲得執行其工作所需的最小權限
- 分層權限設計，避免權限過度集中

### 2. 資料隔離
- 員工只能查看自己的資料
- 部門主管只能查看部門內資料
- 公司範圍的資料隔離

### 3. 審計追蹤
- 自動創建的記錄有特殊標記
- 修改權限受到嚴格控制
- 保持資料完整性

## 企業規模適應性

### 小型企業
- 可使用薪酬管理員群組處理大部分功能
- 簡化權限結構

### 中型企業
- 使用部門主管群組
- 分層管理權限

### 大型企業
- 完整使用所有權限群組
- 細緻的權限控制
- 多公司支援

## 台灣企業管理層級需求

### 1. 符合台灣勞基法
- 加班時數限制控制
- 隱藏超時加班時數
- 合規性檢查

### 2. 階層式管理
- 員工 → 部門主管 → HR管理員 → 系統管理員
- 符合台灣企業文化的管理層級

### 3. 彈性配置
- 可根據企業需求調整權限
- 支援不同規模企業的需求

## 安全檢查清單

- [x] 所有新模型都有適當的權限設定
- [x] 繼承模型的新欄位有權限控制
- [x] 記錄規則確保資料隔離
- [x] 自動創建記錄有特殊權限控制
- [x] 批量操作權限受到限制
- [x] 部門主管權限正確設定
- [x] 公司範圍限制已實施
- [x] 權限群組層級合理
- [x] 符合最小權限原則
- [x] 支援多企業規模需求

## 注意事項

1. **權限更新**: 模組更新時需要重新載入安全設定
2. **測試**: 每個權限群組都應該進行功能測試
3. **文檔**: 權限變更需要更新用戶手冊
4. **備份**: 權限設定變更前應備份現有設定
5. **監控**: 定期檢查權限使用情況和安全性

---

**最後更新**: 2025-06-12
**版本**: 1.0
**負責人**: 系統管理員