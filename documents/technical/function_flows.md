# 功能流程文檔

## 概述
本文檔詳細說明 l10n_tw_hr_payroll 模組的核心業務流程、資料流向和系統整合邏輯。

## 核心業務流程

### 1. 出勤打卡到加班記錄流程

#### 流程圖
```mermaid
graph TD
    A[員工打卡上班] --> B[記錄 check_in]
    B --> C[員工打卡下班]
    C --> D[記錄 check_out]
    D --> E[計算工作時數]
    E --> F{是否超過標準工時?}
    F -->|是| G[計算加班時數]
    F -->|否| H[結束流程]
    G --> I[判斷加班類型]
    I --> J[自動創建加班記錄]
    J --> K[檢查加班限制]
    K --> L{是否符合自動審核?}
    L -->|是| M[自動審核通過]
    L -->|否| N[等待人工審核]
    M --> O[納入薪資計算]
    N --> P[主管審核]
    P --> Q{審核結果}
    Q -->|通過| O
    Q -->|拒絕| R[記錄拒絕原因]
```

#### 詳細步驟

##### 1.1 打卡記錄創建
**觸發點**: 員工使用出勤系統打卡
**處理類別**: [`HrAttendanceExtension`](../../models/hr_attendance_extension.py:268)

```python
@api.model_create_multi
def create(self, vals_list):
    """監聽打卡記錄創建"""
    records = super().create(vals_list)
    for record in records:
        if record.check_in:
            record._process_attendance_creation()
    return records
```

**處理邏輯**:
1. 記錄打卡時間 (UTC)
2. 轉換為本地時間
3. 判斷是否為工作日
4. 計算預期工作時數

##### 1.2 下班打卡處理
**觸發點**: 員工下班打卡
**處理類別**: [`HrAttendanceExtension`](../../models/hr_attendance_extension.py:280)

```python
def write(self, vals):
    """監聽打卡記錄修改"""
    old_check_out_states = {rec.id: rec.check_out for rec in self}
    result = super().write(vals)
    
    if 'check_out' in vals:
        for record in self:
            old_check_out = old_check_out_states.get(record.id)
            if not old_check_out and record.check_out:
                record._process_attendance_checkout()
    return result
```

**處理邏輯**:
1. 計算總工作時數
2. 扣除午休時間
3. 計算加班時數
4. 判斷加班類型

##### 1.3 加班時數計算
**計算方法**: [`HrAttendanceExtension._compute_tw_overtime_hours`](../../models/hr_attendance_extension.py:330)

```python
@api.depends('worked_hours', 'expected_working_hours', 'break_hours', 'tw_overtime_type')
def _compute_tw_overtime_hours(self):
    """計算台灣加班時數 - 修復計算邏輯"""
    for record in self:
        if not record.worked_hours or not record.check_out:
            record.tw_overtime_hours = 0.0
            continue
            
        # 實際工作時數（保持原本的 worked_hours）
        actual_working_hours = record.worked_hours
        # 標準工作時數（基本工作時數 - 休息時間）
        standard_working_hours = record.expected_working_hours - record.break_hours
        
        # 計算加班時數 - 修正公式
        if actual_working_hours > standard_working_hours:
            # 正確公式: 實際工作時數 - 標準工作時數
            overtime_hours = actual_working_hours - standard_working_hours
            record.tw_overtime_hours = max(0.0, overtime_hours)
        else:
            record.tw_overtime_hours = 0.0
```

**計算公式**:
```
加班時數 = MAX(0, 實際工作時數 - 標準工作時數)

其中：
- 標準工作時數 = 基本工作時數 - 休息時間
- 休息時間 = 從工作行事曆動態計算所有 break 時間總和

範例計算：
- 在公司時間：12小時
- 工作行事曆：9.5小時（包含1.5小時休息）
- 實際工作時數：12 - 1.5 = 10.5小時
- 標準工作時數：9.5 - 1.5 = 8小時
- 加班時數：10.5 - 8 = 2.5小時
```

##### 1.4 自動創建加班記錄
**處理方法**: [`HrAttendanceExtension._auto_create_overtime_record`](../../models/hr_attendance_extension.py:324)

**邏輯流程**:
1. 檢查是否已有關聯的加班記錄
2. 檢查是否已有相同日期的加班記錄
3. 檢查加班時數限制
4. 創建新的加班記錄
5. 建立出勤記錄與加班記錄的關聯

### 2. 加班記錄審核流程

#### 流程圖
```mermaid
graph TD
    A[加班記錄創建] --> B{來源類型}
    B -->|自動創建| C[檢查自動審核條件]
    B -->|手動創建| D[草稿狀態]
    C --> E{符合自動審核?}
    E -->|是| F[自動提交並審核]
    E -->|否| D
    D --> G[員工提交申請]
    G --> H[主管審核]
    H --> I{審核結果}
    I -->|通過| J[更新狀態為已核准]
    I -->|拒絕| K[更新狀態為已拒絕]
    J --> L[納入薪資計算]
    K --> M[記錄拒絕原因]
```

#### 詳細步驟

##### 2.1 自動審核檢查
**檢查方法**: [`TwOvertimeRecord._check_auto_approve`](../../models/overtime_management.py:422)

```python
def _check_auto_approve(self):
    """檢查是否符合自動審核條件"""
    self.ensure_one()
    
    if self.state != 'draft':
        return
        
    limit_setting = self.env['tw.overtime.limit'].get_applicable_limit_for_employee(
        self.employee_id.id, self.date
    )
    
    if not limit_setting or not limit_setting.auto_approve:
        return
        
    if self.actual_overtime_hours <= limit_setting.auto_approve_limit:
        self.state = 'submitted'
        self.action_approve()
```

**自動審核條件**:
1. 記錄狀態為草稿
2. 存在適用的限制設定
3. 限制設定啟用自動審核
4. 加班時數在自動審核限制內

##### 2.2 批次審核處理
**處理方法**: [`TwOvertimeRecord.batch_approve_records`](../../models/overtime_management.py:481)

**特色功能**:
- 原子性事務處理
- 完整的權限檢查
- 詳細的錯誤處理
- 操作日誌記錄

```python
def batch_approve_records(self):
    """批量核准加班記錄"""
    validation_result = self.batch_process_validation()
    
    if not validation_result['has_permission']:
        raise UserError(_('您沒有權限批次審核加班申請'))
    
    with self.env.cr.savepoint():
        for record in valid_records:
            try:
                record._check_monthly_limit()
                if record.attendance_id:
                    record._validate_attendance_consistency()
                
                record.write({
                    'state': 'approved',
                    'approved_by': self.env.user.id,
                    'approved_date': fields.Datetime.now()
                })
            except Exception as e:
                # 錯誤處理邏輯
                pass
```

### 3. 薪資計算流程 ✅ **[修復完成]**

#### 流程圖
```mermaid
graph TD
    A[薪資期間開始] --> B[收集員工清單]
    B --> C[收集 tw.hr.attendance 考勤記錄]
    C --> D[計算顯示/隱藏時數]
    D --> E[生成雙薪資單]
    E --> F[標準版薪資單]
    E --> G[完整版薪資單]
    F --> H[Salary Rule 計算標準加班費]
    G --> I[Salary Rule 計算完整加班費]
    H --> J[每日分段計算 - 顯示時數]
    I --> K[每日分段計算 - 實際時數]
    J --> L[生成薪資明細]
    K --> M[生成薪資明細]
    L --> N[薪資單確認]
    M --> O[薪資單確認]
    
    style C fill:#e8f5e8
    style H fill:#fff3e0
    style I fill:#ffebee
```

#### 薪資計算修復 ✅ **[最新完成]**
- ✅ 修復 compute_sheet 功能
- ✅ 完善與 tw.hr.attendance 的資料整合
- ✅ 修復加班費計算邏輯
- ✅ 修復標準版與完整版薪資單差異計算

#### 詳細步驟

##### 3.1 考勤時數收集 ✅ **[修復完成]**
**處理方法**: [`HrPayslip._update_overtime_from_records`](../../models/payslip_extension.py:228)

```python
def _update_overtime_from_records(self):
    """從 tw.hr.attendance 考勤記錄更新薪資單的加班時數"""
    self.ensure_one()
    
    # 修復：從 tw.hr.attendance 收集考勤資料
    attendance_records = self.env['tw.hr.attendance'].search([
        ('employee_id', '=', self.employee_id.id),
        ('check_in_date', '>=', self.date_from),
        ('check_in_date', '<=', self.date_to),
        ('state', 'in', ['confirmed', 'validated'])
    ])
    
    if attendance_records:
        total_displayed = sum(attendance_records.mapped('displayed_overtime_hours'))
        total_hidden = sum(attendance_records.mapped('hidden_overtime_hours'))
        
        # 直接更新資料庫避免循環依賴
        self.env.cr.execute("""
            UPDATE hr_payslip
            SET displayed_overtime_hours = %s, hidden_overtime_hours = %s
            WHERE id = %s
        """, (total_displayed, total_hidden, self.id))
```

**修復內容**:
- ✅ 修復資料來源從 tw.hr.attendance 收集
- ✅ 修復 check_in_date 欄位使用
- ✅ 完善狀態篩選邏輯

##### 3.2 雙薪資單生成
**生成方法**: [`HrPayslip.action_generate_dual_payslips`](../../models/payslip_extension.py:140)

**生成邏輯**:
1. 檢查薪資單狀態
2. 創建完整版薪資單
3. 建立配對關係
4. 設定薪資單類型

##### 3.3 每日分段加班費計算 ✅ **[修復完成]**
**計算方法**: [`HrPayslip.calculate_daily_overtime_pay`](../../models/payslip_extension.py:196)

```python
def calculate_daily_overtime_pay(self, use_total_hours=False):
    """計算每日分段加班費 - 修復版本"""
    # 按日期分組計算考勤記錄
    daily_records = {}
    attendance_records = self.env['tw.hr.attendance'].search([
        ('employee_id', '=', self.employee_id.id),
        ('check_in_date', '>=', self.date_from),
        ('check_in_date', '<=', self.date_to),
        ('state', 'in', ['confirmed', 'validated'])
    ])
    
    for record in attendance_records:
        date_key = record.check_in_date
        if date_key not in daily_records:
            daily_records[date_key] = []
        daily_records[date_key].append(record)
    
    # 對每一天的加班時數進行分段計算
    total_overtime_pay = 0.0
    for date_key, records in daily_records.items():
        # 計算當日總加班時數
        if use_total_hours:
            daily_hours = sum(record.overtime_hours for record in records)
        else:
            daily_hours = sum(record.displayed_overtime_hours for record in records)
        
        # 每日分段計算：前2小時1.34倍，超過2小時的部分1.67倍
        hourly_rate = self.employee_id.contract_id.wage / 30 / 8  # 時薪計算
        if daily_hours <= 2:
            daily_pay = daily_hours * hourly_rate * 1.34
        else:
            daily_pay = 2 * hourly_rate * 1.34 + (daily_hours - 2) * hourly_rate * 1.67
        
        total_overtime_pay += daily_pay
    
    return total_overtime_pay
```

**修復內容**:
- ✅ 修復資料來源為 tw.hr.attendance
- ✅ 修復 check_in_date 欄位使用
- ✅ 完善每日分段計算邏輯
- ✅ 修復時薪計算公式

**Salary Rule 整合**: 加班費計算已轉移至 [`data/payroll_structure_data.xml`](../../data/payroll_structure_data.xml)

```xml
<!-- 標準加班費規則 -->
<field name="amount_python_compute"><![CDATA[
# 標準加班費計算 - 使用每日分段計算方法
try:
    result = payslip.calculate_daily_overtime_pay(use_total_hours=False)
except:
    # 備用計算邏輯
    # ...
]]></field>

<!-- 完整加班費規則 -->
<field name="amount_python_compute"><![CDATA[
# 完整版薪資單使用每日分段計算方法（使用總時數）
try:
    result = payslip.calculate_daily_overtime_pay(use_total_hours=True)
except:
    # 備用計算邏輯
    # ...
]]></field>
```

### 4. 假日和補班日管理流程

#### 流程圖
```mermaid
graph TD
    A[假日資料匯入] --> B[檔案格式檢查]
    B --> C[資料驗證]
    C --> D[創建假日記錄]
    D --> E{是否有補班日?}
    E -->|是| F[創建補班日覆蓋]
    E -->|否| G[完成匯入]
    F --> H[確認補班日設定]
    H --> I[更新工作行事曆]
    I --> G
```

#### 詳細步驟

##### 4.1 假日匯入處理
**處理類別**: [`TwHolidayImport`](../../models/holiday_calendar.py:11)

**匯入流程**:
1. 檔案格式檢查 (CSV/Excel)
2. 必要欄位驗證
3. 日期格式解析
4. 重複記錄檢查
5. 假日記錄創建
6. 補班日處理

##### 4.2 補班日處理
**處理方法**: [`TwHolidayImport._process_makeup_day`](../../models/holiday_calendar.py:169)

```python
def _process_makeup_day(self, makeup_date, holiday_name):
    """處理補班日"""
    weekday = makeup_date.weekday()
    
    if weekday == 5:  # Saturday
        self._create_saturday_working_day(makeup_date, holiday_name)

def _create_saturday_working_day(self, saturday_date, reason):
    """為週六創建工作日"""
    self.env['tw.working.calendar.override'].create({
        'calendar_id': self.calendar_id.id,
        'date': saturday_date,
        'is_working_day': True,
        'reason': f"因 {reason} 補班",
        'working_hours': 8.0,
    })
```

### 5. 權限控制流程

#### 流程圖
```mermaid
graph TD
    A[使用者操作請求] --> B[檢查使用者群組]
    B --> C{是否有完整權限?}
    C -->|是| D[顯示完整資訊]
    C -->|否| E[檢查限制設定]
    E --> F[計算可顯示時數]
    F --> G[隱藏超額時數]
    G --> H[返回限制後資訊]
    D --> I[執行操作]
    H --> I
```

#### 權限群組定義

##### 5.1 權限群組架構
**定義檔案**: [`security/tw_payroll_groups.xml`](../../security/tw_payroll_groups.xml)

```xml
<!-- 基礎使用者群組 -->
<record id="group_tw_payroll_user" model="res.groups">
    <field name="name">台灣薪資使用者</field>
    <field name="category_id" ref="base.module_category_human_resources"/>
</record>

<!-- 管理員群組 -->
<record id="group_tw_payroll_manager" model="res.groups">
    <field name="name">台灣薪資管理員</field>
    <field name="implied_ids" eval="[(4, ref('group_tw_payroll_user'))]"/>
</record>

<!-- 完整權限群組 -->
<record id="group_overtime_full_access" model="res.groups">
    <field name="name">加班時數完整存取</field>
    <field name="implied_ids" eval="[(4, ref('group_tw_payroll_manager'))]"/>
</record>
```

##### 5.2 權限檢查邏輯
**檢查方法**: [`HrEmployee._compute_permissions`](../../models/hr_employee_extension.py:231)

```python
def _compute_permissions(self):
    """計算權限"""
    for employee in self:
        user = self.env.user
        
        employee.can_view_full_overtime = (
            user.has_group('l10n_tw_hr_payroll.group_overtime_full_access') or
            user.has_group('l10n_tw_hr_payroll.group_tw_payroll_super_admin') or
            user.has_group('l10n_tw_hr_payroll.group_attendance_integration_manager')
        )
        
        employee.can_approve_overtime = (
            user.has_group('l10n_tw_hr_payroll.group_tw_payroll_manager') or
            user.has_group('l10n_tw_hr_payroll.group_tw_payroll_super_admin') or
            user.has_group('l10n_tw_hr_payroll.group_department_supervisor') or
            user.has_group('l10n_tw_hr_payroll.group_attendance_integration_manager')
        )
```

### 6. 定時任務流程

#### 6.1 出勤記錄處理任務
**任務方法**: [`HrAttendanceExtension._cron_process_unprocessed_attendances`](../../models/hr_attendance_extension.py:437)

**執行頻率**: 每日執行
**處理邏輯**:
1. 查找昨天及之前未處理的出勤記錄
2. 逐一處理下班打卡邏輯
3. 自動創建加班記錄
4. 記錄處理結果

#### 6.2 加班記錄自動創建任務
**任務方法**: [`TwOvertimeRecord._cron_auto_create_from_attendance`](../../models/overtime_management.py:777)

**執行頻率**: 每日執行
**處理邏輯**:
1. 查找已完成但未處理的出勤記錄
2. 檢查是否已有手動創建的記錄
3. 自動創建加班記錄
4. 統計處理結果

### 7. 資料同步流程

#### 7.1 出勤與加班記錄同步
**同步方法**: [`TwOvertimeRecord.sync_with_attendance`](../../models/overtime_management.py:244)

```python
def sync_with_attendance(self):
    """同步打卡資料"""
    self.ensure_one()
    
    if not self.attendance_id:
        raise UserError(_('此加班記錄沒有關聯的出勤記錄'))
    
    attendance = self.attendance_id
    
    vals = {
        'actual_overtime_hours': attendance.tw_overtime_hours,
        'overtime_type': attendance.tw_overtime_type,
        'date': attendance.check_in.date(),
    }
    
    self.write(vals)
```

#### 7.2 薪資單與加班記錄同步
**同步方法**: [`HrPayslipOvertimeExtension.update_overtime_from_records`](../../models/overtime_management.py:928)

**同步時機**:
- 薪資單計算時
- 加班記錄狀態變更時
- 手動觸發同步時

### 8. 錯誤處理和恢復流程

#### 8.1 資料一致性檢查
**檢查方法**: [`TwOvertimeRecord._validate_attendance_consistency`](../../models/overtime_management.py:213)

**檢查項目**:
1. 日期一致性
2. 員工一致性
3. 加班時數合理性

#### 8.2 批次操作錯誤處理
**處理策略**:
- 使用資料庫事務確保原子性
- 詳細記錄錯誤訊息
- 部分成功時提供明確回饋
- 支援操作回滾

#### 8.3 定時任務錯誤恢復
**恢復機制**:
- 記錄處理失敗的記錄
- 下次執行時重新處理
- 提供手動修復工具
- 監控和告警機制

## 效能優化策略

### 1. 資料庫查詢優化
- 使用適當的索引
- 批次查詢減少往返
- 避免 N+1 查詢問題

### 2. 計算欄位優化
- 使用 `store=True` 儲存結果
- 避免循環依賴
- 合理使用快取

### 3. 批次處理優化
- 使用事務批次處理
- 分批處理大量資料
- 提供進度回饋

## 監控和日誌

### 1. 操作日誌
- 記錄關鍵業務操作
- 包含操作人和時間
- 支援審計追蹤

### 2. 效能監控
- 監控查詢執行時間
- 追蹤批次操作效能
- 定時任務執行狀況

### 3. 錯誤追蹤
- 詳細的錯誤堆疊
- 業務邏輯錯誤分類
- 自動告警機制

## 薪資計算流程修復記錄 ✅ **[最新完成]**

### 修復內容
1. **compute_sheet 功能修復**
   - 修復薪資單計算邏輯錯誤
   - 完善與 tw.hr.attendance 的資料整合
   - 修復 check_in_date 欄位相關問題

2. **加班費計算流程改進**
   - 修復每日分段加班費計算
   - 完善前2小時1.34倍，後續1.67倍的費率計算
   - 修復標準版與完整版薪資單的差異計算

3. **資料流程優化**
   - 優化考勤資料收集流程
   - 完善權限控制與薪資顯示邏輯
   - 修復資料一致性問題

### 待測試流程
1. **薪資單計算流程測試**
   - 測試 compute_sheet 功能
   - 驗證加班費計算準確性
   - 檢查標準版與完整版差異

2. **整合流程測試**
   - 測試考勤到薪資的完整流程
   - 驗證權限控制在薪資計算中的表現
   - 檢查資料同步和一致性

### 測試建議
- 建議先測試基本的薪資單計算功能
- 重點驗證加班費計算邏輯的準確性
- 確認不同權限用戶的薪資單計算差異