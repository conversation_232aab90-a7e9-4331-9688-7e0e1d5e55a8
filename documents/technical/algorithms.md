# 算法文檔

## 概述
本文檔詳細說明 l10n_tw_hr_payroll 模組中的核心算法，包括薪資計算、加班計算、時數分配等關鍵業務邏輯。

## 核心算法架構

### 算法分類
```
算法類型
├── 時間計算算法
│   ├── 工作時數計算
│   ├── 加班時數計算
│   └── 時區轉換算法
├── 薪資計算算法
│   ├── 基本薪資計算
│   ├── 加班費計算
│   └── 法定扣除計算
├── 權限控制算法
│   ├── 時數顯示算法
│   ├── 權限檢查算法
│   └── 資料過濾算法
└── 業務邏輯算法
    ├── 審核流程算法
    ├── 限制檢查算法
    └── 資料同步算法
```

## 1. 時間計算算法

### 1.1 多段式打卡加班時數計算算法

#### 算法位置
**檔案**: [`models/hr_attendance_extension.py`](../../models/hr_attendance_extension.py:330)
**方法**: `_compute_tw_overtime_hours`

#### 算法邏輯
```python
@api.depends('check_in', 'check_out', 'employee_id', 'expected_working_hours', 'break_hours', 'tw_overtime_type')
def _compute_tw_overtime_hours(self):
    """計算台灣加班時數 - 支援多段式打卡制度"""
    for record in self:
        if not record.check_out or not record.employee_id or not record.check_in:
            record.tw_overtime_hours = 0.0
            continue
        
        # 取得同一天同一員工的所有完整出勤記錄（有 check_in 和 check_out）
        check_in_date = record.check_in.date()
        same_day_records = self.search([
            ('employee_id', '=', record.employee_id.id),
            ('check_in', '>=', check_in_date),
            ('check_in', '<', check_in_date + timedelta(days=1)),
            ('check_out', '!=', False)
        ])
        
        if not same_day_records:
            record.tw_overtime_hours = 0.0
            continue
        
        # 計算當天總工作時數（所有時段的 worked_hours 總和）
        total_worked_hours = sum(rec.worked_hours for rec in same_day_records if rec.worked_hours)
        
        # 取得標準工作時數和休息時數（使用當前記錄的設定）
        expected_hours = record.expected_working_hours or 8.0
        break_hours = record.break_hours or 1.0
        standard_work_hours = expected_hours - break_hours
        
        # 計算加班時數
        overtime_hours = max(0.0, total_worked_hours - standard_work_hours)
        
        # 只在當天最後一筆記錄顯示加班時數，避免重複計算
        last_record = same_day_records.sorted('check_out')[-1]
        if record.id == last_record.id:
            record.tw_overtime_hours = overtime_hours
        else:
            record.tw_overtime_hours = 0.0
```

#### 多段式打卡算法公式
```
當天總工作時數 = SUM(所有時段的 worked_hours)

加班時數 = MAX(0, 當天總工作時數 - 標準工作時數)

顯示規則：
- 只在當天最後一筆記錄顯示加班時數
- 其他記錄的加班時數 = 0
- 所有記錄關聯到同一個加班記錄

其中：
- 當天總工作時數 = 時段1工作時數 + 時段2工作時數 + ... + 時段N工作時數
- 標準工作時數 = 基本工作時數 - 休息時間
- 基本工作時數 = 根據工作行事曆設定 (通常為8小時)
- 休息時間 = 從工作行事曆動態計算所有 break 時間總和
```

#### 多段式打卡場景示例
```
場景：員工一天內多次打卡
08:00-12:00  上午工作 (4小時)
13:00-17:00  下午工作 (4小時)
18:00-21:00  加班工作 (3小時)

計算過程：
1. 當天總工作時數 = 4 + 4 + 3 = 11小時
2. 標準工作時數 = 9 - 1 = 8小時 (假設9小時預期工作時數，1小時休息)
3. 加班時數 = 11 - 8 = 3小時
4. 顯示：只在21:00的記錄顯示3小時加班，其他記錄顯示0小時
5. 關聯：三筆記錄都關聯到同一個加班記錄
```

#### 算法特點
1. **多段式支援**: 支援一天內多次打卡，正確累計總工作時數
2. **避免重複**: 只在最後一筆記錄顯示加班時數，避免重複計算
3. **統一關聯**: 所有當天記錄關聯到同一個加班記錄
4. **容錯性**: 使用 `max(0.0, overtime_hours)` 確保不會產生負數
5. **動態計算**: 從工作行事曆動態計算所有休息時間
6. **精確計算**: 考慮實際的工作行事曆設定和所有休息時段

#### 算法複雜度
- **時間複雜度**: O(n) - n為當天出勤記錄數量
- **空間複雜度**: O(1) - 只使用常數空間

### 1.2 工作日判斷算法

#### 算法位置
**檔案**: [`models/working_calendar.py`](../../models/working_calendar.py:244)
**方法**: `is_working_day`

#### 算法邏輯
```python
def is_working_day(self, date):
    """檢查指定日期是否為工作日"""
    self.ensure_one()
    
    # 1. 檢查覆蓋設定（最高優先級）
    override = self.env['tw.working.calendar.override'].search([
        ('calendar_id', '=', self.id),
        ('date', '=', date),
        ('state', '=', 'confirmed')
    ], limit=1)
    
    if override:
        return override.is_working_day
    
    # 2. 檢查是否為假日
    holiday = self.env['resource.calendar.leaves'].search([
        ('calendar_id', '=', self.id),
        ('date_from', '<=', date),
        ('date_to', '>=', date)
    ], limit=1)
    
    if holiday:
        return False
    
    # 3. 檢查是否為正常工作日
    weekday = date.weekday()  # 0=Monday, 6=Sunday
    attendances = self.attendance_ids.filtered(lambda a: int(a.dayofweek) == weekday)
    
    return bool(attendances)
```

#### 算法決策樹
```mermaid
graph TD
    A[輸入日期] --> B{是否有覆蓋設定?}
    B -->|是| C[返回覆蓋設定結果]
    B -->|否| D{是否為假日?}
    D -->|是| E[返回 False]
    D -->|否| F{週幾?}
    F --> G[檢查該日是否有工作時間設定]
    G --> H[返回檢查結果]
```

#### 優先級順序
1. **覆蓋設定** (最高優先級) - 補班日、特殊工作日
2. **假日設定** - 國定假日、公司假日
3. **標準工作日** - 根據工作行事曆的週間設定

### 1.3 時區轉換算法

#### 算法位置
**檔案**: [`models/hr_attendance_extension.py`](../../models/hr_attendance_extension.py:140)
**方法**: `_compute_local_times`

#### 算法邏輯
```python
@api.depends('check_in', 'check_out', 'employee_id')
def _compute_local_times(self):
    """計算本地時間"""
    for record in self:
        if not record.employee_id:
            record.check_in_local = record.check_in
            record.check_out_local = record.check_out
            continue
            
        # 取得員工時區，預設為台北時區
        tz_name = record.employee_id.tz or 'Asia/Taipei'
        user_tz = pytz.timezone(tz_name)
        
        if record.check_in:
            utc_check_in = pytz.utc.localize(record.check_in)
            record.check_in_local = utc_check_in.astimezone(user_tz).replace(tzinfo=None)
        else:
            record.check_in_local = False
            
        if record.check_out:
            utc_check_out = pytz.utc.localize(record.check_out)
            record.check_out_local = utc_check_out.astimezone(user_tz).replace(tzinfo=None)
        else:
            record.check_out_local = False
```

#### 時區轉換流程
```
UTC 時間 → 本地時區 → 移除時區資訊
    ↓           ↓            ↓
儲存在DB    顯示給使用者    計算使用
```

## 2. 薪資計算算法

### 2.1 多段式打卡薪資整合算法

#### 算法位置
**檔案**: [`models/payslip_extension.py`](../../models/payslip_extension.py:234)
**方法**: `_update_overtime_from_records`

#### 薪資更新算法邏輯
```python
def _update_overtime_from_records(self):
    """從加班記錄更新薪資單的加班時數 - 支援多段式打卡並正確應用限制"""
    self.ensure_one()
    
    if not self.employee_id or not self.date_from or not self.date_to:
        return
    
    # 取得期間內的加班記錄 - 包含草稿、已提交和已核准的記錄
    overtime_records = self.env['tw.hr.attendance (原 tw.overtime.record 已移除)'].search([
        ('employee_id', '=', self.employee_id.id),
        ('date', '>=', self.date_from),
        ('date', '<=', self.date_to),
        ('state', 'in', ['draft', 'submitted', 'approved'])
    ])
    
    if overtime_records:
        # 重新計算顯示和隱藏時數，考慮薪資單類型和用戶權限
        total_actual = sum(overtime_records.mapped('actual_overtime_hours'))
        
        # 檢查用戶權限和薪資單類型
        has_full_access = (
            self.env.user.has_group('l10n_tw_hr_payroll.group_overtime_full_access') or
            self.env.user.has_group('l10n_tw_hr_payroll.group_tw_payroll_super_admin') or
            self.env.user.has_group('l10n_tw_hr_payroll.group_attendance_integration_manager')
        )
        
        if self.payslip_type == 'full' and has_full_access:
            # 完整版薪資單且有權限：顯示全部時數
            total_displayed = total_actual
            total_hidden = 0.0
        elif self.payslip_type == 'standard' or not has_full_access:
            # 標準版薪資單或無完整權限：應用顯示限制
            total_displayed = sum(overtime_records.mapped('displayed_overtime_hours'))
            total_hidden = sum(overtime_records.mapped('hidden_overtime_hours'))
        else:
            # 其他情況：使用加班記錄的原始計算
            total_displayed = sum(overtime_records.mapped('displayed_overtime_hours'))
            total_hidden = sum(overtime_records.mapped('hidden_overtime_hours'))
        
        # 直接更新欄位值，避免觸發 compute 方法
        self.env.cr.execute("""
            UPDATE hr_payslip
            SET displayed_overtime_hours = %s, hidden_overtime_hours = %s
            WHERE id = %s
        """, (total_displayed, total_hidden, self.id))
        
        # 清除快取以確保下次讀取時獲得正確值
        self.invalidate_model(['displayed_overtime_hours', 'hidden_overtime_hours', 'total_overtime_hours'])
```

### 2.2 每日分段加班費計算算法

#### 算法位置
**檔案**: [`models/payslip_extension.py`](../../models/payslip_extension.py:196)
**方法**: `calculate_daily_overtime_pay`

#### 算法邏輯
```python
def calculate_daily_overtime_pay(self, use_total_hours=False):
    """計算每日分段加班費
    
    Args:
        use_total_hours (bool): 是否使用總時數（完整版薪資單）
        
    Returns:
        float: 計算後的加班費總額
    """
    self.ensure_one()
    
    # 取得期間內的加班記錄
    overtime_records = self.env['tw.hr.attendance (原 tw.overtime.record 已移除)'].search([
        ('employee_id', '=', self.employee_id.id),
        ('date', '>=', self.date_from),
        ('date', '<=', self.date_to),
        ('state', 'in', ['approved', 'submitted'])
    ])
    
    if not overtime_records:
        return 0.0
    
    # 計算時薪
    hourly_rate = self.contract_id.wage / 30 / 8  # 月薪轉時薪
    total_overtime_pay = 0.0
    
    # 按日期分組計算
    daily_records = {}
    for record in overtime_records:
        date_key = record.date
        if date_key not in daily_records:
            daily_records[date_key] = []
        daily_records[date_key].append(record)
    
    # 對每一天的加班時數進行分段計算
    for date_key, records in daily_records.items():
        # 計算當日總加班時數
        if use_total_hours:
            # 完整版：使用實際時數
            daily_hours = sum(record.actual_overtime_hours for record in records)
        else:
            # 標準版：使用顯示時數
            daily_hours = sum(record.displayed_overtime_hours for record in records)
        
        if daily_hours <= 0:
            continue
        
        # 每日分段計算：前2小時1.34倍，超過2小時的部分1.67倍
        if daily_hours <= 2:
            daily_pay = daily_hours * hourly_rate * 1.34
        else:
            daily_pay = 2 * hourly_rate * 1.34 + (daily_hours - 2) * hourly_rate * 1.67
        
        total_overtime_pay += daily_pay
    
    return total_overtime_pay
```

#### 薪資單類型處理邏輯
```mermaid
graph TD
    A[薪資計算開始] --> B[從加班記錄更新時數]
    B --> C{薪資單類型?}
    C -->|標準版| D[應用 display_limit 限制]
    C -->|完整版| E{用戶有完整權限?}
    E -->|是| F[顯示全部時數]
    E -->|否| D
    D --> G[計算受限制的加班費]
    F --> H[計算完整加班費]
    G --> I[更新薪資明細行]
    H --> I
```

#### 每日分段加班費計算公式
```
時薪 = 月薪 ÷ 30天 ÷ 8小時

每日加班費計算：
- 當日加班時數 ≤ 2小時：
  加班費 = 當日加班時數 × 時薪 × 1.34

- 當日加班時數 > 2小時：
  加班費 = 2 × 時薪 × 1.34 + (當日加班時數 - 2) × 時薪 × 1.67

總加班費 = SUM(每日加班費)

其中加班倍率根據台灣勞基法：
- 平日前2小時: 1.34倍
- 平日2小時後: 1.67倍
- 假日: 2.0倍
- 國定假日: 2.0倍

重要特點：
- 每日獨立計算分段，避免跨日累計造成的不公平
- 支援標準版（顯示時數）和完整版（實際時數）計算
- 由 salary rule 調用，確保薪資計算的一致性
```

### 2.2 法定扣除計算算法

#### 勞保費計算
```python
def calculate_labor_insurance(self, monthly_wage):
    """計算勞保費"""
    # 勞保投保薪資上限
    max_insurable_salary = 45800
    
    # 投保薪資 = MIN(月薪, 投保上限)
    insurable_salary = min(monthly_wage, max_insurable_salary)
    
    # 勞保費率 (2024年為11.5%)
    labor_insurance_rate = 0.115
    
    # 員工負擔比例 (20%)
    employee_ratio = 0.2
    
    # 勞保費 = 投保薪資 × 費率 × 員工負擔比例
    labor_insurance_fee = insurable_salary * labor_insurance_rate * employee_ratio
    
    return labor_insurance_fee
```

#### 健保費計算
```python
def calculate_health_insurance(self, monthly_wage):
    """計算健保費"""
    # 健保投保薪資上限
    max_insurable_salary = 182000
    
    # 投保薪資
    insurable_salary = min(monthly_wage, max_insurable_salary)
    
    # 健保費率 (2024年為5.17%)
    health_insurance_rate = 0.0517
    
    # 員工負擔比例 (30%)
    employee_ratio = 0.3
    
    # 健保費 = 投保薪資 × 費率 × 員工負擔比例
    health_insurance_fee = insurable_salary * health_insurance_rate * employee_ratio
    
    return health_insurance_fee
```

#### 勞退金計算
```python
def calculate_pension_fund(self, monthly_wage):
    """計算勞退金"""
    # 勞退月提繳工資上限
    max_pension_salary = 150000
    
    # 提繳工資
    pension_salary = min(monthly_wage, max_pension_salary)
    
    # 員工自提比例 (0-6%，預設6%)
    employee_contribution_rate = 0.06
    
    # 勞退金 = 提繳工資 × 自提比例
    pension_fund = pension_salary * employee_contribution_rate
    
    return pension_fund
```

## 3. 權限控制算法

### 3.1 時數顯示算法

#### 算法位置
**檔案**: [`models/overtime_management.py`](../../models/overtime_management.py:168)
**方法**: `_compute_displayed_hours`

#### 算法邏輯
```python
@api.depends('actual_overtime_hours', 'employee_id', 'date', 'auto_created')
def _compute_displayed_hours(self):
    for record in self:
        if not record.employee_id or not record.date:
            record.displayed_overtime_hours = record.actual_overtime_hours
            record.hidden_overtime_hours = 0.0
            continue
        
        # 取得該員工的限制設定
        limit_setting = self.env['tw.overtime.limit'].get_applicable_limit_for_employee(
            record.employee_id.id, record.date
        )
        
        if not limit_setting:
            record.displayed_overtime_hours = record.actual_overtime_hours
            record.hidden_overtime_hours = 0.0
            continue
        
        # 計算該月已累計的顯示時數
        month_start = record.date.replace(day=1)
        
        # 計算月末日期
        if month_start.month == 12:
            next_month_start = month_start.replace(year=month_start.year + 1, month=1, day=1)
        else:
            next_month_start = month_start.replace(month=month_start.month + 1, day=1)
        month_end = next_month_start - timedelta(days=1)
        
        existing_displayed_hours = sum(
            self.search([
                ('employee_id', '=', record.employee_id.id),
                ('date', '>=', month_start),
                ('date', '<=', month_end),
                ('id', '!=', record.id),
                ('state', 'in', ['approved', 'submitted'])
            ]).mapped('displayed_overtime_hours')
        )
        
        # 計算可顯示的時數
        remaining_display_quota = limit_setting.display_limit - existing_displayed_hours
        displayable_hours = min(record.actual_overtime_hours, max(0, remaining_display_quota))
        
        record.displayed_overtime_hours = displayable_hours
        record.hidden_overtime_hours = record.actual_overtime_hours - displayable_hours
```

#### 時數分配算法流程
```mermaid
graph TD
    A[實際加班時數] --> B{是否有限制設定?}
    B -->|否| C[全部顯示]
    B -->|是| D[計算月累計顯示時數]
    D --> E[計算剩餘顯示配額]
    E --> F{配額是否足夠?}
    F -->|是| G[全部顯示]
    F -->|否| H[部分顯示，其餘隱藏]
    G --> I[隱藏時數 = 0]
    H --> J[隱藏時數 = 實際時數 - 顯示時數]
```

#### 算法公式
```
剩餘顯示配額 = 月度顯示限制 - 本月已顯示時數

顯示時數 = MIN(實際加班時數, MAX(0, 剩餘顯示配額))

隱藏時數 = 實際加班時數 - 顯示時數
```

### 3.2 權限檢查算法

#### 算法位置
**檔案**: [`models/hr_employee_extension.py`](../../models/hr_employee_extension.py:231)
**方法**: `_compute_permissions`

#### 算法邏輯
```python
def _compute_permissions(self):
    """計算權限"""
    for employee in self:
        user = self.env.user
        
        # 檢查是否可查看完整加班時數
        employee.can_view_full_overtime = (
            user.has_group('l10n_tw_hr_payroll.group_overtime_full_access') or
            user.has_group('l10n_tw_hr_payroll.group_tw_payroll_super_admin') or
            user.has_group('l10n_tw_hr_payroll.group_attendance_integration_manager')
        )
        
        # 檢查是否可核准加班
        employee.can_approve_overtime = (
            user.has_group('l10n_tw_hr_payroll.group_tw_payroll_manager') or
            user.has_group('l10n_tw_hr_payroll.group_tw_payroll_super_admin') or
            user.has_group('l10n_tw_hr_payroll.group_department_supervisor') or
            user.has_group('l10n_tw_hr_payroll.group_attendance_integration_manager')
        )
```

#### 權限檢查矩陣
```
權限類型 = OR(群組1, 群組2, ..., 群組N)

完整時數查看權限 = OR(
    完整權限存取群組,
    超級管理員群組,
    出勤整合管理員群組
)

加班審核權限 = OR(
    薪資管理員群組,
    超級管理員群組,
    部門主管群組,
    出勤整合管理員群組
)
```

## 4. 業務邏輯算法

### 4.1 限制檢查算法

#### 算法位置
**檔案**: [`models/overtime_management.py`](../../models/overtime_management.py:50)
**方法**: `get_applicable_limit_for_employee`

#### 算法邏輯
```python
def get_applicable_limit_for_employee(self, employee_id, date=None):
    """取得適用於指定員工的加班限制"""
    if date is None:
        date = fields.Date.today()
    
    domain = [
        ('active', '=', True),
        '|', ('date_from', '<=', date), ('date_from', '=', False),
        '|', ('date_to', '>=', date), ('date_to', '=', False),
    ]
    
    # 優先查找特定員工設定
    employee_limits = self.search(domain + [('employee_ids', 'in', [employee_id])])
    if employee_limits:
        return employee_limits[0]
    
    # 查找部門設定
    employee = self.env['hr.employee'].browse(employee_id)
    if employee.department_id:
        dept_limits = self.search(domain + [('department_ids', 'in', [employee.department_id.id])])
        if dept_limits:
            return dept_limits[0]
    
    # 查找全公司設定
    company_limits = self.search(domain + [('apply_to_all', '=', True)])
    if company_limits:
        return company_limits[0]
    
    return False
```

#### 限制查找優先級
```mermaid
graph TD
    A[開始查找] --> B[檢查特定員工設定]
    B --> C{找到?}
    C -->|是| D[返回員工設定]
    C -->|否| E[檢查部門設定]
    E --> F{找到?}
    F -->|是| G[返回部門設定]
    F -->|否| H[檢查全公司設定]
    H --> I{找到?}
    I -->|是| J[返回公司設定]
    I -->|否| K[返回無限制]
```

#### 優先級順序
1. **特定員工設定** (最高優先級)
2. **部門設定**
3. **全公司設定** (最低優先級)

### 4.2 自動審核算法

#### 算法位置
**檔案**: [`models/overtime_management.py`](../../models/overtime_management.py:422)
**方法**: `_check_auto_approve`

#### 算法邏輯
```python
def _check_auto_approve(self):
    """檢查是否符合自動審核條件"""
    self.ensure_one()
    
    if self.state != 'draft':
        return
        
    # 取得該員工的限制設定
    limit_setting = self.env['tw.overtime.limit'].get_applicable_limit_for_employee(
        self.employee_id.id, self.date
    )
    
    if not limit_setting or not limit_setting.auto_approve:
        return
        
    # 檢查是否符合自動審核條件
    if self.actual_overtime_hours <= limit_setting.auto_approve_limit:
        self.state = 'submitted'
        self.action_approve()
        _logger.info(f"加班記錄 {self.id} 符合自動審核條件，已自動審核通過")
```

#### 自動審核決策樹
```mermaid
graph TD
    A[加班記錄創建] --> B{狀態是草稿?}
    B -->|否| C[結束]
    B -->|是| D{有限制設定?}
    D -->|否| C
    D -->|是| E{啟用自動審核?}
    E -->|否| C
    E -->|是| F{時數 <= 自動審核限制?}
    F -->|否| C
    F -->|是| G[自動提交並審核]
```

#### 自動審核條件
```
自動審核 = AND(
    記錄狀態 == '草稿',
    存在限制設定,
    限制設定.啟用自動審核 == True,
    實際加班時數 <= 限制設定.自動審核時數上限
)
```

## 5. 效能優化算法

### 5.1 批次處理算法

#### 算法位置
**檔案**: [`models/overtime_management.py`](../../models/overtime_management.py:481)
**方法**: `batch_approve_records`

#### 算法特點
1. **原子性事務**: 使用 `savepoint()` 確保批次操作的原子性
2. **錯誤隔離**: 單一記錄錯誤不影響其他記錄處理
3. **權限預檢**: 批次處理前統一檢查權限
4. **進度追蹤**: 詳細記錄處理結果和錯誤訊息

#### 批次處理流程
```python
def batch_approve_records(self):
    """批量核准加班記錄"""
    # 1. 權限和狀態驗證
    validation_result = self.batch_process_validation()
    
    if not validation_result['has_permission']:
        raise UserError(_('您沒有權限批次審核加班申請'))
    
    valid_records = validation_result['valid_records']
    
    # 2. 使用事務確保原子性
    approved_count = 0
    error_messages = []
    
    with self.env.cr.savepoint():
        try:
            for record in valid_records:
                try:
                    # 3. 個別記錄處理
                    record._check_monthly_limit()
                    if record.attendance_id:
                        record._validate_attendance_consistency()
                    
                    record.write({
                        'state': 'approved',
                        'approved_by': self.env.user.id,
                        'approved_date': fields.Datetime.now()
                    })
                    
                    approved_count += 1
                    
                except Exception as e:
                    error_messages.append(f"記錄 {record.id}: {str(e)}")
                    
        except Exception as e:
            _logger.error(f"批次審核事務失敗: {str(e)}")
            raise
    
    # 4. 返回處理結果
    return self._prepare_batch_result(approved_count, error_messages)
```

### 5.2 快取優化算法

#### 計算欄位快取策略
```python
@api.depends('displayed_overtime_hours', 'hidden_overtime_hours')
def _compute_total_overtime(self):
    """計算總加班時數 - 使用快取優化"""
    for payslip in self:
        # 檢查快取
        cache_key = f"total_overtime_{payslip.id}_{payslip.write_date}"
        cached_value = self.env.cache.get(cache_key)
        
        if cached_value is not None:
            payslip.total_overtime_hours = cached_value
            continue
        
        # 計算並快取結果
        total = payslip.displayed_overtime_hours + payslip.hidden_overtime_hours
        self.env.cache.set(cache_key, total)
        payslip.total_overtime_hours = total
```

### 5.3 查詢優化算法

#### 索引建議算法
```python
def suggest_database_indexes(self):
    """建議資料庫索引"""
    suggestions = []
    
    # 分析查詢模式
    common_queries = [
        ('tw_overtime_record', ['employee_id', 'date']),
        ('tw_overtime_record', ['state', 'date']),
        ('hr_attendance', ['employee_id', 'check_in']),
        ('hr_payslip', ['employee_id', 'date_from', 'date_to']),
    ]
    
    for table, columns in common_queries:
        index_name = f"idx_{table}_{'_'.join(columns)}"
        suggestions.append({
            'table': table,
            'columns': columns,
            'index_name': index_name,
            'sql': f"CREATE INDEX {index_name} ON {table}({', '.join(columns)});"
        })
    
    return suggestions
```

## 6. 算法測試和驗證

### 6.1 單元測試算法
```python
def test_overtime_calculation_algorithm(self):
    """測試加班時數計算算法"""
    # 測試案例 1: 正常加班
    attendance = self.create_test_attendance(
        check_in='2024-01-01 09:00:00',
        check_out='2024-01-01 19:00:00',  # 在公司12小時
        expected_working_hours=9.5,  # 工作行事曆設定（含休息時間）
        break_hours=1.5  # 動態計算的休息時間
    )
    
    # 實際工作時數 = 12 - 1.5 = 10.5小時
    # 標準工作時數 = 9.5 - 1.5 = 8小時
    # 加班時數 = 10.5 - 8 = 2.5小時
    expected_overtime = 2.5
    self.assertEqual(attendance.tw_overtime_hours, expected_overtime)
    
    # 測試案例 2: 無加班
    attendance2 = self.create_test_attendance(
        check_in='2024-01-01 09:00:00',
        check_out='2024-01-01 17:30:00',  # 在公司8.5小時
        expected_working_hours=8.0,
        break_hours=1.0
    )
    
    # 實際工作時數 = 8.5 - 1.0 = 7.5小時
    # 標準工作時數 = 8.0 - 1.0 = 7小時
    # 加班時數 = 7.5 - 7 = 0.5小時
    expected_overtime2 = 0.5
    self.assertEqual(attendance2.tw_overtime_hours, expected_overtime2)
    
    # 測試案例 3: 邊界條件 - 工作時數不足
    attendance3 = self.create_test_attendance(
        check_in='2024-01-01 09:00:00',
        check_out='2024-01-01 16:00:00',  # 在公司7小時
        expected_working_hours=8.0,
        break_hours=1.0
    )
    
    # 實際工作時數 = 7 - 1.0 = 6小時
    # 標準工作時數 = 8.0 - 1.0 = 7小時
    # 加班時數 = 6 - 7 = -1 -> MAX(0, -1) = 0
    expected_overtime3 = 0.0
    self.assertEqual(attendance3.tw_overtime_hours, expected_overtime3)
```

### 6.2 效能測試算法
```python
def test_algorithm_performance(self):
    """測試算法效能"""
    import time
    
    # 創建大量測試資料
    test_records = self.create_test_overtime_records(1000)
    
    # 測試批次處理效能
    start_time = time.time()
    test_records.batch_approve_records()
    end_time = time.time()
    
    processing_time = end_time - start_time
    records_per_second = len(test_records) / processing_time
    
    # 效能基準：每秒至少處理100筆記錄
    self.assertGreater(records_per_second, 100)
    
    # 測試記憶體使用
    import psutil
    process = psutil.Process()
    memory_usage = process.memory_info().rss / 1024 / 1024  # MB
    
    # 記憶體使用基準：不超過100MB
    self.assertLess(memory_usage, 100)
```

## 7. 算法優化建議

### 7.1 時間複雜度優化
1. **批次查詢**: 使用 `search_read()` 減少資料庫往返
2. **索引優化**: 為常用查詢欄位建立