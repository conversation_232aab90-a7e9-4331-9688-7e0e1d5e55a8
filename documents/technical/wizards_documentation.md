# 嚮導文檔

## 概述
本文檔詳細說明 l10n_tw_hr_payroll 模組中所有嚮導（Wizard）的功能、使用方式和實作細節。

## 嚮導架構概覽

### 嚮導檔案結構
```
wizards/
├── __init__.py                      # 嚮導模組初始化
├── holiday_import_wizard.py         # 假日匯入嚮導
├── overtime_limit_wizard.py         # 加班限制設定嚮導
├── makeup_day_wizard.py             # 補班日設定嚮導
├── holiday_import_wizard_views.xml  # 假日匯入嚮導視圖
├── overtime_limit_wizard_views.xml  # 加班限制嚮導視圖
└── makeup_day_wizard_views.xml      # 補班日嚮導視圖
```

## 1. 假日匯入嚮導

### 檔案位置
- **模型**: [`wizards/holiday_import_wizard.py`](../../wizards/holiday_import_wizard.py)
- **視圖**: [`wizards/holiday_import_wizard_views.xml`](../../wizards/holiday_import_wizard_views.xml)

### 功能概述
假日匯入嚮導提供了一個使用者友善的介面，用於批量匯入假日和補班日資料。支援 CSV 和 Excel 格式的檔案匯入。

### 模型定義

#### 基本資訊
```python
class HolidayImportWizard(models.TransientModel):
    _name = 'holiday.import.wizard'
    _description = '假日匯入嚮導'
```

#### 主要欄位

##### 匯入設定欄位
```python
name = fields.Char('匯入名稱', required=True, 
                   default=lambda self: f"假日匯入 - {datetime.now().strftime('%Y-%m-%d')}")
import_file = fields.Binary('匯入檔案', required=True)
filename = fields.Char('檔案名稱')
calendar_id = fields.Many2one('resource.calendar', '工作行事曆', required=True)
year = fields.Integer('年度', required=True, default=lambda self: datetime.now().year)
```

##### 匯入選項欄位
```python
override_existing = fields.Boolean('覆蓋現有假日', default=False)
create_makeup_days = fields.Boolean('自動創建補班日', default=True)
```

##### 檔案格式設定欄位
```python
date_column = fields.Char('日期欄位名稱', default='date', required=True)
name_column = fields.Char('假日名稱欄位', default='name', required=True)
makeup_column = fields.Char('補班日欄位名稱', default='makeup_date')
```

##### 預覽功能欄位
```python
preview_data = fields.Text('預覽資料', readonly=True)
show_preview = fields.Boolean('顯示預覽', default=False)
```

### 核心功能方法

#### 1. 預覽資料功能
```python
def action_preview_data(self):
    """預覽匯入資料"""
    self.ensure_one()
    
    if not self.import_file:
        raise UserError(_('請上傳匯入檔案'))
    
    try:
        # 讀取檔案
        file_content = base64.b64decode(self.import_file)
        
        if self.filename.endswith('.csv'):
            df = pd.read_csv(io.BytesIO(file_content), encoding='utf-8')
        elif self.filename.endswith(('.xlsx', '.xls')):
            df = pd.read_excel(io.BytesIO(file_content))
        else:
            raise UserError(_('不支援的檔案格式'))
        
        # 檢查必要欄位
        required_columns = [self.date_column, self.name_column]
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise UserError(_('缺少必要欄位: %s') % ', '.join(missing_columns))
        
        # 生成預覽資料
        preview_rows = []
        for i, row in df.head(10).iterrows():
            date_val = row[self.date_column]
            name_val = row[self.name_column]
            makeup_val = row.get(self.makeup_column, '') if self.makeup_column in df.columns else ''
            
            preview_rows.append(f"第{i+1}行: {date_val} - {name_val}" + (f" (補班: {makeup_val})" if makeup_val else ""))
        
        self.preview_data = '\n'.join(preview_rows)
        self.show_preview = True
        
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'holiday.import.wizard',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
            'context': self.env.context,
        }
        
    except Exception as e:
        raise UserError(_('檔案預覽失敗: %s') % str(e))
```

**功能特色**:
- 支援 CSV 和 Excel 格式
- 自動檢測必要欄位
- 提供前 10 筆資料預覽
- 錯誤處理和使用者友善的錯誤訊息

#### 2. 執行匯入功能
```python
def action_import_holidays(self):
    """執行假日匯入"""
    self.ensure_one()
    
    # 創建匯入記錄
    import_record = self.env['tw.holiday.import'].create({
        'name': self.name,
        'import_file': self.import_file,
        'filename': self.filename,
        'calendar_id': self.calendar_id.id,
        'year': self.year,
        'override_existing': self.override_existing,
        'has_makeup_days': self.create_makeup_days,
        'makeup_day_column': self.makeup_column,
    })
    
    # 執行匯入
    import_record.action_import_holidays()
    
    return {
        'type': 'ir.actions.act_window',
        'name': _('匯入結果'),
        'res_model': 'tw.holiday.import',
        'res_id': import_record.id,
        'view_mode': 'form',
        'target': 'current',
    }
```

**處理流程**:
1. 創建 `tw.holiday.import` 記錄
2. 委託給後端模型執行實際匯入
3. 返回匯入結果頁面

### 視圖定義

#### 嚮導表單視圖
```xml
<record id="holiday_import_wizard_view_form" model="ir.ui.view">
    <field name="name">假日匯入嚮導</field>
    <field name="model">holiday.import.wizard</field>
    <field name="arch" type="xml">
        <form string="假日匯入嚮導">
            <group>
                <group string="基本設定">
                    <field name="name"/>
                    <field name="calendar_id"/>
                    <field name="year"/>
                </group>
                
                <group string="檔案設定">
                    <field name="import_file" filename="filename"/>
                    <field name="filename" invisible="1"/>
                    <field name="override_existing"/>
                    <field name="create_makeup_days"/>
                </group>
            </group>
            
            <group string="欄位對應">
                <field name="date_column"/>
                <field name="name_column"/>
                <field name="makeup_column" invisible="not create_makeup_days"/>
            </group>
            
            <group string="預覽資料" invisible="not show_preview">
                <field name="preview_data" nolabel="1" readonly="1"/>
            </group>
            
            <footer>
                <button name="action_preview_data" string="預覽資料" 
                        type="object" class="btn-secondary"/>
                <button name="action_import_holidays" string="執行匯入" 
                        type="object" class="btn-primary"/>
                <button string="取消" class="btn-secondary" special="cancel"/>
            </footer>
        </form>
    </field>
</record>
```

#### 嚮導動作定義
```xml
<record id="action_holiday_import_wizard" model="ir.actions.act_window">
    <field name="name">假日匯入</field>
    <field name="res_model">holiday.import.wizard</field>
    <field name="view_mode">form</field>
    <field name="target">new</field>
</record>
```

### 使用場景

#### 1. 年度假日設定
- 匯入政府公告的國定假日
- 設定公司特殊假日
- 批量處理補班日安排

#### 2. 檔案格式範例

##### CSV 格式範例
```csv
date,name,makeup_date
2024-01-01,元旦,
2024-02-08,農曆除夕,
2024-02-09,農曆新年,
2024-02-10,農曆新年,2024-02-17
2024-02-11,農曆新年,
2024-02-12,農曆新年,
```

##### Excel 格式範例
| 日期 | 假日名稱 | 補班日期 |
|------|----------|----------|
| 2024-01-01 | 元旦 | |
| 2024-02-08 | 農曆除夕 | |
| 2024-02-10 | 農曆新年 | 2024-02-17 |

## 2. 加班限制設定嚮導

### 檔案位置
- **模型**: [`wizards/overtime_limit_wizard.py`](../../wizards/overtime_limit_wizard.py)
- **視圖**: [`wizards/overtime_limit_wizard_views.xml`](../../wizards/overtime_limit_wizard_views.xml)

### 功能概述
加班限制設定嚮導提供了一個簡化的介面，用於快速設定員工或部門的加班時數限制規則。

### 模型定義

#### 基本資訊
```python
class OvertimeLimitWizard(models.TransientModel):
    _name = 'overtime.limit.wizard'
    _description = '加班時數限制設定嚮導'
```

#### 主要欄位

##### 基本設定欄位
```python
name = fields.Char('設定名稱', required=True)
monthly_limit = fields.Float('每月加班時數上限', default=46.0, required=True)
display_limit = fields.Float('顯示時數上限', default=30.0, required=True)
```

##### 適用範圍欄位
```python
apply_scope = fields.Selection([
    ('all', '全公司'),
    ('departments', '指定部門'),
    ('employees', '指定員工')
], string='適用範圍', default='all', required=True)

department_ids = fields.Many2many('hr.department', string='適用部門')
employee_ids = fields.Many2many('hr.employee', string='適用員工')
```

##### 生效時間欄位
```python
date_from = fields.Date('開始日期', default=fields.Date.today)
date_to = fields.Date('結束日期')
priority = fields.Integer('優先級', default=10)
```

### 核心功能方法

#### 1. 適用範圍變更處理
```python
@api.onchange('apply_scope')
def _onchange_apply_scope(self):
    """切換適用範圍時清空相關欄位"""
    if self.apply_scope != 'departments':
        self.department_ids = False
    if self.apply_scope != 'employees':
        self.employee_ids = False
```

#### 2. 資料驗證
```python
@api.constrains('monthly_limit', 'display_limit')
def _check_limits(self):
    for record in self:
        if record.monthly_limit <= 0:
            raise ValidationError(_('每月加班時數上限必須大於0'))
        if record.display_limit < 0:
            raise ValidationError(_('顯示時數上限不能為負數'))
        if record.display_limit > record.monthly_limit:
            raise ValidationError(_('顯示時數上限不能超過每月加班時數上限'))
```

#### 3. 創建限制設定
```python
def action_create_limit(self):
    """創建加班限制設定"""
    self.ensure_one()
    
    vals = {
        'name': self.name,
        'monthly_limit': self.monthly_limit,
        'display_limit': self.display_limit,
        'priority': self.priority,
        'date_from': self.date_from,
        'date_to': self.date_to,
        'apply_to_all': self.apply_scope == 'all',
    }
    
    if self.apply_scope == 'departments':
        vals['department_ids'] = [(6, 0, self.department_ids.ids)]
    elif self.apply_scope == 'employees':
        vals['employee_ids'] = [(6, 0, self.employee_ids.ids)]
    
    limit_record = self.env['tw.overtime.limit'].create(vals)
    
    return {
        'type': 'ir.actions.act_window',
        'name': _('加班時數限制'),
        'res_model': 'tw.overtime.limit',
        'res_id': limit_record.id,
        'view_mode': 'form',
        'target': 'current',
    }
```

### 視圖定義

#### 嚮導表單視圖
```xml
<record id="overtime_limit_wizard_view_form" model="ir.ui.view">
    <field name="name">加班限制設定嚮導</field>
    <field name="model">overtime.limit.wizard</field>
    <field name="arch" type="xml">
        <form string="加班限制設定">
            <group>
                <group string="基本設定">
                    <field name="name"/>
                    <field name="monthly_limit"/>
                    <field name="display_limit"/>
                    <field name="priority"/>
                </group>
                
                <group string="生效期間">
                    <field name="date_from"/>
                    <field name="date_to"/>
                </group>
            </group>
            
            <group string="適用範圍">
                <field name="apply_scope" widget="radio"/>
                <field name="department_ids" 
                       invisible="apply_scope != 'departments'"
                       widget="many2many_tags"/>
                <field name="employee_ids" 
                       invisible="apply_scope != 'employees'"
                       widget="many2many_tags"/>
            </group>
            
            <footer>
                <button name="action_create_limit" string="創建設定" 
                        type="object" class="btn-primary"/>
                <button string="取消" class="btn-secondary" special="cancel"/>
            </footer>
        </form>
    </field>
</record>
```

## 3. 補班日設定嚮導

### 檔案位置
- **模型**: [`wizards/makeup_day_wizard.py`](../../wizards/makeup_day_wizard.py)
- **視圖**: [`wizards/makeup_day_wizard_views.xml`](../../wizards/makeup_day_wizard_views.xml)

### 功能概述
補班日設定嚮導用於快速設定年度的補班日安排，特別是週六補班的情況。

### 模型定義

#### 主要模型
```python
class MakeupDayWizard(models.TransientModel):
    _name = 'makeup.day.wizard'
    _description = '補班日設定嚮導'
```

#### 明細模型
```python
class MakeupDayLine(models.TransientModel):
    _name = 'makeup.day.line'
    _description = '補班日明細'
```

### 主要欄位

#### 嚮導主體欄位
```python
name = fields.Char('設定名稱', required=True)
year = fields.Integer('年度', required=True, default=lambda self: datetime.now().year)
calendar_id = fields.Many2one('resource.calendar', '工作行事曆', required=True)
line_ids = fields.One2many('makeup.day.line', 'wizard_id', '補班日明細')
```

#### 明細欄位
```python
wizard_id = fields.Many2one('makeup.day.wizard', '嚮導', required=True)
makeup_date = fields.Date('補班日期', required=True)
reason = fields.Char('補班原因', required=True)
working_hours = fields.Float('工作時數', default=8.0)
is_saturday = fields.Boolean('是否為週六', compute='_compute_is_saturday')
```

### 核心功能方法

#### 1. 週六檢查
```python
@api.depends('makeup_date')
def _compute_is_saturday(self):
    for line in self:
        if line.makeup_date:
            line.is_saturday = line.makeup_date.weekday() == 5
        else:
            line.is_saturday = False
```

#### 2. 預設補班日生成
```python
def action_generate_default_makeup_days(self):
    """生成預設的補班日"""
    self.ensure_one()
    
    # 清空現有明細
    self.line_ids.unlink()
    
    # 根據年度生成常見的補班日
    default_makeup_days = self._get_default_makeup_days_for_year(self.year)
    
    lines_vals = []
    for makeup_info in default_makeup_days:
        lines_vals.append({
            'wizard_id': self.id,
            'makeup_date': makeup_info['date'],
            'reason': makeup_info['reason'],
            'working_hours': 8.0,
        })
    
    self.env['makeup.day.line'].create(lines_vals)
    
    return {
        'type': 'ir.actions.act_window',
        'res_model': 'makeup.day.wizard',
        'res_id': self.id,
        'view_mode': 'form',
        'target': 'new',
        'context': self.env.context,
    }
```

#### 3. 執行補班日設定
```python
def action_create_makeup_days(self):
    """創建補班日設定"""
    self.ensure_one()
    
    if not self.line_ids:
        raise UserError(_('請先添加補班日明細'))
    
    created_overrides = []
    
    for line in self.line_ids:
        # 檢查是否已存在
        existing = self.env['tw.working.calendar.override'].search([
            ('calendar_id', '=', self.calendar_id.id),
            ('date', '=', line.makeup_date)
        ])
        
        if existing:
            continue
        
        # 創建覆蓋設定
        override = self.env['tw.working.calendar.override'].create({
            'calendar_id': self.calendar_id.id,
            'date': line.makeup_date,
            'is_working_day': True,
            'reason': line.reason,
            'working_hours': line.working_hours,
            'override_type': 'makeup',
        })
        
        # 確認設定
        override.action_confirm()
        created_overrides.append(override)
    
    return {
        'type': 'ir.actions.client',
        'tag': 'display_notification',
        'params': {
            'title': _('設定完成'),
            'message': _('成功創建 %d 個補班日設定') % len(created_overrides),
            'type': 'success',
        }
    }
```

### 視圖定義

#### 嚮導表單視圖
```xml
<record id="makeup_day_wizard_view_form" model="ir.ui.view">
    <field name="name">補班日設定嚮導</field>
    <field name="model">makeup.day.wizard</field>
    <field name="arch" type="xml">
        <form string="補班日設定">
            <group>
                <field name="name"/>
                <field name="year"/>
                <field name="calendar_id"/>
            </group>
            
            <group string="補班日明細">
                <button name="action_generate_default_makeup_days" 
                        string="生成預設補班日" 
                        type="object" class="btn-secondary"/>
            </group>
            
            <field name="line_ids">
                <list editable="bottom">
                    <field name="makeup_date"/>
                    <field name="reason"/>
                    <field name="working_hours"/>
                    <field name="is_saturday" readonly="1"/>
                </list>
            </field>
            
            <footer>
                <button name="action_create_makeup_days" string="創建補班日" 
                        type="object" class="btn-primary"/>
                <button string="取消" class="btn-secondary" special="cancel"/>
            </footer>
        </form>
    </field>
</record>
```

## 4. 雙薪資單生成嚮導

### 檔案位置
- **模型**: [`models/payslip_extension.py`](../../models/payslip_extension.py:547) (內嵌定義)
- **視圖**: 動態生成或在薪資單視圖中定義

### 功能概述
雙薪資單生成嚮導用於批量生成標準版和完整版薪資單，支援不同的員工選擇方式。

### 模型定義

#### 基本資訊
```python
class TwDualPayslipWizard(models.TransientModel):
    _name = 'tw.dual.payslip.wizard'
    _description = '雙薪資單生成嚮導'
```

#### 主要欄位

##### 基本設定欄位
```python
name = fields.Char('批次名稱', compute='_compute_name', store=True)
date_from = fields.Date('開始日期', required=True)
date_to = fields.Date('結束日期', required=True)
```

##### 員工選擇欄位
```python
employee_selection = fields.Selection([
    ('all', '所有員工'),
    ('departments', '指定部門'),
    ('employees', '指定員工')
], string='員工選擇', default='all', required=True)

department_ids = fields.Many2many('hr.department', string='部門')
employee_ids = fields.Many2many('hr.employee', string='員工')
```

##### 生成選項欄位
```python
auto_confirm = fields.Boolean('自動確認薪資單', default=False)
include_draft_overtime = fields.Boolean('包含草稿狀態的加班記錄', default=False)
estimated_count = fields.Integer('預估生成數量', compute='_compute_estimated_count')
```

### 核心功能方法

#### 1. 批次名稱計算
```python
@api.depends('date_from', 'date_to')
def _compute_name(self):
    for record in self:
        if record.date_from and record.date_to:
            record.name = f"雙薪資單批次 - {record.date_from} 至 {record.date_to}"
        else:
            record.name = "雙薪資單批次"
```

#### 2. 預估數量計算
```python
@api.depends('employee_selection', 'department_ids', 'employee_ids')
def _compute_estimated_count(self):
    for record in self:
        employees = record._get_target_employees()
        record.estimated_count = len(employees) * 2  # 每個員工生成兩份薪資單
```

#### 3. 目標員工取得
```python
def _get_target_employees(self):
    """取得目標員工"""
    if self.employee_selection == 'all':
        return self.env['hr.employee'].search([
            ('company_id', '=', self.env.company.id),
            ('contract_ids', '!=', False)
        ])
    elif self.employee_selection == 'departments':
        return self.env['hr.employee'].search([
            ('department_id', 'in', self.department_ids.ids),
            ('contract_ids', '!=', False)
        ])
    else:
        return self.employee_ids.filtered(lambda e: e.contract_ids)
```

## 嚮導設計模式和最佳實踐

### 1. 暫存模型設計
- 使用 `TransientModel` 作為基類
- 設定適當的 `_name` 和 `_description`
- 實作必要的驗證邏輯

### 2. 使用者體驗設計
- 提供預覽功能
- 分步驟引導使用者
- 清晰的錯誤訊息
- 進度指示和回饋

### 3. 資料驗證
- 使用 `@api.constrains` 進行資料驗證
- 使用 `@api.onchange` 提供即時回饋
- 檔案格式和內容驗證

### 4. 錯誤處理
- 使用 `UserError` 提供使用者友善的錯誤訊息
- 詳細的日誌記錄
- 回滾機制確保資料一致性

### 5. 效能優化
- 批次處理大量資料
- 使用事務確保原子性
- 適當的進度回饋

## 嚮導整合和擴展

### 1. 選單整合
```xml
<menuitem id="menu_holiday_import_wizard" 
          name="假日匯入" 
          action="action_holiday_import_wizard" 
          parent="menu_tw_payroll_settings" 
          sequence="10"/>
```

### 2. 按鈕整合
```xml
<button name="%(action_overtime_limit_wizard)d" 
        string="快速設定限制" 
        type="action" 
        class="btn-primary"/>
```

### 3. 權限控制
```xml
<record id="action_holiday_import_wizard" model="ir.actions.act_window">
    <field name="groups_id" eval="[(4, ref('group_holiday_manager'))]"/>
</record>
```

### 4. 國際化支援
- 所有字串使用 `_()` 函數包裝
- 支援多語言介面
- 日期和數字格式本地化

## 測試和除錯

### 1. 單元測試
- 測試嚮導的核心邏輯
- 驗證資料轉換正確性
- 錯誤處理測試

### 2. 整合測試
- 測試與其他模組的整合
- 端到端流程測試
- 權限和安全性測試

### 3. 使用者測試
- 介面易用性測試
- 錯誤情境測試
- 效能測試

## 維護和更新

### 1. 版本相容性
- 確保與 Odoo 版本相容
- 向後相容性考量
- 升級路徑規劃

### 2. 功能擴展
- 模組化設計便於擴展
- 新功能整合點
- API 穩定性維護

### 3. 文檔維護
- 保持文檔與程式碼同步
- 使用範例更新
- 常見問題解答