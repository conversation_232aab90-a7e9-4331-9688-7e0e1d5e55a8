# 權限文檔

## 概述
本文檔詳細說明 l10n_tw_hr_payroll 模組的完整權限架構、安全控制機制和存取控制策略。

## 權限架構概覽

### 權限檔案結構
```
security/
├── tw_payroll_groups.xml      # 權限群組定義
├── tw_payroll_security.xml    # 記錄層級安全規則
└── ir.model.access.csv        # 模型存取權限
```

## 1. 權限群組架構

### 檔案: [`security/tw_payroll_groups.xml`](../../security/tw_payroll_groups.xml)

#### 權限群組層級結構
```mermaid
graph TD
    A[基礎使用者] --> B[薪資使用者]
    B --> C[部門主管]
    B --> D[薪資管理員]
    D --> E[超級管理員]
    B --> F[假日管理員]
    B --> G[工時管理員]
    D --> H[出勤整合管理員]
    D --> I[完整權限存取]
```

#### 權限群組定義

##### 1. 基礎權限群組
```xml
<!-- 台灣薪資基礎使用者 -->
<record id="group_tw_payroll_user" model="res.groups">
    <field name="name">台灣薪資使用者</field>
    <field name="category_id" ref="base.module_category_human_resources"/>
    <field name="comment">可以查看自己的薪資和加班記錄</field>
</record>

<!-- 台灣薪資管理員 -->
<record id="group_tw_payroll_manager" model="res.groups">
    <field name="name">台灣薪資管理員</field>
    <field name="category_id" ref="base.module_category_human_resources"/>
    <field name="implied_ids" eval="[(4, ref('group_tw_payroll_user'))]"/>
    <field name="comment">可以管理所有員工的薪資和加班記錄</field>
</record>

<!-- 台灣薪資超級管理員 -->
<record id="group_tw_payroll_super_admin" model="res.groups">
    <field name="name">台灣薪資超級管理員</field>
    <field name="category_id" ref="base.module_category_human_resources"/>
    <field name="implied_ids" eval="[(4, ref('group_tw_payroll_manager'))]"/>
    <field name="comment">擁有所有薪資模組的完整權限</field>
</record>
```

##### 2. 功能特定權限群組
```xml
<!-- 部門主管 -->
<record id="group_department_supervisor" model="res.groups">
    <field name="name">部門主管</field>
    <field name="category_id" ref="base.module_category_human_resources"/>
    <field name="implied_ids" eval="[(4, ref('group_tw_payroll_user'))]"/>
    <field name="comment">可以審核部門員工的加班申請</field>
</record>

<!-- 假日管理員 -->
<record id="group_holiday_manager" model="res.groups">
    <field name="name">假日管理員</field>
    <field name="category_id" ref="base.module_category_human_resources"/>
    <field name="implied_ids" eval="[(4, ref('group_tw_payroll_user'))]"/>
    <field name="comment">可以管理假日和補班日設定</field>
</record>

<!-- 工作時間管理員 -->
<record id="group_working_time_manager" model="res.groups">
    <field name="name">工作時間管理員</field>
    <field name="category_id" ref="base.module_category_human_resources"/>
    <field name="implied_ids" eval="[(4, ref('group_tw_payroll_user'))]"/>
    <field name="comment">可以管理工作行事曆和時間設定</field>
</record>

<!-- 出勤整合管理員 -->
<record id="group_attendance_integration_manager" model="res.groups">
    <field name="name">出勤整合管理員</field>
    <field name="category_id" ref="base.module_category_human_resources"/>
    <field name="implied_ids" eval="[(4, ref('group_tw_payroll_manager'))]"/>
    <field name="comment">可以管理出勤與加班記錄的整合</field>
</record>

<!-- 加班時數完整存取 -->
<record id="group_overtime_full_access" model="res.groups">
    <field name="name">加班時數完整存取</field>
    <field name="category_id" ref="base.module_category_human_resources"/>
    <field name="implied_ids" eval="[(4, ref('group_tw_payroll_manager'))]"/>
    <field name="comment">可以查看完整的加班時數（包含隱藏時數）</field>
</record>
```

#### 權限群組功能對照表

| 權限群組 | 查看薪資 | 編輯薪資 | 審核加班 | 查看隱藏時數 | 管理設定 | 批次操作 |
|----------|----------|----------|----------|--------------|----------|----------|
| 基礎使用者 | 自己 | ❌ | ❌ | ❌ | ❌ | ❌ |
| 部門主管 | 部門 | ❌ | 部門 | ❌ | ❌ | ❌ |
| 薪資管理員 | 全部 | ✅ | ✅ | ❌ | 部分 | ✅ |
| 超級管理員 | 全部 | ✅ | ✅ | ✅ | ✅ | ✅ |
| 完整權限存取 | 全部 | ✅ | ✅ | ✅ | ❌ | ✅ |

## 2. 模型存取權限

### 檔案: [`security/ir.model.access.csv`](../../security/ir.model.access.csv)

#### 存取權限矩陣

##### 加班相關模型權限
```csv
id,name,model_id/id,group_id/id,perm_read,perm_write,perm_create,perm_unlink
access_tw_overtime_record_user,tw.hr.attendance (原 tw.overtime.record 已移除).user,model_tw_overtime_record,group_tw_payroll_user,1,1,1,0
access_tw_overtime_record_manager,tw.hr.attendance (原 tw.overtime.record 已移除).manager,model_tw_overtime_record,group_tw_payroll_manager,1,1,1,1
access_tw_overtime_record_dept_supervisor,tw.hr.attendance (原 tw.overtime.record 已移除).dept.supervisor,model_tw_overtime_record,group_department_supervisor,1,1,0,0
access_tw_overtime_record_integration_manager,tw.hr.attendance (原 tw.overtime.record 已移除).integration.manager,model_tw_overtime_record,group_attendance_integration_manager,1,1,1,1
access_tw_overtime_record_super_admin,tw.hr.attendance (原 tw.overtime.record 已移除).super.admin,model_tw_overtime_record,group_tw_payroll_super_admin,1,1,1,1
```

##### 加班限制模型權限
```csv
access_tw_overtime_limit_user,tw.overtime.limit.user,model_tw_overtime_limit,group_tw_payroll_user,1,0,0,0
access_tw_overtime_limit_manager,tw.overtime.limit.manager,model_tw_overtime_limit,group_tw_payroll_manager,1,1,1,1
access_tw_overtime_limit_integration_manager,tw.overtime.limit.integration.manager,model_tw_overtime_limit,group_attendance_integration_manager,1,1,1,1
```

##### 假日管理模型權限
```csv
access_tw_holiday_import_user,tw.holiday.import.user,model_tw_holiday_import,group_tw_payroll_user,1,0,0,0
access_tw_holiday_import_manager,tw.holiday.import.manager,model_tw_holiday_import,group_holiday_manager,1,1,1,1
```

##### 工作行事曆模型權限
```csv
access_tw_working_calendar_override_user,tw.working.calendar.override.user,model_tw_working_calendar_override,group_tw_payroll_user,1,0,0,0
access_tw_working_calendar_override_manager,tw.working.calendar.override.manager,model_tw_working_calendar_override,group_working_time_manager,1,1,1,1
```

##### 嚮導模型權限
```csv
access_tw_dual_payslip_wizard_manager,tw.dual.payslip.wizard.manager,model_tw_dual_payslip_wizard,group_tw_payroll_manager,1,1,1,1
access_makeup_day_wizard_user,makeup.day.wizard.user,model_makeup_day_wizard,group_tw_payroll_user,1,1,1,1
access_makeup_day_wizard_manager,makeup.day.wizard.manager,model_makeup_day_wizard,group_tw_payroll_manager,1,1,1,1
access_holiday_import_wizard_user,holiday.import.wizard.user,model_holiday_import_wizard,group_tw_payroll_user,1,1,1,1
access_overtime_limit_wizard_user,overtime.limit.wizard.user,model_overtime_limit_wizard,group_tw_payroll_user,1,1,1,1
```

#### 權限說明

##### 權限欄位含義
- `perm_read`: 讀取權限 (1=允許, 0=拒絕)
- `perm_write`: 寫入權限 (1=允許, 0=拒絕)
- `perm_create`: 創建權限 (1=允許, 0=拒絕)
- `perm_unlink`: 刪除權限 (1=允許, 0=拒絕)

##### 權限設計原則
1. **最小權限原則**: 使用者只獲得執行工作所需的最小權限
2. **職責分離**: 不同角色擁有不同的權限範圍
3. **層級繼承**: 高級權限群組繼承低級權限
4. **功能隔離**: 特定功能需要特定權限群組

## 3. 記錄層級安全規則

### 檔案: [`security/tw_payroll_security.xml`](../../security/tw_payroll_security.xml)

#### 加班記錄安全規則

##### 1. 基礎使用者規則
```xml
<!-- 基礎使用者只能查看自己的加班記錄 -->
<record id="tw_overtime_record_user_rule" model="ir.rule">
    <field name="name">加班記錄：使用者規則</field>
    <field name="model_id" ref="model_tw_overtime_record"/>
    <field name="domain_force">[('employee_id.user_id', '=', user.id)]</field>
    <field name="groups" eval="[(4, ref('group_tw_payroll_user'))]"/>
    <field name="perm_read" eval="True"/>
    <field name="perm_write" eval="True"/>
    <field name="perm_create" eval="True"/>
    <field name="perm_unlink" eval="False"/>
</record>

<!-- 部門主管可以查看部門員工的加班記錄 -->
<record id="tw_overtime_record_dept_supervisor_rule" model="ir.rule">
    <field name="name">加班記錄：部門主管規則</field>
    <field name="model_id" ref="model_tw_overtime_record"/>
    <field name="domain_force">[('employee_id.department_id.manager_id.user_id', '=', user.id)]</field>
    <field name="groups" eval="[(4, ref('group_department_supervisor'))]"/>
    <field name="perm_read" eval="True"/>
    <field name="perm_write" eval="True"/>
    <field name="perm_create" eval="False"/>
    <field name="perm_unlink" eval="False"/>
</record>

<!-- 薪資管理員可以查看所有加班記錄 -->
<record id="tw_overtime_record_manager_rule" model="ir.rule">
    <field name="name">加班記錄：管理員規則</field>
    <field name="model_id" ref="model_tw_overtime_record"/>
    <field name="domain_force">[(1, '=', 1)]</field>
    <field name="groups" eval="[(4, ref('group_tw_payroll_manager'))]"/>
    <field name="perm_read" eval="True"/>
    <field name="perm_write" eval="True"/>
    <field name="perm_create" eval="True"/>
    <field name="perm_unlink" eval="True"/>
</record>
```

##### 2. 薪資單安全規則
```xml
<!-- 使用者只能查看自己的薪資單 -->
<record id="hr_payslip_user_rule" model="ir.rule">
    <field name="name">薪資單：使用者規則</field>
    <field name="model_id" ref="hr_payroll.model_hr_payslip"/>
    <field name="domain_force">[('employee_id.user_id', '=', user.id)]</field>
    <field name="groups" eval="[(4, ref('group_tw_payroll_user'))]"/>
    <field name="perm_read" eval="True"/>
    <field name="perm_write" eval="False"/>
    <field name="perm_create" eval="False"/>
    <field name="perm_unlink" eval="False"/>
</record>

<!-- 完整版薪資單需要特殊權限 -->
<record id="hr_payslip_full_version_rule" model="ir.rule">
    <field name="name">薪資單：完整版存取規則</field>
    <field name="model_id" ref="hr_payroll.model_hr_payslip"/>
    <field name="domain_force">[('payslip_type', '!=', 'full')]</field>
    <field name="groups" eval="[(4, ref('group_tw_payroll_user'))]"/>
    <field name="perm_read" eval="True"/>
    <field name="perm_write" eval="False"/>
    <field name="perm_create" eval="False"/>
    <field name="perm_unlink" eval="False"/>
</record>
```

##### 3. 多公司安全規則
```xml
<!-- 多公司環境下的資料隔離 -->
<record id="tw_overtime_record_company_rule" model="ir.rule">
    <field name="name">加班記錄：公司規則</field>
    <field name="model_id" ref="model_tw_overtime_record"/>
    <field name="domain_force">[('employee_id.company_id', 'in', company_ids)]</field>
    <field name="global" eval="True"/>
</record>

<record id="tw_overtime_limit_company_rule" model="ir.rule">
    <field name="name">加班限制：公司規則</field>
    <field name="model_id" ref="model_tw_overtime_limit"/>
    <field name="domain_force">[('company_id', 'in', company_ids)]</field>
    <field name="global" eval="True"/>
</record>
```

## 4. 動態權限控制

### 程式碼層級權限檢查

#### 1. 員工權限計算
```python
# models/hr_employee_extension.py
def _compute_permissions(self):
    """計算員工相關權限"""
    for employee in self:
        user = self.env.user
        
        # 檢查是否可查看完整加班時數
        employee.can_view_full_overtime = (
            user.has_group('l10n_tw_hr_payroll.group_overtime_full_access') or
            user.has_group('l10n_tw_hr_payroll.group_tw_payroll_super_admin') or
            user.has_group('l10n_tw_hr_payroll.group_attendance_integration_manager')
        )
        
        # 檢查是否可核准加班
        employee.can_approve_overtime = (
            user.has_group('l10n_tw_hr_payroll.group_tw_payroll_manager') or
            user.has_group('l10n_tw_hr_payroll.group_tw_payroll_super_admin') or
            user.has_group('l10n_tw_hr_payroll.group_department_supervisor') or
            user.has_group('l10n_tw_hr_payroll.group_attendance_integration_manager')
        )
```

#### 2. 加班記錄權限檢查
```python
# models/overtime_management.py
def action_approve(self):
    """核准加班申請"""
    if not (self.env.user.has_group('l10n_tw_hr_payroll.group_tw_payroll_manager') or
            self.env.user.has_group('l10n_tw_hr_payroll.group_department_supervisor') or
            self.env.user.has_group('l10n_tw_hr_payroll.group_attendance_integration_manager')):
        raise UserError(_('您沒有權限核准加班申請'))
    
    for record in self:
        if record.state != 'submitted':
            continue
        
        # 部門主管只能核准自己部門的記錄
        if (self.env.user.has_group('l10n_tw_hr_payroll.group_department_supervisor') and
            not self.env.user.has_group('l10n_tw_hr_payroll.group_tw_payroll_manager')):
            if record.employee_id.department_id.manager_id.user_id != self.env.user:
                raise UserError(_('您只能核准自己部門員工的加班申請'))
        
        record.state = 'approved'
        record.approved_by = self.env.user.id
        record.approved_date = fields.Datetime.now()
```

#### 3. 薪資單權限檢查
```python
# models/payslip_extension.py
def action_view_paired_payslip(self):
    """查看配對薪資單"""
    self.ensure_one()
    
    if not self.paired_payslip_id:
        raise UserError(_('該薪資單沒有配對薪資單'))
    
    # 檢查完整版薪資單存取權限
    if (self.paired_payslip_id.payslip_type == 'full' and
        not (self.env.user.has_group('l10n_tw_hr_payroll.group_overtime_full_access') or
             self.env.user.has_group('l10n_tw_hr_payroll.group_tw_payroll_super_admin') or
             self.env.user.has_group('l10n_tw_hr_payroll.group_attendance_integration_manager'))):
        raise UserError(_('您沒有權限查看完整版薪資單'))
    
    return {
        'type': 'ir.actions.act_window',
        'name': _('配對薪資單'),
        'view_mode': 'form',
        'res_model': 'hr.payslip',
        'res_id': self.paired_payslip_id.id,
        'target': 'current',
    }
```

## 5. 視圖層級權限控制

### 欄位可見性控制

#### 1. 群組基礎控制
```xml
<!-- 只有特定群組可以看到的欄位 -->
<field name="hidden_overtime_hours" 
       groups="l10n_tw_hr_payroll.group_overtime_full_access"/>

<field name="actual_overtime_hours" 
       groups="l10n_tw_hr_payroll.group_tw_payroll_manager,l10n_tw_hr_payroll.group_overtime_full_access"/>
```

#### 2. 動態可見性控制
```xml
<!-- 根據計算欄位控制可見性 -->
<field name="full_overtime_amount" 
       invisible="not can_access_full_version"/>

<field name="hidden_overtime_hours" 
       invisible="not show_hidden_overtime"/>

<!-- 根據記錄狀態控制可見性 -->
<field name="reject_reason" 
       invisible="state != 'rejected'"/>
```

#### 3. 按鈕權限控制
```xml
<!-- 群組權限控制 -->
<button name="action_approve" 
        string="核准" 
        groups="l10n_tw_hr_payroll.group_tw_payroll_manager,l10n_tw_hr_payroll.group_department_supervisor"/>

<!-- 動態權限控制 -->
<button name="batch_approve_records" 
        string="批次核准" 
        invisible="not can_approve_overtime"/>
```

### 選單權限控制

#### 1. 選單群組限制
```xml
<menuitem id="menu_overtime_management" 
          name="加班管理" 
          groups="l10n_tw_hr_payroll.group_tw_payroll_user"/>

<menuitem id="menu_payroll_settings" 
          name="薪資設定" 
          groups="l10n_tw_hr_payroll.group_tw_payroll_manager"/>

<menuitem id="menu_overtime_full_access" 
          name="完整加班資訊" 
          groups="l10n_tw_hr_payroll.group_overtime_full_access"/>
```

#### 2. 動作權限控制
```xml
<record id="action_overtime_records" model="ir.actions.act_window">
    <field name="name">加班記錄</field>
    <field name="res_model">tw.hr.attendance (原 tw.overtime.record 已移除)</field>
    <field name="groups_id" eval="[(4, ref('group_tw_payroll_user'))]"/>
</record>
```

## 6. 資料隱私和敏感資訊保護

### 敏感欄位保護

#### 1. 身分證字號保護
```python
# models/hr_employee_extension.py
@api.constrains('national_id')
def _check_national_id(self):
    """驗證身分證字號格式和唯一性"""
    for employee in self:
        if employee.national_id:
            # 格式檢查
            import re
            if not re.match(r'^[A-Z][0-9]{9}$', employee.national_id):
                raise ValidationError(_('身分證字號格式不正確'))
            
            # 唯一性檢查
            existing = self.search([
                ('national_id', '=', employee.national_id),
                ('id', '!=', employee.id)
            ])
            if existing:
                raise ValidationError(_('身分證字號不能重複'))

def _get_national_id_display(self):
    """取得遮罩後的身分證字號顯示"""
    if not self.national_id:
        return ''
    
    # 只有特定權限才能看到完整身分證字號
    if self.env.user.has_group('l10n_tw_hr_payroll.group_tw_payroll_super_admin'):
        return self.national_id
    else:
        # 遮罩中間數字
        return self.national_id[:2] + '*' * 6 + self.national_id[-2:]
```

#### 2. 薪資資訊保護
```python
# models/payslip_extension.py
def _compute_display_settings(self):
    for payslip in self:
        # 檢查使用者是否有完整查看權限
        payslip.can_access_full_version = (
            self.env.user.has_group('l10n_tw_hr_payroll.group_overtime_full_access') or
            self.env.user.has_group('l10n_tw_hr_payroll.group_tw_payroll_super_admin') or
            self.env.user.has_group('l10n_tw_hr_payroll.group_attendance_integration_manager') or
            payslip.employee_id.user_id == self.env.user  # 員工可以看自己的
        )
```

### 存取日誌記錄

#### 1. 敏感操作記錄
```python
def _log_sensitive_access(self, operation, record_id=None):
    """記錄敏感資料存取"""
    self.env['audit.log'].sudo().create({
        'user_id': self.env.user.id,
        'model': self._name,
        'res_id': record_id or self.id,
        'operation': operation,
        'timestamp': fields.Datetime.now(),
        'ip_address': self.env.context.get('request_ip'),
        'user_agent': self.env.context.get('request_user_agent'),
    })

def read(self, fields=None, load='_classic_read'):
    """覆寫讀取方法以記錄存取"""
    result = super().read(fields, load)
    
    # 記錄敏感欄位存取
    sensitive_fields = ['national_id', 'monthly_salary', 'hidden_overtime_hours']
    if fields and any(field in sensitive_fields for field in fields):
        self._log_sensitive_access('read')
    
    return result
```

## 7. 權限測試和驗證

### 權限測試案例

#### 1. 基礎權限測試
```python
def test_user_can_only_see_own_records(self):
    """測試使用者只能看到自己的記錄"""
    # 創建測試使用者和員工
    user1 = self.env['res.users'].create({
        'name': 'Test User 1',
        'login': '<EMAIL>',
        'groups_id': [(4, self.ref('l10n_tw_hr_payroll.group_tw_payroll_user'))]
    })
    
    employee1 = self.env['hr.employee'].create({
        'name': 'Employee 1',
        'user_id': user1.id
    })
    
    # 創建加班記錄
    overtime1 = self.env['tw.hr.attendance (原 tw.overtime.record 已移除)'].create({
        'employee_id': employee1.id,
        'date': fields.Date.today(),
        'actual_overtime_hours': 2.0
    })
    
    # 切換到測試使用者
    overtime_as_user1 = overtime1.sudo(user1)
    
    # 驗證可以存取自己的記錄
    self.assertTrue(overtime_as_user1.exists())
    
    # 驗證不能存取其他人的記錄
    other_overtime = self.env['tw.hr.attendance (原 tw.overtime.record 已移除)'].create({
        'employee_id': self.env.ref('hr.employee_admin').id,
        'date': fields.Date.today(),
        'actual_overtime_hours': 3.0
    })
    
    other_overtime_as_user1 = other_overtime.sudo(user1)
    self.assertFalse(other_overtime_as_user1.exists())
```

#### 2. 管理員權限測試
```python
def test_manager_can_approve_overtime(self):
    """測試管理員可以核准加班"""
    manager = self.env['res.users'].create({
        'name': 'Manager',
        'login': '<EMAIL>',
        'groups_id': [(4, self.ref('l10n_tw_hr_payroll.group_tw_payroll_manager'))]
    })
    
    overtime = self.env['tw.hr.attendance (原 tw.overtime.record 已移除)'].create({
        'employee_id': self.env.ref('hr.employee_admin').id,
        'date': fields.Date.today(),
        'actual_overtime_hours': 2.0,
        'state': 'submitted'
    })
    
    # 切換到管理員身份
    overtime_as_manager = overtime.sudo(manager)
    
    # 測試核准功能
    overtime_as_manager.action_approve()
    self.assertEqual(overtime.state, 'approved')
    self.assertEqual(overtime.approved_by, manager)
```

### 權限審計

#### 1. 權限配置檢查
```python
def audit_permission_configuration(self):
    """審計權限配置"""
    issues = []
    
    # 檢查是否有模型缺少存取權限
    models_without_access = []
    for model in ['tw.hr.attendance (原 tw.overtime.record 已移除)', 'tw.overtime.limit']:
        access_records = self.env['ir.model.access'].search([
            ('model_id.model', '=', model)
        ])
        if not access_records:
            models_without_access.append(model)
    
    if models_without_access:
        issues.append(f"缺少存取權限的模型: {', '.join(models_without_access)}")
    
    # 檢查是否有群組沒有基礎權限
    groups_without_base = []
    base_group = self.env.ref('l10n_tw_hr_payroll.group_tw_payroll_user')
    for group in self.env['res.groups'].search([('category_id.name', '=', 'Human Resources')]):
        if base_group not in group.implied_ids and group != base_group:
            groups_without_base.append(group.name)
    
    if groups_without_base:
        issues.append(f"沒有基礎權限的群組: {', '.join(groups_without_base)}")
    
    return issues
```

## 8. 權限最佳實踐

### 設計原則

#### 1. 最小權限原則
- 使用者只獲得執行工作所需的最小權限
- 定