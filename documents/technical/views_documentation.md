# 視圖文檔

## 概述
本文檔詳細說明 l10n_tw_hr_payroll 模組中所有視圖的定義、功能和使用方式。

## 視圖架構概覽

### 視圖檔案結構
```
views/
├── menuitems.xml              # 主選單定義
├── hr_employee_views.xml      # 員工視圖擴展
├── hr_attendance_views.xml    # 出勤視圖擴展
├── overtime_views.xml         # 加班管理視圖
├── ~~overtime_integration_views.xml~~  # ~~加班整合視圖~~ **[已移除]**
├── payslip_views.xml          # 薪資單視圖
├── working_calendar_views.xml # 工作行事曆視圖
└── holiday_import_views.xml   # 假日匯入視圖
```

## 主選單結構

### 選單檔案: [`views/menuitems.xml`](../../views/menuitems.xml)

#### 主選單架構
```xml
<!-- 台灣薪資主選單 -->
<menuitem id="menu_tw_payroll_root" 
          name="台灣薪資" 
          sequence="85"/>

<!-- 加班管理子選單 -->
<menuitem id="menu_tw_overtime_management" 
          name="加班管理" 
          parent="menu_tw_payroll_root" 
          sequence="10"/>

<!-- 薪資管理子選單 -->
<menuitem id="menu_tw_payroll_management" 
          name="薪資管理" 
          parent="menu_tw_payroll_root" 
          sequence="20"/>

<!-- 設定子選單 -->
<menuitem id="menu_tw_payroll_settings" 
          name="設定" 
          parent="menu_tw_payroll_root" 
          sequence="90"/>
```

#### 功能選單項目

##### 加班管理選單
- **加班記錄**: 查看和管理所有加班記錄
- **加班限制設定**: 配置加班時數限制
- **出勤整合**: 出勤與加班記錄整合管理

##### 薪資管理選單
- **薪資單**: 標準和完整版薪資單管理
- **薪資報表**: 薪資統計和分析報表

##### 設定選單
- **假日匯入**: 假日和補班日資料匯入
- **工作行事曆**: 工作時間和假日設定

## 員工視圖擴展

### 檔案: [`views/hr_employee_views.xml`](../../views/hr_employee_views.xml)

#### 員工表單視圖擴展
**視圖 ID**: `hr_employee_view_form_tw_payroll`
**繼承**: `hr.hr_employee_view_form`

##### 新增頁籤結構
```xml
<page string="台灣薪資資訊" name="tw_payroll_info">
    <group string="基本資訊">
        <field name="tw_employee_id"/>
        <field name="national_id"/>
        <field name="labor_insurance_no"/>
        <field name="health_insurance_no"/>
    </group>
    
    <group string="薪資設定">
        <field name="monthly_salary"/>
        <field name="hourly_rate" readonly="1"/>
        <field name="overtime_rate"/>
        <field name="overtime_rate_extended"/>
        <field name="holiday_overtime_rate"/>
    </group>
    
    <group string="工作時間">
        <field name="personal_calendar_id"/>
        <field name="standard_working_hours"/>
        <field name="weekly_working_hours"/>
    </group>
</page>
```

##### 加班統計頁籤
```xml
<page string="加班統計" name="overtime_stats">
    <group string="本月統計">
        <field name="current_month_overtime"/>
        <field name="current_month_displayed_overtime" 
               invisible="not can_view_full_overtime"/>
        <field name="current_month_hidden_overtime" 
               invisible="not can_view_full_overtime"/>
    </group>
    
    <group string="出勤整合統計">
        <field name="auto_overtime_count"/>
        <field name="manual_overtime_count"/>
        <field name="pending_attendance_overtime"/>
    </group>
</page>
```

#### 員工列表視圖擴展
**視圖 ID**: `hr_employee_view_tree_tw_payroll`

##### 新增欄位
- 員工編號
- 本月加班時數
- 待處理出勤加班數量
- 權限狀態指示器

#### 員工搜尋視圖擴展
**視圖 ID**: `hr_employee_view_search_tw_payroll`

##### 搜尋篩選器
```xml
<filter string="有待處理加班" name="has_pending_overtime" 
        domain="[('pending_attendance_overtime', '>', 0)]"/>
<filter string="本月有加班" name="has_current_month_overtime" 
        domain="[('current_month_overtime', '>', 0)]"/>
<filter string="有隱藏加班時數" name="has_hidden_overtime" 
        domain="[('current_month_hidden_overtime', '>', 0)]"/>
```

## 出勤視圖擴展

### 檔案: [`views/hr_attendance_views.xml`](../../views/hr_attendance_views.xml)

#### 出勤表單視圖擴展
**視圖 ID**: `hr_attendance_view_form_tw_extension`
**繼承**: `hr_attendance.hr_attendance_view_form`

##### 台灣加班資訊群組
```xml
<group string="台灣加班資訊" name="tw_overtime_info">
    <field name="tw_overtime_hours"/>
    <field name="tw_overtime_type"/>
    <field name="displayed_tw_overtime_hours"/>
    <field name="tw_overtime_record_id" readonly="1"/>
</group>

<group string="工作日資訊" name="working_day_info">
    <field name="is_working_day"/>
    <field name="expected_working_hours"/>
    <field name="break_hours"/>
</group>

<group string="本地時間" name="local_times">
    <field name="check_in_local"/>
    <field name="check_out_local"/>
</group>
```

##### 操作按鈕
```xml
<header>
    <button name="action_view_overtime_record" 
            string="查看加班記錄" 
            type="object" 
            invisible="not tw_overtime_record_id"
            class="btn-primary"/>
    <button name="action_create_overtime_record" 
            string="創建加班記錄" 
            type="object" 
            invisible="tw_overtime_record_id or tw_overtime_hours == 0"
            class="btn-secondary"/>
</header>
```

#### 出勤列表視圖擴展
**視圖 ID**: `hr_attendance_view_tree_tw_extension`

##### 新增欄位顯示
- 台灣加班時數
- 加班類型
- 關聯加班記錄狀態
- 自動處理狀態

#### 出勤搜尋視圖
**視圖 ID**: `hr_attendance_view_search_tw_extension`

##### 搜尋篩選器
```xml
<filter string="有加班時數" name="has_overtime" 
        domain="[('tw_overtime_hours', '>', 0)]"/>
<filter string="未創建加班記錄" name="no_overtime_record" 
        domain="[('tw_overtime_hours', '>', 0), ('tw_overtime_record_id', '=', False)]"/>
<filter string="平日加班" name="weekday_overtime" 
        domain="[('tw_overtime_type', '=', 'weekday')]"/>
<filter string="假日加班" name="weekend_overtime" 
        domain="[('tw_overtime_type', 'in', ['weekend', 'holiday'])]"/>
```

## 加班管理視圖

### 檔案: [`views/overtime_views.xml`](../../views/overtime_views.xml)

#### 加班記錄表單視圖
**視圖 ID**: `tw_overtime_record_view_form`

##### 表單結構
```xml
<form string="加班記錄">
    <header>
        <button name="action_submit" string="提交" 
                type="object" invisible="state != 'draft'" 
                class="btn-primary"/>
        <button name="action_approve" string="核准" 
                type="object" invisible="state != 'submitted'" 
                class="btn-success"/>
        <button name="action_reject" string="拒絕" 
                type="object" invisible="state != 'submitted'" 
                class="btn-danger"/>
        <field name="state" widget="statusbar" 
               statusbar_visible="draft,submitted,approved"/>
    </header>
    
    <sheet>
        <group>
            <group string="基本資訊">
                <field name="employee_id"/>
                <field name="date"/>
                <field name="overtime_type"/>
                <field name="source_type" readonly="1"/>
            </group>
            
            <group string="時數資訊">
                <field name="actual_overtime_hours"/>
                <field name="displayed_overtime_hours" readonly="1"/>
                <field name="hidden_overtime_hours" readonly="1" 
                       invisible="not show_full_overtime"/>
            </group>
        </group>
        
        <group string="審核資訊" invisible="state in ['draft']">
            <field name="approved_by" readonly="1"/>
            <field name="approved_date" readonly="1"/>
            <field name="reject_reason" invisible="state != 'rejected'"/>
        </group>
        
        <group string="關聯資訊" invisible="not attendance_id">
            <field name="attendance_id" readonly="1"/>
            <field name="auto_created" readonly="1"/>
        </group>
    </sheet>
</form>
```

#### 加班記錄列表視圖
**視圖 ID**: `tw_overtime_record_view_tree`

##### 列表欄位配置
```xml
<list string="加班記錄" multi_edit="1" 
      decoration-info="state == 'draft'" 
      decoration-warning="state == 'submitted'" 
      decoration-success="state == 'approved'" 
      decoration-danger="state == 'rejected'">
    
    <field name="employee_id"/>
    <field name="date"/>
    <field name="overtime_type"/>
    <field name="actual_overtime_hours" sum="總實際時數"/>
    <field name="displayed_overtime_hours" sum="總顯示時數"/>
    <field name="hidden_overtime_hours" sum="總隱藏時數" 
           invisible="context.get('hide_full_access_fields', True)"/>
    <field name="source_type"/>
    <field name="state"/>
    <field name="approved_by"/>
</list>
```

#### 加班記錄搜尋視圖
**視圖 ID**: `tw_overtime_record_view_search`

##### 搜尋欄位和篩選器
```xml
<search string="搜尋加班記錄">
    <field name="employee_id"/>
    <field name="date"/>
    <field name="overtime_type"/>
    
    <filter string="草稿" name="draft" domain="[('state', '=', 'draft')]"/>
    <filter string="已提交" name="submitted" domain="[('state', '=', 'submitted')]"/>
    <filter string="已核准" name="approved" domain="[('state', '=', 'approved')]"/>
    <filter string="已拒絕" name="rejected" domain="[('state', '=', 'rejected')]"/>
    
    <separator/>
    <filter string="自動創建" name="auto_created" domain="[('auto_created', '=', True)]"/>
    <filter string="手動創建" name="manual_created" domain="[('auto_created', '=', False)]"/>
    
    <separator/>
    <filter string="本月" name="current_month" 
            domain="[('date', '>=', (context_today() + relativedelta(day=1)).strftime('%Y-%m-%d')), 
                     ('date', '&lt;', (context_today() + relativedelta(months=1, day=1)).strftime('%Y-%m-%d'))]"/>
    
    <group expand="0" string="分組">
        <filter string="員工" name="group_by_employee" context="{'group_by': 'employee_id'}"/>
        <filter string="狀態" name="group_by_state" context="{'group_by': 'state'}"/>
        <filter string="加班類型" name="group_by_overtime_type" context="{'group_by': 'overtime_type'}"/>
        <filter string="來源類型" name="group_by_source_type" context="{'group_by': 'source_type'}"/>
        <filter string="月份" name="group_by_month" context="{'group_by': 'month'}"/>
    </group>
</search>
```

#### 加班記錄看板視圖
**視圖 ID**: `tw_overtime_record_view_kanban`

```xml
<kanban default_group_by="state" class="o_kanban_small_column">
    <field name="employee_id"/>
    <field name="date"/>
    <field name="actual_overtime_hours"/>
    <field name="overtime_type"/>
    <field name="state"/>
    
    <templates>
        <t t-name="kanban-box">
            <div class="oe_kanban_card oe_kanban_global_click">
                <div class="oe_kanban_content">
                    <div class="o_kanban_record_top">
                        <div class="o_kanban_record_headings">
                            <strong class="o_kanban_record_title">
                                <field name="employee_id"/>
                            </strong>
                        </div>
                        <div class="o_kanban_record_subtitle">
                            <field name="date"/>
                        </div>
                    </div>
                    <div class="o_kanban_record_body">
                        <div>加班時數: <field name="actual_overtime_hours"/> 小時</div>
                        <div>類型: <field name="overtime_type"/></div>
                    </div>
                </div>
            </div>
        </t>
    </templates>
</kanban>
```

### 加班限制設定視圖

#### 加班限制表單視圖
**視圖 ID**: `tw_overtime_limit_view_form`

```xml
<form string="加班時數限制">
    <sheet>
        <group>
            <group string="基本設定">
                <field name="name"/>
                <field name="company_id"/>
                <field name="active"/>
                <field name="priority"/>
            </group>
            
            <group string="時數限制">
                <field name="monthly_limit"/>
                <field name="display_limit"/>
                <field name="auto_approve"/>
                <field name="auto_approve_limit" invisible="not auto_approve"/>
            </group>
        </group>
        
        <group string="適用範圍">
            <field name="apply_to_all"/>
            <field name="department_ids" invisible="apply_to_all" 
                   widget="many2many_tags"/>
            <field name="employee_ids" invisible="apply_to_all" 
                   widget="many2many_tags"/>
        </group>
        
        <group string="生效期間">
            <field name="date_from"/>
            <field name="date_to"/>
        </group>
    </sheet>
</form>
```

## 薪資單視圖

### 檔案: [`views/payslip_views.xml`](../../views/payslip_views.xml)

#### 薪資單表單視圖擴展
**視圖 ID**: `hr_payslip_view_form_tw_extension`
**繼承**: `hr_payslip.hr_payslip_view_form`

##### 雙薪資單資訊群組
```xml
<group string="雙薪資單資訊" name="dual_payslip_info">
    <field name="payslip_type"/>
    <field name="paired_payslip_id" readonly="1"/>
    <field name="is_primary_payslip" readonly="1"/>
</group>

<group string="加班時數資訊" name="overtime_hours_info">
    <field name="displayed_overtime_hours"/>
    <field name="hidden_overtime_hours" invisible="not show_hidden_overtime"/>
    <field name="total_overtime_hours"/>
</group>

<group string="加班費計算" name="overtime_amounts">
    <field name="standard_overtime_amount"/>
    <field name="full_overtime_amount" invisible="payslip_type != 'full'"/>
    <field name="hidden_overtime_amount" invisible="not show_hidden_overtime"/>
</group>
```

##### 操作按鈕
```xml
<header>
    <button name="action_generate_dual_payslips" 
            string="生成雙薪資單" 
            type="object" 
            invisible="state != 'draft' or paired_payslip_id"
            class="btn-primary"/>
    <button name="action_view_paired_payslip" 
            string="查看配對薪資單" 
            type="object" 
            invisible="not paired_payslip_id"
            class="btn-secondary"/>
</header>
```

#### 薪資單列表視圖擴展
**視圖 ID**: `hr_payslip_view_tree_tw_extension`

##### 新增欄位
- 薪資單類型
- 顯示加班時數
- 隱藏加班時數（權限控制）
- 配對薪資單狀態

#### 薪資單批次視圖擴展
**視圖 ID**: `hr_payslip_run_view_form_tw_extension`

```xml
<group string="批次統計" name="batch_stats">
    <field name="batch_type"/>
    <field name="standard_payslip_count"/>
    <field name="full_payslip_count"/>
</group>

<button name="action_generate_dual_payslips" 
        string="生成雙薪資單批次" 
        type="object" 
        invisible="state != 'draft'"
        class="btn-primary"/>
```

## 工作行事曆視圖

### 檔案: [`views/working_calendar_views.xml`](../../views/working_calendar_views.xml)

#### 行事曆覆蓋設定視圖
**視圖 ID**: `tw_working_calendar_override_view_form`

```xml
<form string="工作行事曆覆蓋">
    <header>
        <button name="action_confirm" string="確認" 
                type="object" invisible="state != 'draft'" 
                class="btn-primary"/>
        <button name="action_cancel" string="取消" 
                type="object" invisible="state == 'cancelled'" 
                class="btn-danger"/>
        <field name="state" widget="statusbar"/>
    </header>
    
    <sheet>
        <group>
            <group string="基本資訊">
                <field name="calendar_id"/>
                <field name="date"/>
                <field name="override_type"/>
                <field name="reason"/>
            </group>
            
            <group string="工作設定">
                <field name="is_working_day"/>
                <field name="working_hours" invisible="not is_working_day"/>
                <field name="start_time" invisible="not is_working_day"/>
                <field name="lunch_start" invisible="not is_working_day"/>
                <field name="lunch_end" invisible="not is_working_day"/>
                <field name="end_time" invisible="not is_working_day"/>
            </group>
        </group>
        
        <group string="適用範圍">
            <field name="apply_to_all"/>
            <field name="employee_ids" invisible="apply_to_all" 
                   widget="many2many_tags"/>
        </group>
    </sheet>
</form>
```

#### 行事曆覆蓋列表視圖
**視圖 ID**: `tw_working_calendar_override_view_tree`

```xml
<list string="行事曆覆蓋設定" 
      decoration-info="state == 'draft'" 
      decoration-success="state == 'confirmed'" 
      decoration-muted="state == 'cancelled'">
    
    <field name="date"/>
    <field name="reason"/>
    <field name="override_type"/>
    <field name="is_working_day"/>
    <field name="working_hours"/>
    <field name="state"/>
    <field name="calendar_id"/>
</list>
```

## 假日匯入視圖

### 檔案: [`views/holiday_import_views.xml`](../../views/holiday_import_views.xml)

#### 假日匯入表單視圖
**視圖 ID**: `tw_holiday_import_view_form`

```xml
<form string="假日匯入">
    <header>
        <button name="action_import_holidays" string="執行匯入" 
                type="object" invisible="state != 'draft'" 
                class="btn-primary"/>
        <field name="state" widget="statusbar"/>
    </header>
    
    <sheet>
        <group>
            <group string="匯入設定">
                <field name="name"/>
                <field name="calendar_id"/>
                <field name="year"/>
                <field name="override_existing"/>
            </group>
            
            <group string="檔案設定">
                <field name="import_file" filename="filename"/>
                <field name="filename" invisible="1"/>
                <field name="has_makeup_days"/>
                <field name="makeup_day_column" invisible="not has_makeup_days"/>
            </group>
        </group>
        
        <group string="匯入結果" invisible="state == 'draft'">
            <field name="total_records" readonly="1"/>
            <field name="success_records" readonly="1"/>
            <field name="error_records" readonly="1"/>
            <field name="error_log" readonly="1" invisible="not error_log"/>
        </group>
    </sheet>
</form>
```

## 視圖安全性和權限

### 權限控制機制

#### 1. 欄位級別權限
```xml
<!-- 根據群組顯示/隱藏欄位 -->
<field name="hidden_overtime_hours" 
       groups="l10n_tw_hr_payroll.group_overtime_full_access"/>

<!-- 根據計算欄位控制顯示 -->
<field name="full_overtime_amount" 
       invisible="not can_access_full_version"/>
```

#### 2. 操作按鈕權限
```xml
<!-- 根據群組控制按鈕顯示 -->
<button name="action_approve" 
        groups="l10n_tw_hr_payroll.group_tw_payroll_manager"/>

<!-- 根據狀態和權限控制 -->
<button name="batch_approve_records" 
        invisible="not can_approve_overtime"/>
```

#### 3. 選單項目權限
```xml
<menuitem id="menu_overtime_full_access" 
          groups="l10n_tw_hr_payroll.group_overtime_full_access"/>
```

### 動態權限控制

#### 計算欄位權限
```python
@api.depends('employee_id')
def _compute_display_settings(self):
    for payslip in self:
        payslip.can_access_full_version = (
            self.env.user.has_group('l10n_tw_hr_payroll.group_overtime_full_access') or
            self.env.user.has_group('l10n_tw_hr_payroll.group_tw_payroll_super_admin')
        )
```

#### 上下文權限控制
```xml
<field name="hidden_overtime_hours" 
       invisible="context.get('hide_full_access_fields', True)"/>
```

## 視圖客製化和擴展

### 1. 視圖繼承模式
- 使用 `inherit_id` 擴展現有視圖
- 使用 `xpath` 精確定位插入點
- 保持與原始視圖的相容性

### 2. 條件顯示邏輯
- 基於狀態的條件顯示
- 基於權限的動態控制
- 基於業務邏輯的欄位可見性

### 3. 使用者體驗優化
- 狀態列顯示流程進度
- 顏色編碼區分不同狀態
- 智慧預設值和自動填充
- 批次操作支援

### 4. 響應式設計
- 支援不同螢幕尺寸
- 行動裝置友善介面
- 適應性欄位佈局

## 效能優化

### 1. 列表視圖優化
- 限制預設載入欄位
- 使用分頁減少資料量
- 智慧搜尋和篩選

### 2. 表單視圖優化
- 延遲載入非關鍵欄位
- 分頁組織複雜表單
- 快取計算欄位結果

### 3. 搜尋視圖優化
- 建立適當的資料庫索引
- 優化搜尋域表達式
- 提供常用篩選器

## 國際化支援

### 1. 多語言標籤
```xml
<field name="name" string="名稱"/>
<!-- 自動支援多語言翻譯 -->
```

### 2. 日期格式化
- 根據使用者語言環境格式化日期
- 支援不同的時區顯示

### 3. 數字格式化
- 貨幣符號本地化
- 小數點格式本地化