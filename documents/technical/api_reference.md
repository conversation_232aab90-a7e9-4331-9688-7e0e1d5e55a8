# API 參考文檔

## 概述
本文檔提供 l10n_tw_hr_payroll 模組所有模型的 API 參考，包括方法、屬性和使用範例。

**最新更新 (2025-06-17)**：
- ✅ External ID 錯誤已完全修復
- ✅ 清理 72 個無效模型引用
- ✅ 移除 12 個 External ID 錯誤引用
- ✅ 模組可正常安裝和運行

## 模型列表

### 重要說明
**tw.overtime.record 模型已移除**：在最新的架構優化中，原有的 `tw.overtime.record` 模型已完全移除，相關功能已整合到 `tw.hr.attendance` 模型中。這解決了 External ID 錯誤並簡化了系統架構。

### 1. TwHrAttendance (tw.hr.attendance) ✨ **[核心模型]**
**檔案位置**: [`models/tw_hr_attendance.py`](../../models/tw_hr_attendance.py)

#### 主要欄位
- `employee_id`: 員工 (Many2one, required)
- `department_id`: 部門 (Many2one, related)
- `check_in`: 上班打卡 (Datetime, required)
- `check_out`: 下班打卡 (Datetime)
- `worked_hours`: 工作時數 (Float, computed)
- `overtime_hours`: 加班時數 (Float, computed)
- `displayed_overtime_hours`: 顯示加班時數 (Float, computed)
- `hidden_overtime_hours`: 隱藏加班時數 (Float, computed)
- `validated_overtime_hours`: 核准加班時數 (Float)
- `overtime_status`: 加班狀態 (Selection)
- `hr_attendance_id`: 原生考勤關聯 (Many2one)
- `state`: 狀態 (Selection)
- `show_overtime_details`: 顯示加班詳情 (Boolean, computed)
- `show_hidden_overtime`: 顯示隱藏加班 (Boolean, computed)
- `notes`: 備註 (Text)

#### 方法
```python
def calculate_overtime_hours(self):
    """
    計算加班時數，支援複雜工作行事曆
    
    Returns:
        float: 計算後的加班時數
    """

def apply_overtime_limits(self):
    """
    應用加班時數限制，計算顯示和隱藏時數
    
    Returns:
        dict: 包含顯示和隱藏時數的字典
    """

def sync_from_hr_attendance(self):
    """
    從原生 hr.attendance 同步資料
    
    Returns:
        bool: 同步是否成功
    """
```

### 2. TwSyncManagement (tw.sync.management) ✨ **[同步管理]**
**檔案位置**: [`models/sync_management.py`](../../models/sync_management.py)

#### 主要欄位
- `name`: 名稱 (Char, required)

#### 方法
```python
def check_sync_status(self):
    """
    檢查同步狀態
    
    Returns:
        dict: 同步狀態報告
    """

def manual_sync_from_hr_attendance(self):
    """
    手動從 hr.attendance 同步資料
    
    Returns:
        dict: 同步結果
    """
```

### 3. HrEmployee (hr.employee 擴展)
**檔案位置**: [`models/hr_employee_extension.py`](../../models/hr_employee_extension.py)

#### 新增欄位
- `tw_employee_id`: 員工編號 (Char)
- `national_id`: 身分證字號 (Char) - 包含格式驗證
- `labor_insurance_no`: 勞保證號 (Char)
- `health_insurance_no`: 健保證號 (Char)
- `hourly_rate`: 時薪 (Monetary, computed) - 從合約月薪自動計算

#### 方法
```python
def get_taiwan_payroll_info(self):
    """
    取得員工台灣薪資相關資訊
    
    Returns:
        dict: 包含員工薪資計算所需的台灣特定資訊
    """
```

### 2. HrAttendance (hr.attendance 擴展)
**檔案位置**: [`models/hr_attendance_extension.py`](../../models/hr_attendance_extension.py)

#### 新增欄位
- `overtime_hours`: 加班時數 (Float)
- `overtime_type`: 加班類型 (Selection)
- `is_makeup_day`: 是否為補班日 (Boolean)
- `attendance_status`: 出勤狀態 (Selection)

#### 方法
```python
def calculate_overtime_hours(self):
    """
    計算加班時數
    
    Returns:
        float: 計算後的加班時數
    """

def validate_attendance_time(self):
    """
    驗證出勤時間的合理性
    
    Returns:
        bool: 驗證結果
    """
```

### 3. WorkingCalendar (working.calendar)
**檔案位置**: [`models/working_calendar.py`](../../models/working_calendar.py)

#### 主要欄位
- `name`: 行事曆名稱 (Char, required)
- `year`: 年份 (Integer, required)
- `holiday_ids`: 假日列表 (One2many)
- `makeup_day_ids`: 補班日列表 (One2many)

#### 方法
```python
def is_holiday(self, date):
    """
    檢查指定日期是否為假日
    
    Args:
        date (datetime.date): 要檢查的日期
        
    Returns:
        bool: 是否為假日
    """

def is_makeup_day(self, date):
    """
    檢查指定日期是否為補班日
    
    Args:
        date (datetime.date): 要檢查的日期
        
    Returns:
        bool: 是否為補班日
    """

def get_working_days_in_month(self, year, month):
    """
    取得指定月份的工作天數
    
    Args:
        year (int): 年份
        month (int): 月份
        
    Returns:
        int: 工作天數
    """
```

### 4. HolidayCalendar (holiday.calendar)
**檔案位置**: [`models/holiday_calendar.py`](../../models/holiday_calendar.py)

#### 主要欄位
- `name`: 假日名稱 (Char, required)
- `date`: 假日日期 (Date, required)
- `holiday_type`: 假日類型 (Selection)
- `is_compensated`: 是否有補假 (Boolean)
- `calendar_id`: 所屬行事曆 (Many2one)

#### 方法
```python
def create_makeup_day(self, makeup_date):
    """
    建立對應的補班日
    
    Args:
        makeup_date (datetime.date): 補班日期
        
    Returns:
        recordset: 建立的補班日記錄
    """
```

### 5. OvertimeManagement (overtime.management)
**檔案位置**: [`models/overtime_management.py`](../../models/overtime_management.py)

#### 主要欄位
- `employee_id`: 員工 (Many2one, required)
- `date`: 加班日期 (Date, required)
- `start_time`: 開始時間 (Datetime)
- `end_time`: 結束時間 (Datetime)
- `overtime_hours`: 加班時數 (Float)
- `overtime_type`: 加班類型 (Selection)
- `state`: 狀態 (Selection)

#### 方法
```python
def calculate_overtime_pay(self):
    """
    計算加班費
    
    Returns:
        float: 加班費金額
    """

def approve_overtime(self):
    """
    核准加班申請
    """

def reject_overtime(self):
    """
    拒絕加班申請
    """
```

### 6. HrPayslip (hr.payslip 擴展)
**檔案位置**: [`models/payslip_extension.py`](../../models/payslip_extension.py)

#### 新增欄位
- `payslip_type`: 薪資單類型 (Selection: standard, full, hidden)
- `paired_payslip_id`: 配對薪資單 (Many2one)
- `is_primary_payslip`: 主要薪資單 (Boolean)
- `displayed_overtime_hours`: 顯示加班時數 (Float)
- `hidden_overtime_hours`: 隱藏加班時數 (Float)
- `total_overtime_hours`: 總加班時數 (Float, computed)
- `basic_wage`: 基本薪資 (Monetary, related)

#### 方法
```python
def calculate_daily_overtime_pay(self, use_total_hours=False):
    """
    計算每日分段加班費
    
    Args:
        use_total_hours (bool): 是否使用總時數（完整版薪資單）
        
    Returns:
        float: 計算後的加班費總額
    """

def action_generate_dual_payslips(self):
    """
    生成雙薪資單（標準版和完整版）
    
    Returns:
        dict: 動作字典，顯示生成的薪資單
    """

def _update_overtime_from_records(self):
    """
    從加班記錄更新薪資單的加班時數
    支援多段式打卡並正確應用限制
    """
```

## 使用範例

### 計算員工月加班時數
```python
# 取得員工
employee = self.env['hr.employee'].browse(employee_id)

# 取得當月加班記錄
overtime_records = self.env['overtime.management'].search([
    ('employee_id', '=', employee.id),
    ('date', '>=', month_start),
    ('date', '<=', month_end),
    ('state', '=', 'approved')
])

# 計算總加班時數
total_overtime = sum(overtime_records.mapped('overtime_hours'))
```

### 檢查假日和補班日
```python
# 取得行事曆
calendar = self.env['working.calendar'].search([('year', '=', 2024)], limit=1)

# 檢查是否為假日
is_holiday = calendar.is_holiday(date(2024, 1, 1))

# 檢查是否為補班日
is_makeup = calendar.is_makeup_day(date(2024, 2, 17))
```

## 錯誤處理

### 常見錯誤碼
- `INVALID_TAIWAN_ID`: 無效的台灣身分證字號
- `OVERTIME_LIMIT_EXCEEDED`: 超過加班時數限制
- `INVALID_ATTENDANCE_TIME`: 無效的出勤時間

### 錯誤處理範例
```python
try:
    attendance_record.apply_overtime_limits()
except ValidationError as e:
    if 'OVERTIME_LIMIT_EXCEEDED' in str(e):
        # 處理加班時數超限
        pass
```

## 最新架構變更 (2025-06-17)

### 移除的模型
- ❌ `tw.overtime.record`: 已完全移除，功能整合到 `tw.hr.attendance`
- ❌ `model_tw_overtime_record`: 相關 External ID 已清理

### 新增的核心功能
- ✅ `tw.hr.attendance`: 台灣考勤管理核心模型
- ✅ `tw.sync.management`: 同步管理系統
- ✅ 隱藏加班功能：根據權限控制可見性
- ✅ 智慧同步機制：與原生 hr.attendance 雙向同步

### 修復成果
- ✅ 修復 `ValueError: External ID not found in the system: l10n_tw_hr_payroll.model_tw_overtime_record` 錯誤
- ✅ 清理 72 個無效模型引用
- ✅ 移除 12 個 External ID 錯誤引用
- ✅ 模組現在可以正常安裝和運行

---

**文檔版本**: 1.1
**最後更新**: 2025-06-17
**適用模組版本**: l10n_tw_hr_payroll v1.0
**修復狀態**: ✅ External ID 錯誤已修復，架構已優化