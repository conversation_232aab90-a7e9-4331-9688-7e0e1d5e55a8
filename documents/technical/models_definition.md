# 模型定義文檔

## 概述
本文檔詳細說明 l10n_tw_hr_payroll 模組中所有模型的定義、欄位、關聯關係和業務邏輯。

## 核心模型架構

### 1. 台灣考勤記錄模型 (tw.hr.attendance) ✨ **[核心模型]**
**檔案位置**: [`models/tw_hr_attendance.py`](../../models/tw_hr_attendance.py)

#### 薪資單整合 ✅ **[最新完成]**
- ✅ 修復與薪資單計算的整合問題
- ✅ 完善 check_in_date 欄位支援
- ✅ 修復加班時數計算邏輯
- ✅ 整合權限控制與薪資顯示

#### 模型定義
```python
class TwHrAttendance(models.Model):
    _name = 'tw.hr.attendance'
    _description = '台灣考勤記錄'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'check_in desc'
    _rec_name = 'display_name'
```

#### 主要欄位分類

##### 基本資訊
- `employee_id`: 員工 (Many2one, required)
- `department_id`: 部門 (Many2one, related)
- `display_name`: 顯示名稱 (Char, computed)

##### 考勤時間
- `check_in`: 上班打卡 (Datetime, required)
- `check_out`: 下班打卡 (Datetime)
- `worked_hours`: 工作時數 (Float, computed)

##### 加班相關欄位
- `overtime_hours`: 加班時數 (Float, computed)
- `displayed_overtime_hours`: 顯示加班時數 (Float, computed)
- `hidden_overtime_hours`: 隱藏加班時數 (Float, computed)
- `validated_overtime_hours`: 核准加班時數 (Float, computed)
- `overtime_status`: 加班狀態 (Selection: to_approve, approved, refused)

##### 權限控制欄位
- `show_overtime_details`: 顯示加班詳情 (Boolean, computed)
- `show_hidden_overtime`: 顯示隱藏加班 (Boolean, computed)

##### 原生考勤關聯
- `hr_attendance_id`: 原生考勤記錄 (Many2one)
- `check_in_date`: 打卡日期 (Date, computed) **[新增支援]**

##### 狀態和備註
- `state`: 狀態 (Selection: draft, confirmed, validated)
- `notes`: 備註 (Text)

#### 核心計算邏輯

##### 加班時數計算
```python
@api.depends('check_in', 'check_out', 'employee_id')
def _compute_overtime_hours(self):
    """計算加班時數 - 參考官方 hr.attendance 邏輯
    
    使用員工工作行事曆計算預期工作時間
    支援複雜的工作時間安排和休息時間
    """
```

##### 隱藏加班邏輯
```python
@api.depends('overtime_hours', 'employee_id', 'check_in')
def _compute_displayed_overtime_hours(self):
    """計算顯示加班時數 - 根據員工限制設定計算"""

@api.depends('overtime_hours', 'displayed_overtime_hours')
def _compute_hidden_overtime_hours(self):
    """計算隱藏加班時數 - 只有主管可見部分"""
```

##### 權限控制
```python
@api.depends('employee_id')
def _compute_show_overtime_details(self):
    """根據權限控制加班詳情的可見性"""

@api.depends('employee_id')
def _compute_show_hidden_overtime(self):
    """根據權限控制隱藏加班時數的可見性"""
```

#### 同步機制
```python
def sync_to_hr_attendance(self):
    """同步到原生 hr.attendance"""

@api.model
def create_from_hr_attendance(self, hr_attendance):
    """從原生 hr.attendance 創建 tw.hr.attendance 記錄"""
```

#### 批量操作
```python
def action_approve_overtime(self):
    """批量核准加班 - 用於列表視圖的批量操作"""

def action_refuse_overtime(self):
    """批量拒絕加班 - 用於列表視圖的批量操作"""
```

### 2. 同步管理模型 (tw.sync.management) ✨ **[新增]**
**檔案位置**: [`models/sync_management.py`](../../models/sync_management.py)

#### 模型定義
```python
class SyncManagement(models.TransientModel):
    _name = 'tw.sync.management'
    _description = '同步管理'
```

#### 主要功能
```python
@api.model
def check_sync_status(self, *args, **kwargs):
    """檢查同步狀態"""

@api.model
def manual_sync_from_hr_attendance(self, *args, **kwargs):
    """手動同步所有未同步的 hr.attendance 記錄"""
```

### 3. 員工擴展模型 (hr.employee)
**檔案位置**: [`models/hr_employee_extension.py`](../../models/hr_employee_extension.py)

#### 繼承關係
```python
class HrEmployee(models.Model):
    _inherit = 'hr.employee'
```

#### 主要欄位分類

##### 台灣特有欄位
- `tw_employee_id`: 員工編號 (Char)
- `national_id`: 身分證字號 (Char) - 包含格式驗證
- `labor_insurance_no`: 勞保證號 (Char)
- `health_insurance_no`: 健保證號 (Char)

##### 薪資相關欄位
- `hourly_rate`: 時薪 (Monetary, computed) - 從合約月薪自動計算

##### 工作時間相關
- `personal_calendar_id`: 個人工作行事曆 (Many2one)
- `standard_working_hours`: 標準工作時數 (Float, default=8.0)
- `weekly_working_hours`: 每週工作時數 (Float, default=40.0)

##### 統計欄位
- `current_month_overtime`: 本月加班時數 (Float, computed)
- `current_month_displayed_overtime`: 本月顯示加班時數 (Float, computed)
- `current_month_hidden_overtime`: 本月隱藏加班時數 (Float, computed)

#### 重要方法

##### 計算方法
```python
@api.depends('contract_id.wage', 'standard_working_hours')
def _compute_hourly_rate(self):
    """從合約計算時薪 = 合約月薪 / 30天 / 每日工作時數"""
```

##### 業務邏輯方法
```python
def get_working_calendar(self):
    """取得員工的工作行事曆"""

def is_working_day(self, date):
    """檢查指定日期對該員工是否為工作日"""

def get_overtime_rate_for_date(self, date, hours=0):
    """取得指定日期的加班費率 - 從工作行事曆取得"""

def create_overtime_record(self, date, hours, overtime_type='weekday', description=''):
    """創建加班記錄"""
```

#### 約束條件
```python
@api.constrains('national_id')
def _check_national_id(self):
    """驗證身分證字號格式 (1英文字母 + 9數字) 和唯一性"""
```

### 4. 加班記錄模型 (tw.overtime.record)
**檔案位置**: [`models/overtime_management.py`](../../models/overtime_management.py)

#### 模型定義
```python
class TwOvertimeRecord(models.Model):
    _name = 'tw.overtime.record'
    _description = '加班記錄'
    _order = 'date desc, employee_id'
```

#### 主要欄位

##### 基本資訊
- `name`: 記錄名稱 (Char, computed)
- `employee_id`: 員工 (Many2one, required)
- `date`: 加班日期 (Date, required)

##### 出勤整合
- `attendance_id`: 關聯出勤記錄 (Many2one)
- `auto_created`: 自動創建標記 (Boolean)
- `source_type`: 來源類型 (Selection)

##### 加班時數
- `actual_overtime_hours`: 實際加班時數 (Float, required)
- `displayed_overtime_hours`: 顯示加班時數 (Float, computed)
- `hidden_overtime_hours`: 隱藏加班時數 (Float, computed)

##### 審核流程
- `state`: 狀態 (Selection: draft, submitted, approved, rejected)
- `approved_by`: 核准人 (Many2one)
- `approved_date`: 核准時間 (Datetime)

#### 核心業務邏輯

##### 顯示時數計算
```python
@api.depends('actual_overtime_hours', 'employee_id', 'date', 'auto_created')
def _compute_displayed_hours(self):
    """根據限制設定計算顯示和隱藏的加班時數"""
```

##### 批次處理
```python
def batch_approve_records(self):
    """批量核准加班記錄 - 包含原子性事務處理"""

def batch_reject_records(self, reject_reason=None):
    """批量拒絕加班記錄"""
```

##### 出勤整合
```python
@api.model
def create_from_attendance(self, attendance_id):
    """從 hr.attendance 記錄創建加班記錄"""
```

### 4. 加班限制模型 (tw.overtime.limit)
**檔案位置**: [`models/overtime_management.py`](../../models/overtime_management.py)

#### 模型定義
```python
class TwOvertimeLimit(models.Model):
    _name = 'tw.overtime.limit'
    _description = '加班時數限制設定'
    _order = 'priority desc, id desc'
```

#### 主要欄位

##### 限制設定
- `monthly_limit`: 每月加班時數上限 (Float, default=46.0)
- `display_limit`: 顯示時數上限 (Float, default=30.0)
- `auto_approve`: 自動審核 (Boolean)
- `auto_approve_limit`: 自動審核時數上限 (Float, default=2.0)

##### 適用範圍
- `department_ids`: 適用部門 (Many2many)
- `employee_ids`: 適用員工 (Many2many)
- `apply_to_all`: 套用至全公司 (Boolean)

##### 優先級和時效
- `priority`: 優先級 (Integer, default=10)
- `date_from`: 開始日期 (Date)
- `date_to`: 結束日期 (Date)

#### 核心方法
```python
def get_applicable_limit_for_employee(self, employee_id, date=None):
    """取得適用於指定員工的加班限制
    
    優先順序：特定員工 > 部門設定 > 全公司設定
    """
```

### 5. 薪資單擴展模型 (hr.payslip) ✅ **[修復完成]**
**檔案位置**: [`models/payslip_extension.py`](../../models/payslip_extension.py)

#### 繼承關係
```python
class HrPayslip(models.Model):
    _inherit = 'hr.payslip'
```

#### 薪資單計算修復 ✅ **[最新完成]**
- ✅ 修復 compute_sheet 功能
- ✅ 完善加班費計算邏輯
- ✅ 修復標準版與完整版薪資單差異計算
- ✅ 整合 tw.hr.attendance 考勤資料

#### 雙薪資單功能

##### 薪資單類型
- `payslip_type`: 薪資單類型 (Selection: standard, full, hidden)
- `paired_payslip_id`: 配對薪資單 (Many2one)
- `is_primary_payslip`: 主要薪資單 (Boolean)

##### 時數欄位
- `displayed_overtime_hours`: 顯示加班時數 (Float)
- `hidden_overtime_hours`: 隱藏加班時數 (Float)
- `total_overtime_hours`: 總加班時數 (Float, computed)

##### 加班費計算（已移除 - 交由 salary rule 處理）
- 移除了所有加班費計算相關欄位
- 加班費計算邏輯已轉移至 [`data/payroll_structure_data.xml`](../../data/payroll_structure_data.xml) 中的 salary rule
- 新增 `calculate_daily_overtime_pay()` 方法供 salary rule 調用
- ✅ 修復每日分段加班費計算邏輯

#### 核心功能

##### 雙薪資單生成
```python
def action_generate_dual_payslips(self):
    """生成雙薪資單 - 標準版和完整版"""

@api.model
def create_batch_dual_payslips(self, employee_ids, date_from, date_to):
    """批量創建雙薪資單"""
```

##### 每日分段加班費計算
```python
def calculate_daily_overtime_pay(self, use_total_hours=False):
    """計算每日分段加班費
    
    Args:
        use_total_hours (bool): 是否使用總時數（完整版薪資單）
        
    Returns:
        float: 計算後的加班費總額
    """
    # 按日期分組計算加班記錄
    # 每日獨立進行分段計算：前2小時1.34倍，後續1.67倍
    # 支援標準版（顯示時數）和完整版（實際時數）
```

### 6. 工作行事曆模型
**檔案位置**: [`models/working_calendar.py`](../../models/working_calendar.py)

#### 覆蓋設定模型 (tw.working.calendar.override)
```python
class TwWorkingCalendarOverride(models.Model):
    _name = 'tw.working.calendar.override'
    _description = '工作行事曆覆蓋設定'
```

##### 主要欄位
- `calendar_id`: 工作行事曆 (Many2one, required)
- `date`: 日期 (Date, required)
- `is_working_day`: 是否為工作日 (Boolean)
- `override_type`: 覆蓋類型 (Selection: holiday, makeup, special)
- `working_hours`: 工作時數 (Float, default=8.0)

##### 核心功能
```python
def action_confirm(self):
    """確認覆蓋設定 - 創建實際的工作時間覆蓋"""

def _create_saturday_special_attendance(self):
    """為週六補班創建特殊出勤記錄"""
```

#### 資源行事曆擴展 (resource.calendar)
```python
class ResourceCalendar(models.Model):
    _inherit = 'resource.calendar'
```

##### 台灣特有設定
- `is_tw_calendar`: 台灣行事曆 (Boolean)
- `tw_override_ids`: 覆蓋設定 (One2many)
- `default_daily_hours`: 預設每日工作時數 (Float, default=8.0)
- `default_weekly_hours`: 預設每週工作時數 (Float, default=40.0)

##### 加班費率設定
- `tw_overtime_rate`: 加班費率 (Float, default=1.34) - 平日加班前2小時
- `tw_extended_overtime_rate`: 延長加班費率 (Float, default=1.67) - 平日加班2小時後
- `tw_holiday_overtime_rate`: 假日加班費率 (Float, default=2.0) - 假日加班

##### 核心方法
```python
def get_effective_working_hours(self, date):
    """取得指定日期的有效工作時數"""

def is_working_day(self, date):
    """檢查指定日期是否為工作日"""
```

### 7. 假日管理模型
**檔案位置**: [`models/holiday_calendar.py`](../../models/holiday_calendar.py)

#### 假日匯入模型 (tw.holiday.import)
```python
class TwHolidayImport(models.Model):
    _name = 'tw.holiday.import'
    _description = '台灣假日匯入'
```

##### 匯入設定
- `import_file`: 匯入檔案 (Binary, required)
- `calendar_id`: 工作行事曆 (Many2one, required)
- `year`: 年度 (Integer, required)
- `override_existing`: 覆蓋現有假日 (Boolean)

##### 處理結果
- `total_records`: 總記錄數 (Integer)
- `success_records`: 成功記錄數 (Integer)
- `error_records`: 錯誤記錄數 (Integer)
- `error_log`: 錯誤日誌 (Text)

##### 核心功能
```python
def action_import_holidays(self):
    """執行假日匯入 - 支援 CSV 和 Excel 格式"""

def _process_holiday_data(self, df):
    """處理假日資料 - 包含補班日處理"""
```

## 模型關聯關係圖

```
hr.employee (員工)
    ├── tw.hr.attendance (台灣考勤記錄) [One2many] **[新增核心關聯]**
    ├── ~~tw.overtime.record (加班記錄) [One2many]~~ **[已移除]**
    ├── hr.attendance (出勤記錄) [One2many]
    ├── hr.payslip (薪資單) [One2many]
    └── resource.calendar (工作行事曆) [Many2one]

tw.hr.attendance (台灣考勤記錄) **[核心模型]**
    ├── hr.employee (員工) [Many2one]
    ├── hr.department (部門) [Many2one]
    ├── hr.attendance (原生考勤關聯) [Many2one]
    └── tw.overtime.limit (加班限制) [透過計算取得]

hr.attendance (原生考勤記錄)
    ├── hr.employee (員工) [Many2one]
    └── tw.hr.attendance (台灣考勤) [One2one] **[反向關聯]**

tw.sync.management (同步管理) **[新增]**
    └── 管理 tw.hr.attendance 與 hr.attendance 的同步

~~tw.overtime.record (加班記錄)~~ **[已移除 - 功能整合到 tw.hr.attendance]**
    ~~├── hr.employee (員工) [Many2one]~~
    ├── hr.attendance (出勤記錄) [Many2one]
    └── hr.payslip (薪資單) [Many2one]

hr.payslip (薪資單)
    ├── hr.employee (員工) [Many2one]
    ├── hr.payslip (配對薪資單) [Many2one]
    └── ~~tw.overtime.record (加班記錄) [One2many]~~ **[已移除]**

resource.calendar (工作行事曆)
    ├── tw.working.calendar.override (覆蓋設定) [One2many]
    └── resource.calendar.leaves (假日) [One2many]

tw.overtime.limit (加班限制)
    ├── hr.department (部門) [Many2many]
    └── hr.employee (員工) [Many2many]
```

### 架構變更說明

#### 新增核心模型
- **tw.hr.attendance**: 台灣專屬考勤模型，整合考勤和加班管理
- **tw.sync.management**: 同步管理工具，確保資料一致性

#### 移除的模型
- **hr.attendance 擴展**: 移除複雜的擴展邏輯，改用專屬模型
- **複雜的循環依賴**: 簡化模型間的關聯關係

#### 關聯關係優化
- **統一考勤管理**: tw.hr.attendance 成為考勤管理的核心
- **簡化同步邏輯**: 透過 tw.sync.management 統一管理同步
- **清晰的權限控制**: 在 tw.hr.attendance 中實現完整的權限邏輯

## 資料流程

### 1. 出勤到加班記錄流程
```
hr.attendance (打卡) 
    → 計算 tw_overtime_hours
    → ~~自動創建 tw.overtime.record~~ **[已移除 - 直接在 tw.hr.attendance 中處理]**
    → 審核流程
    → 納入 hr.payslip 計算
```

### 2. 薪資計算流程
```
~~tw.overtime.record (加班記錄)~~ **[已移除]**
    → ~~計算 displayed_overtime_hours / hidden_overtime_hours~~ **[功能移至 tw.hr.attendance]**
    → hr.payslip (薪資單) 
    → 生成雙薪資單 (standard / full)
    → 計算不同版本的加班費
```

### 3. 權限控制流程
```
使用者權限檢查
    → 決定可見的加班時數
    → 控制薪資單版本存取
    → 限制操作權限
```

## 設計模式

### 1. 策略模式 (Strategy Pattern)
- 加班費率計算：根據日期類型使用不同費率
- 時數顯示：根據權限顯示不同時數

### 2. 工廠模式 (Factory Pattern)
- 加班記錄創建：根據來源類型創建不同記錄
- 薪資單生成：根據類型生成不同版本

### 3. 觀察者模式 (Observer Pattern)
- 出勤記錄變更自動觸發加班記錄更新
- 加班記錄變更自動更新薪資單

### 4. 命令模式 (Command Pattern)
- 批次操作：審核、拒絕、創建等批次命令
- 嚮導操作：封裝複雜的業務流程

## 效能考量

### 1. 計算欄位優化
- 使用 `store=True` 儲存計算結果
- 避免循環依賴的計算邏輯

### 2. 查詢優化
- 使用適當的索引
- 批次查詢減少資料庫存取

### 3. 快取機制
- 權限檢查結果快取
- 行事曆資料快取

## 擴展性設計

### 1. 模組化架構
- 每個功能模組獨立
- 清晰的介面定義

### 2. 配置驅動
- 加班限制可配置
- 費率計算可調整

### 3. 多公司支援
- 所有模型支援多公司
- 權限按公司隔離

## 薪資單整合修復記錄 ✅ **[最新完成]**

### 修復內容
1. **compute_sheet 功能修復**
   - 修復薪資單計算邏輯錯誤
   - 完善與 tw.hr.attendance 的資料整合
   - 修復 check_in_date 欄位相關問題

2. **加班費計算改進**
   - 修復每日分段加班費計算
   - 完善前2小時1.34倍，後續1.67倍的費率計算
   - 修復標準版與完整版薪資單的差異計算

3. **權限控制整合**
   - 整合權限控制與薪資顯示邏輯
   - 確保隱藏加班功能在薪資單中正常運作
   - 完善不同權限用戶的薪資單內容控制

### 相關檔案
- [`models/payslip_extension.py`](../../models/payslip_extension.py) - 薪資單擴展模型
- [`models/tw_hr_attendance.py`](../../models/tw_hr_attendance.py) - 考勤模型
- [`data/payroll_structure_data.xml`](../../data/payroll_structure_data.xml) - 薪資結構資料
- [`models/hr_employee_extension.py`](../../models/hr_employee_extension.py) - 員工資料擴展

### 待測試項目
1. 薪資單 compute_sheet 功能測試
2. 加班費計算結果驗證
3. 標準版 vs 完整版薪資單差異測試
4. 整合測試驗證