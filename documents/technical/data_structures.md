# 資料結構文檔

## 概述
本文檔詳細說明 l10n_tw_hr_payroll 模組的資料結構、初始資料和資料關聯關係。

## 資料檔案結構

### 資料檔案概覽
```
data/
├── holiday_data.xml           # 假日基礎資料
├── payroll_structure_data.xml # 薪資結構資料
└── attendance_cron_data.xml   # 定時任務資料
```

## 1. 假日基礎資料

### 檔案: [`data/holiday_data.xml`](../../data/holiday_data.xml)

#### 台灣國定假日資料結構
```xml
<!-- 假日類型定義 -->
<record id="holiday_type_national" model="resource.calendar.leaves.type">
    <field name="name">國定假日</field>
    <field name="code">NATIONAL</field>
    <field name="color">red</field>
</record>

<record id="holiday_type_traditional" model="resource.calendar.leaves.type">
    <field name="name">民俗節日</field>
    <field name="code">TRADITIONAL</field>
    <field name="color">orange</field>
</record>

<!-- 2024年國定假日範例 -->
<record id="holiday_2024_new_year" model="resource.calendar.leaves">
    <field name="name">元旦</field>
    <field name="date_from">2024-01-01</field>
    <field name="date_to">2024-01-01</field>
    <field name="is_tw_holiday" eval="True"/>
    <field name="holiday_type">national</field>
</record>

<record id="holiday_2024_lunar_new_year_eve" model="resource.calendar.leaves">
    <field name="name">農曆除夕</field>
    <field name="date_from">2024-02-09</field>
    <field name="date_to">2024-02-09</field>
    <field name="is_tw_holiday" eval="True"/>
    <field name="holiday_type">traditional</field>
</record>
```

#### 假日資料結構說明

##### 假日記錄欄位
| 欄位名稱 | 資料類型 | 說明 | 範例 |
|----------|----------|------|------|
| `name` | Char | 假日名稱 | "元旦" |
| `date_from` | Date | 假日開始日期 | 2024-01-01 |
| `date_to` | Date | 假日結束日期 | 2024-01-01 |
| `is_tw_holiday` | Boolean | 是否為台灣假日 | True |
| `holiday_type` | Selection | 假日類型 | "national" |
| `makeup_date` | Date | 對應補班日期 | 2024-02-17 |

##### 假日類型分類
- **national**: 國定假日（如元旦、國慶日）
- **traditional**: 民俗節日（如農曆新年、中秋節）
- **special**: 特殊假日（如颱風假、臨時假日）
- **makeup**: 補假（調整後的假日）

#### 補班日資料結構
```xml
<!-- 補班日覆蓋設定 -->
<record id="makeup_2024_0217" model="tw.working.calendar.override">
    <field name="name">農曆新年補班</field>
    <field name="date">2024-02-17</field>
    <field name="is_working_day" eval="True"/>
    <field name="reason">因農曆新年調整</field>
    <field name="override_type">makeup</field>
    <field name="working_hours">8.0</field>
    <field name="start_time">9.0</field>
    <field name="lunch_start">12.0</field>
    <field name="lunch_end">13.0</field>
    <field name="end_time">18.0</field>
</record>
```

## 2. 薪資結構資料

### 檔案: [`data/payroll_structure_data.xml`](../../data/payroll_structure_data.xml)

#### 台灣薪資結構定義
```xml
<!-- 薪資結構類型 -->
<record id="structure_type_tw_employee" model="hr.payroll.structure.type">
    <field name="name">台灣員工</field>
    <field name="country_id" ref="base.tw"/>
</record>

<!-- 基本薪資結構 -->
<record id="structure_tw_employee_salary" model="hr.payroll.structure">
    <field name="name">台灣員工薪資</field>
    <field name="type_id" ref="structure_type_tw_employee"/>
    <field name="country_id" ref="base.tw"/>
</record>
```

#### 薪資規則定義

##### 基本薪資規則
```xml
<!-- 基本薪資 -->
<record id="rule_tw_basic_salary" model="hr.salary.rule">
    <field name="name">基本薪資</field>
    <field name="code">BASIC</field>
    <field name="category_id" ref="hr_payroll.ALW"/>
    <field name="condition_select">none</field>
    <field name="amount_select">code</field>
    <field name="amount_python_compute">
result = contract.wage
    </field>
    <field name="struct_id" ref="structure_tw_employee_salary"/>
    <field name="sequence">100</field>
</record>

<!-- 標準加班費規則 -->
<record id="rule_tw_overtime_standard" model="hr.salary.rule">
    <field name="name">加班費 (標準)</field>
    <field name="code">OT_STD</field>
    <field name="category_id" ref="hr_payroll.ALW"/>
    <field name="condition_select">python</field>
    <field name="condition_python">
try:
    result = payslip.payslip_type != 'full'
except:
    result = True
    </field>
    <field name="amount_select">code</field>
    <field name="amount_python_compute"><![CDATA[
# 標準加班費計算 - 使用每日分段計算方法
try:
    result = payslip.calculate_daily_overtime_pay(use_total_hours=False)
except:
    # 備用計算邏輯
    overtime_hours = payslip.displayed_overtime_hours or 0
    if overtime_hours > 0 and contract.wage:
        hourly_rate = contract.wage / 30 / 8
        if overtime_hours <= 2:
            result = overtime_hours * hourly_rate * 1.34
        else:
            result = 2 * hourly_rate * 1.34 + (overtime_hours - 2) * hourly_rate * 1.67
    else:
        result = 0
]]></field>
    <field name="struct_id" ref="structure_tw_employee_salary"/>
    <field name="sequence">200</field>
</record>

<!-- 完整加班費規則 -->
<record id="rule_tw_overtime_full" model="hr.salary.rule">
    <field name="name">加班費 (完整)</field>
    <field name="code">OT_FULL</field>
    <field name="category_id" ref="hr_payroll.ALW"/>
    <field name="condition_select">python</field>
    <field name="condition_python">
try:
    result = payslip.payslip_type == 'full'
except:
    result = False
    </field>
    <field name="amount_select">code</field>
    <field name="amount_python_compute"><![CDATA[
# 完整版薪資單使用每日分段計算方法（使用總時數）
try:
    result = payslip.calculate_daily_overtime_pay(use_total_hours=True)
except:
    # 備用計算邏輯
    overtime_hours = payslip.total_overtime_hours or 0
    if overtime_hours > 0 and contract.wage:
        hourly_rate = contract.wage / 30 / 8
        if overtime_hours <= 2:
            result = overtime_hours * hourly_rate * 1.34
        else:
            result = 2 * hourly_rate * 1.34 + (overtime_hours - 2) * hourly_rate * 1.67
    else:
        result = 0
]]></field>
    <field name="struct_id" ref="structure_tw_employee_salary"/>
    <field name="sequence">201</field>
</record>
```

##### 扣除項目規則
```xml
<!-- 勞保費 -->
<record id="rule_tw_labor_insurance" model="hr.salary.rule">
    <field name="name">勞保費</field>
    <field name="code">LABOR_INS</field>
    <field name="category_id" ref="hr_payroll.DED"/>
    <field name="condition_select">none</field>
    <field name="amount_select">code</field>
    <field name="amount_python_compute">
# 勞保費計算 (員工負擔20%)
insurable_salary = min(contract.wage, 45800)  # 勞保投保薪資上限
labor_insurance_rate = 0.115  # 勞保費率11.5%
employee_ratio = 0.2  # 員工負擔比例20%

result = -(insurable_salary * labor_insurance_rate * employee_ratio)
    </field>
    <field name="struct_id" ref="structure_tw_employee_salary"/>
    <field name="sequence">300</field>
</record>

<!-- 健保費 -->
<record id="rule_tw_health_insurance" model="hr.salary.rule">
    <field name="name">健保費</field>
    <field name="code">HEALTH_INS</field>
    <field name="category_id" ref="hr_payroll.DED"/>
    <field name="condition_select">none</field>
    <field name="amount_select">code</field>
    <field name="amount_python_compute">
# 健保費計算 (員工負擔30%)
insurable_salary = min(contract.wage, 182000)  # 健保投保薪資上限
health_insurance_rate = 0.0517  # 健保費率5.17%
employee_ratio = 0.3  # 員工負擔比例30%

result = -(insurable_salary * health_insurance_rate * employee_ratio)
    </field>
    <field name="struct_id" ref="structure_tw_employee_salary"/>
    <field name="sequence">310</field>
</record>

<!-- 勞退金 -->
<record id="rule_tw_pension_fund" model="hr.salary.rule">
    <field name="name">勞退金</field>
    <field name="code">PENSION</field>
    <field name="category_id" ref="hr_payroll.DED"/>
    <field name="condition_select">none</field>
    <field name="amount_select">code</field>
    <field name="amount_python_compute">
# 勞退金計算 (雇主提繳6%，員工可自提0-6%)
pension_salary = min(contract.wage, 150000)  # 勞退月提繳工資上限
employee_contribution_rate = 0.06  # 員工自提6%

result = -(pension_salary * employee_contribution_rate)
    </field>
    <field name="struct_id" ref="structure_tw_employee_salary"/>
    <field name="sequence">320</field>
</record>
```

#### 薪資類別定義
```xml
<!-- 薪資類別 -->
<record id="category_tw_allowance" model="hr.salary.rule.category">
    <field name="name">津貼</field>
    <field name="code">ALW_TW</field>
    <field name="parent_id" ref="hr_payroll.ALW"/>
</record>

<record id="category_tw_deduction" model="hr.salary.rule.category">
    <field name="name">法定扣除</field>
    <field name="code">DED_TW</field>
    <field name="parent_id" ref="hr_payroll.DED"/>
</record>

<record id="category_tw_tax" model="hr.salary.rule.category">
    <field name="name">稅金</field>
    <field name="code">TAX_TW</field>
    <field name="parent_id" ref="hr_payroll.DED"/>
</record>
```

## 3. 定時任務資料

### 檔案: [`data/attendance_cron_data.xml`](../../data/attendance_cron_data.xml)

#### 出勤處理定時任務
```xml
<!-- 處理未處理的出勤記錄 -->
<record id="cron_process_unprocessed_attendances" model="ir.cron">
    <field name="name">處理未處理的出勤記錄</field>
    <field name="model_id" ref="model_hr_attendance"/>
    <field name="state">code</field>
    <field name="code">model._cron_process_unprocessed_attendances()</field>
    <field name="interval_number">1</field>
    <field name="interval_type">days</field>
    <field name="numbercall">-1</field>
    <field name="active" eval="True"/>
    <field name="doall" eval="False"/>
</record>

<!-- 自動創建加班記錄 -->
<record id="cron_auto_create_overtime_records" model="ir.cron">
    <field name="name">自動創建加班記錄</field>
    <field name="model_id" ref="model_tw_overtime_record"/>
    <field name="state">code</field>
    <field name="code">model._cron_auto_create_from_attendance()</field>
    <field name="interval_number">1</field>
    <field name="interval_type">days</field>
    <field name="numbercall">-1</field>
    <field name="active" eval="True"/>
    <field name="doall" eval="False"/>
</record>
```

#### 定時任務配置說明

##### 任務執行時間配置
| 任務名稱 | 執行頻率 | 執行時間 | 說明 |
|----------|----------|----------|------|
| 處理未處理出勤 | 每日 | 凌晨 2:00 | 處理前一日未完成的出勤記錄 |
| 自動創建加班記錄 | 每日 | 凌晨 3:00 | 從出勤記錄自動創建加班記錄 |
| 薪資計算提醒 | 每月 | 25日 09:00 | 提醒薪資計算作業 |

##### 任務參數說明
- `interval_number`: 執行間隔數量
- `interval_type`: 執行間隔類型 (minutes, hours, days, weeks, months)
- `numbercall`: 執行次數 (-1 表示無限次)
- `active`: 是否啟用
- `doall`: 是否執行所有錯過的任務

## 4. 加班限制預設資料

### 預設加班限制設定
```xml
<!-- 全公司預設加班限制 -->
<record id="default_overtime_limit_company" model="tw.overtime.limit">
    <field name="name">全公司預設加班限制</field>
    <field name="monthly_limit">46.0</field>
    <field name="display_limit">30.0</field>
    <field name="auto_approve" eval="True"/>
    <field name="auto_approve_limit">2.0</field>
    <field name="apply_to_all" eval="True"/>
    <field name="priority">1</field>
    <field name="active" eval="True"/>
</record>

<!-- 管理階層加班限制 -->
<record id="overtime_limit_management" model="tw.overtime.limit">
    <field name="name">管理階層加班限制</field>
    <field name="monthly_limit">60.0</field>
    <field name="display_limit">60.0</field>
    <field name="auto_approve" eval="True"/>
    <field name="auto_approve_limit">4.0</field>
    <field name="apply_to_all" eval="False"/>
    <field name="priority">10</field>
    <field name="active" eval="True"/>
</record>
```

## 5. 資料關聯關係

### 主要資料關聯圖
```mermaid
erDiagram
    EMPLOYEE ||--o{ OVERTIME_RECORD : has
    EMPLOYEE ||--o{ ATTENDANCE : has
    EMPLOYEE ||--o{ PAYSLIP : has
    EMPLOYEE }o--|| CALENDAR : uses
    
    OVERTIME_RECORD ||--o| ATTENDANCE : linked_to
    OVERTIME_RECORD }o--|| PAYSLIP : included_in
    OVERTIME_RECORD }o--|| OVERTIME_LIMIT : governed_by
    
    CALENDAR ||--o{ CALENDAR_OVERRIDE : has
    CALENDAR ||--o{ HOLIDAY : contains
    
    PAYSLIP ||--o| PAYSLIP : paired_with
    PAYSLIP ||--o{ PAYSLIP_LINE : contains
    
    OVERTIME_LIMIT }o--o{ DEPARTMENT : applies_to
    OVERTIME_LIMIT }o--o{ EMPLOYEE : applies_to
```

### 資料流向關係

#### 1. 出勤到薪資流程
```
hr.attendance (出勤記錄)
    ↓ 計算加班時數
tw.hr.attendance (原 tw.overtime.record 已移除) (加班記錄)
    ↓ 審核通過
hr.payslip (薪資單)
    ↓ 計算薪資
hr.payslip.line (薪資明細)
```

#### 2. 權限控制流程
```
res.users (使用者)
    ↓ 檢查群組
res.groups (權限群組)
    ↓ 決定可見欄位
tw.hr.attendance (原 tw.overtime.record 已移除).displayed_overtime_hours (顯示時數)
```

#### 3. 限制設定流程
```
tw.overtime.limit (加班限制)
    ↓ 套用到
hr.employee (員工) / hr.department (部門)
    ↓ 控制
tw.hr.attendance (原 tw.overtime.record 已移除).displayed_overtime_hours (顯示時數)
```

## 6. 資料驗證規則

### 資料完整性約束

#### 1. 員工資料約束
```python
# 身分證字號唯一性
@api.constrains('national_id')
def _check_national_id_unique(self):
    for employee in self:
        if employee.national_id:
            existing = self.search([
                ('national_id', '=', employee.national_id),
                ('id', '!=', employee.id)
            ])
            if existing:
                raise ValidationError('身分證字號不能重複')
```

#### 2. 加班記錄約束
```python
# 加班時數合理性檢查
@api.constrains('actual_overtime_hours')
def _check_overtime_hours_reasonable(self):
    for record in self:
        if record.actual_overtime_hours < 0:
            raise ValidationError('加班時數不能為負數')
        if record.actual_overtime_hours > 12:
            raise ValidationError('單日加班時數不能超過12小時')
```

#### 3. 薪資單約束
```python
# 薪資期間合理性檢查
@api.constrains('date_from', 'date_to')
def _check_payslip_period(self):
    for payslip in self:
        if payslip.date_from >= payslip.date_to:
            raise ValidationError('薪資期間開始日期必須早於結束日期')
```

### 業務邏輯約束

#### 1. 加班限制邏輯
```python
# 月度加班時數限制檢查
def _check_monthly_overtime_limit(self):
    limit_setting = self._get_applicable_limit()
    if limit_setting:
        monthly_total = self._calculate_monthly_overtime()
        if monthly_total > limit_setting.monthly_limit:
            # 記錄警告但不阻止
            _logger.warning(f'員工 {self.employee_id.name} 月度加班超限')
```

#### 2. 權限邏輯約束
```python
# 顯示時數計算邏輯
def _compute_displayed_hours(self):
    for record in self:
        if self.env.user.has_group('group_overtime_full_access'):
            record.displayed_overtime_hours = record.actual_overtime_hours
        else:
            # 根據限制設定計算可顯示時數
            record.displayed_overtime_hours = self._calculate_displayable_hours()
```

## 7. 資料遷移和升級

### 資料遷移腳本結構
```python
def migrate(cr, version):
    """模組升級時的資料遷移"""
    if not version:
        return
    
    # 版本 1.0 到 1.1 的遷移
    if version < '1.1':
        _migrate_overtime_records_v1_1(cr)
    
    # 版本 1.1 到 1.2 的遷移
    if version < '1.2':
        _migrate_payslip_structure_v1_2(cr)

def _migrate_overtime_records_v1_1(cr):
    """遷移加班記錄資料結構"""
    # 添加新欄位的預設值
    cr.execute("""
        UPDATE tw_overtime_record 
        SET source_type = 'manual' 
        WHERE source_type IS NULL
    """)
```

### 資料備份和恢復

#### 1. 關鍵資料備份
```sql
-- 備份加班記錄
CREATE TABLE tw_overtime_record_backup AS 
SELECT * FROM tw_overtime_record;

-- 備份薪資單資料
CREATE TABLE hr_payslip_backup AS 
SELECT * FROM hr_payslip WHERE payslip_type IN ('standard', 'full');
```

#### 2. 資料一致性檢查
```python
def check_data_consistency(self):
    """檢查資料一致性"""
    # 檢查孤立的加班記錄
    orphaned_overtime = self.env['tw.hr.attendance (原 tw.overtime.record 已移除)'].search([
        ('attendance_id', '!=', False),
        ('attendance_id.id', 'not in', 
         self.env['hr.attendance'].search([]).ids)
    ])
    
    if orphaned_overtime:
        _logger.warning(f'發現 {len(orphaned_overtime)} 筆孤立的加班記錄')
```

## 8. 效能優化資料結構

### 資料庫索引建議
```sql
-- 加班記錄查詢優化
CREATE INDEX idx_overtime_record_employee_date 
ON tw_overtime_record(employee_id, date);

CREATE INDEX idx_overtime_record_state_date 
ON tw_overtime_record(state, date);

-- 出勤記錄查詢優化
CREATE INDEX idx_attendance_employee_checkin 
ON hr_attendance(employee_id, check_in);

-- 薪資單查詢優化
CREATE INDEX idx_payslip_employee_period 
ON hr_payslip(employee_id, date_from, date_to);
```

### 資料分割策略
```sql
-- 按年份分割加班記錄表
CREATE TABLE tw_overtime_record_2024 
PARTITION OF tw_overtime_record 
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');

CREATE TABLE tw_overtime_record_2025 
PARTITION OF tw_overtime_record 
FOR VALUES FROM ('2025-01-01') TO ('2026-01-01');
```

## 9. 資料安全和隱私

### 敏感資料保護
```python
# 身分證字號加密儲存
def _encrypt_national_id(self, national_id):
    """加密身分證字號"""
    if not national_id:
        return False
    
    # 使用 AES 加密
    cipher = AES.new(self._get_encryption_key(), AES.MODE_CBC)
    encrypted = cipher.encrypt(pad(national_id.encode(), AES.block_size))
    return base64.b64encode(encrypted).decode()

# 薪資資料存取記錄
def _log_salary_access(self, user_id, employee_id, access_type):
    """記錄薪資資料存取"""
    self.env['audit.log'].create({
        'user_id': user_id,
        'model': 'hr.payslip',
        'res_id': employee_id,
        'access_type': access_type,
        'timestamp': fields.Datetime.now(),
    })
```

### 資料保留政策
```python
def _cleanup_old_data(self):
    """清理過期資料"""
    # 刪除3年前的草稿加班記錄
    cutoff_date = fields.Date.today() - timedelta(days=1095)
    old_drafts = self.env['tw.hr.attendance (原 tw.overtime.record 已移除)'].search([
        ('state', '=', 'draft'),
        ('create_date', '<', cutoff_date)
    ])
    old_drafts.unlink()
    
    # 歸檔5年前的薪資單
    archive_date = fields.Date.today() - timedelta(days=1825)
    old_payslips = self.env['hr.payslip'].search([
        ('date_to', '<', archive_date)
    ])
    old_payslips.write({'active': False})
```

## 10. 監控和報表資料

### 統計資料結構
```xml
<!-- 加班統計視圖 -->
<record id="view_overtime_statistics" model="ir.ui.view">
    <field name="name">加班統計</field>
    <field name="model">tw.overtime.statistics</field>
    <field name="arch" type="xml">
        <graph string="月度加班統計" type="bar">
            <field name="month" type="row"/>
            <field name="total_hours" type="measure"/>
            <field name="employee_count" type="measure"/>
        </graph>
    </field>
</record>
```

### 報表資料模型
```python
class OvertimeStatistics(models.Model):
    _name = 'tw.overtime.statistics'
    _description = '加班統計'
    _auto = False
    
    month = fields.Char('月份')
    department_id = fields.Many2one('hr.department', '部門')
    total_hours = fields.Float('總加班時數')
    employee_count = fields.Integer('員工數量')
    avg_hours_per_employee = fields.Float('平均每人加班時數')
    
    def init(self):
        tools.drop_view_if_exists(self.env.cr, self._table)
        self.env.cr.execute("""
            CREATE OR REPLACE VIEW %s AS (
                SELECT
                    row_number() OVER () AS id,
                    to_char(date, 'YYYY-MM') AS month,
                    e.department_id,
                    SUM(actual_overtime_hours) AS total_hours,
                    COUNT(DISTINCT employee_id) AS employee_count,
                    AVG(actual_overtime_hours) AS avg_hours_per_employee
                FROM tw_overtime_record o
                JOIN hr_employee e ON o.employee_id = e.id
                WHERE o.state = 'approved'
                GROUP BY to_char(date, 'YYYY-MM'), e.department_id
            )
        """ % self._table)