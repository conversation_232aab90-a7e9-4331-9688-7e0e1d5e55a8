# 測試指南

## 概述
本文檔提供 l10n_tw_hr_payroll 模組的完整測試指南，包含測試環境設定、單元測試、整合測試、效能測試等各個面向。

## 測試環境設定

### 測試資料庫設定

#### 建立測試資料庫
```bash
# 1. 建立測試資料庫
createdb odoo_test_tw_payroll

# 2. 初始化測試資料庫
python -m odoo -d odoo_test_tw_payroll -i base,hr,hr_payroll,hr_holidays,hr_attendance --stop-after-init

# 3. 安裝測試模組
python -m odoo -d odoo_test_tw_payroll -i l10n_tw_hr_payroll --stop-after-init
```

#### 測試配置檔
```bash
# 建立測試專用配置
cat > ~/odoo-dev/config/test.conf << EOF
[options]
# 資料庫設定
db_host = localhost
db_port = 5432
db_user = odoo
db_password = odoo

# 測試設定
test_enable = True
test_file = tests/
log_level = test
workers = 0

# 模組路徑
addons_path = /path/to/odoo/addons,/path/to/extra-addons

# 測試專用設定
without_demo = False
stop_after_init = True
EOF
```

### 測試工具安裝

#### Python 測試套件
```bash
# 啟用虛擬環境
source ~/odoo-venv/bin/activate

# 安裝測試工具
pip install pytest pytest-cov pytest-mock
pip install factory-boy faker
pip install selenium webdriver-manager
pip install coverage pytest-html
```

#### 測試資料生成工具
```bash
# 安裝資料生成工具
pip install odoo-test-helper
pip install python-dateutil
```

## 測試架構

### 測試目錄結構
```
tests/
├── __init__.py
├── common.py                    # 測試基礎類別
├── test_models/                 # 模型測試
│   ├── __init__.py
│   ├── test_hr_employee.py
│   ├── test_overtime_record.py
│   ├── test_payslip_extension.py
│   └── test_working_calendar.py
├── test_wizards/                # 嚮導測試
│   ├── __init__.py
│   ├── test_holiday_import.py
│   ├── test_makeup_day.py
│   └── test_overtime_limit.py
├── test_integration/            # 整合測試
│   ├── __init__.py
│   ├── test_attendance_overtime.py
│   ├── test_payroll_calculation.py
│   └── test_dual_payslip.py
├── test_performance/            # 效能測試
│   ├── __init__.py
│   ├── test_batch_operations.py
│   └── test_large_dataset.py
├── test_security/               # 安全測試
│   ├── __init__.py
│   ├── test_access_rights.py
│   └── test_record_rules.py
└── fixtures/                    # 測試資料
    ├── employees.json
    ├── overtime_records.json
    └── holidays.csv
```

### 測試基礎類別
```python
# tests/common.py
from odoo.tests.common import TransactionCase, Form
from odoo import fields
from datetime import datetime, timedelta
import json
import os

class TwPayrollTestCase(TransactionCase):
    """台灣薪資模組測試基礎類別"""
    
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.env = cls.env(context=dict(cls.env.context, tracking_disable=True))
        
        # 建立測試公司
        cls.company = cls.env['res.company'].create({
            'name': '測試公司',
            'currency_id': cls.env.ref('base.TWD').id,
        })
        
        # 建立測試部門
        cls.department = cls.env['hr.department'].create({
            'name': '資訊部',
            'company_id': cls.company.id,
        })
        
        # 建立測試員工
        cls.employee = cls._create_test_employee()
        
        # 建立工作行事曆
        cls.calendar = cls._create_test_calendar()
        
    @classmethod
    def _create_test_employee(cls):
        """建立測試員工"""
        return cls.env['hr.employee'].create({
            'name': '測試員工',
            'tw_employee_id': 'EMP001',
            'national_id': 'A123456789',
            'department_id': cls.department.id,
            'company_id': cls.company.id,
            'monthly_salary': 50000,
            'standard_working_hours': 8.0,
            'weekly_working_hours': 40.0,
        })
    
    @classmethod
    def _create_test_calendar(cls):
        """建立測試工作行事曆"""
        calendar = cls.env['resource.calendar'].create({
            'name': '標準工作時間',
            'company_id': cls.company.id,
            'hours_per_day': 8.0,
        })
        
        # 建立工作時間
        for day in range(5):  # 週一到週五
            cls.env['resource.calendar.attendance'].create({
                'name': f'工作日 {day + 1}',
                'calendar_id': calendar.id,
                'dayofweek': str(day),
                'hour_from': 9.0,
                'hour_to': 18.0,
            })
        
        return calendar
    
    def create_overtime_record(self, employee=None, date=None, hours=2.0, state='draft'):
        """建立測試加班記錄"""
        return self.env['tw.hr.attendance (原 tw.overtime.record 已移除)'].create({
            'employee_id': (employee or self.employee).id,
            'date': date or fields.Date.today(),
            'actual_overtime_hours': hours,
            'overtime_type': 'weekday',
            'state': state,
        })
    
    def create_attendance_record(self, employee=None, check_in=None, check_out=None):
        """建立測試出勤記錄"""
        if not check_in:
            check_in = datetime.now().replace(hour=9, minute=0, second=0)
        if not check_out:
            check_out = check_in + timedelta(hours=10)  # 工作10小時，含2小時加班
            
        return self.env['hr.attendance'].create({
            'employee_id': (employee or self.employee).id,
            'check_in': check_in,
            'check_out': check_out,
        })
```

## 單元測試撰寫

### 模型測試

#### 員工模型測試
```python
# tests/test_models/test_hr_employee.py
from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError
from ..common import TwPayrollTestCase

class TestHrEmployee(TwPayrollTestCase):
    """員工模型測試"""
    
    def test_employee_creation(self):
        """測試員工建立"""
        employee = self.env['hr.employee'].create({
            'name': '新員工',
            'tw_employee_id': 'EMP002',
            'national_id': 'B987654321',
            'monthly_salary': 45000,
        })
        
        self.assertEqual(employee.name, '新員工')
        self.assertEqual(employee.tw_employee_id, 'EMP002')
        self.assertEqual(employee.monthly_salary, 45000)
    
    def test_national_id_validation(self):
        """測試身分證字號驗證"""
        # 測試正確格式
        employee = self.env['hr.employee'].create({
            'name': '測試員工',
            'national_id': 'A123456789',
        })
        self.assertEqual(employee.national_id, 'A123456789')
        
        # 測試錯誤格式
        with self.assertRaises(ValidationError):
            self.env['hr.employee'].create({
                'name': '錯誤員工',
                'national_id': '123456789',  # 缺少英文字母
            })
    
    def test_hourly_rate_computation(self):
        """測試時薪計算"""
        employee = self.employee
        employee.monthly_salary = 60000
        employee.standard_working_hours = 8.0
        
        # 時薪 = 月薪 / 30天 / 每日工作時數
        expected_hourly_rate = 60000 / 30 / 8
        self.assertEqual(employee.hourly_rate, expected_hourly_rate)
    
    def test_overtime_statistics(self):
        """測試加班統計"""
        # 建立本月加班記錄
        today = fields.Date.today()
        first_day = today.replace(day=1)
        
        # 建立已核准的加班記錄
        overtime1 = self.create_overtime_record(
            employee=self.employee,
            date=first_day,
            hours=3.0,
            state='approved'
        )
        
        overtime2 = self.create_overtime_record(
            employee=self.employee,
            date=first_day + timedelta(days=1),
            hours=2.5,
            state='approved'
        )
        
        # 重新計算統計
        self.employee._compute_current_month_overtime()
        
        # 驗證統計結果
        self.assertEqual(self.employee.current_month_overtime, 5.5)
```

#### 加班記錄測試
```python
# tests/test_models/test_overtime_record.py
from ..common import TwPayrollTestCase
from odoo import fields
from datetime import timedelta

class TestOvertimeRecord(TwPayrollTestCase):
    """加班記錄測試"""
    
    def test_overtime_record_creation(self):
        """測試加班記錄建立"""
        record = self.create_overtime_record(hours=3.0)
        
        self.assertEqual(record.employee_id, self.employee)
        self.assertEqual(record.actual_overtime_hours, 3.0)
        self.assertEqual(record.state, 'draft')
    
    def test_overtime_approval_workflow(self):
        """測試加班審核流程"""
        record = self.create_overtime_record()
        
        # 提交審核
        record.action_submit()
        self.assertEqual(record.state, 'submitted')
        
        # 核准
        record.action_approve()
        self.assertEqual(record.state, 'approved')
        self.assertTrue(record.approved_date)
        self.assertEqual(record.approved_by, self.env.user)
    
    def test_overtime_rejection(self):
        """測試加班拒絕"""
        record = self.create_overtime_record()
        record.action_submit()
        
        # 拒絕
        record.action_reject()
        self.assertEqual(record.state, 'rejected')
    
    def test_displayed_hours_calculation(self):
        """測試顯示時數計算"""
        # 建立加班限制設定
        limit = self.env['tw.overtime.limit'].create({
            'name': '測試限制',
            'monthly_limit': 46.0,
            'display_limit': 30.0,
            'apply_to_all': True,
        })
        
        # 建立超過顯示限制的加班記錄
        record = self.create_overtime_record(hours=35.0)
        
        # 驗證顯示時數被限制
        self.assertEqual(record.displayed_overtime_hours, 30.0)
        self.assertEqual(record.hidden_overtime_hours, 5.0)
    
    def test_batch_approval(self):
        """測試批次審核"""
        # 建立多筆加班記錄
        records = []
        for i in range(3):
            record = self.create_overtime_record(
                date=fields.Date.today() + timedelta(days=i),
                hours=2.0
            )
            record.action_submit()
            records.append(record)
        
        # 批次審核
        overtime_records = self.env['tw.hr.attendance (原 tw.overtime.record 已移除)'].browse([r.id for r in records])
        overtime_records.batch_approve_records()
        
        # 驗證所有記錄都已核准
        for record in records:
            record.refresh()
            self.assertEqual(record.state, 'approved')
```

### 嚮導測試

#### 假日匯入嚮導測試
```python
# tests/test_wizards/test_holiday_import.py
from ..common import TwPayrollTestCase
import base64
import csv
import io

class TestHolidayImportWizard(TwPayrollTestCase):
    """假日匯入嚮導測試"""
    
    def setUp(self):
        super().setUp()
        self.wizard_model = self.env['tw.holiday.import.wizard']
    
    def _create_test_csv(self):
        """建立測試 CSV 檔案"""
        csv_data = [
            ['date', 'name', 'makeup_date'],
            ['2024-01-01', '元旦', ''],
            ['2024-02-10', '農曆新年', '2024-02-17'],
            ['2024-04-04', '兒童節', ''],
        ]
        
        output = io.StringIO()
        writer = csv.writer(output)
        writer.writerows(csv_data)
        
        return base64.b64encode(output.getvalue().encode('utf-8'))
    
    def test_csv_import(self):
        """測試 CSV 匯入"""
        csv_content = self._create_test_csv()
        
        wizard = self.wizard_model.create({
            'name': '2024年假日匯入',
            'import_file': csv_content,
            'calendar_id': self.calendar.id,
            'year': 2024,
            'date_column': 'date',
            'name_column': 'name',
            'makeup_date_column': 'makeup_date',
        })
        
        # 執行匯入
        wizard.action_import_holidays()
        
        # 驗證匯入結果
        self.assertEqual(wizard.total_records, 3)
        self.assertEqual(wizard.success_records, 3)
        self.assertEqual(wizard.error_records, 0)
    
    def test_preview_data(self):
        """測試資料預覽"""
        csv_content = self._create_test_csv()
        
        wizard = self.wizard_model.create({
            'name': '測試預覽',
            'import_file': csv_content,
            'calendar_id': self.calendar.id,
            'year': 2024,
        })
        
        # 預覽資料
        result = wizard.action_preview_data()
        
        # 驗證預覽結果
        self.assertIn('preview_data', result['context'])
        preview_data = result['context']['preview_data']
        self.assertEqual(len(preview_data), 3)
```

## 整合測試

### 出勤與加班整合測試
```python
# tests/test_integration/test_attendance_overtime.py
from ..common import TwPayrollTestCase
from datetime import datetime, timedelta

class TestAttendanceOvertimeIntegration(TwPayrollTestCase):
    """出勤與加班整合測試"""
    
    def test_attendance_creates_overtime(self):
        """測試出勤記錄自動建立加班記錄"""
        # 建立有加班的出勤記錄
        check_in = datetime.now().replace(hour=9, minute=0, second=0)
        check_out = check_in + timedelta(hours=10)  # 工作10小時，加班2小時
        
        attendance = self.create_attendance_record(
            check_in=check_in,
            check_out=check_out
        )
        
        # 觸發加班記錄建立
        attendance._auto_create_overtime_record()
        
        # 驗證加班記錄已建立
        overtime_records = self.env['tw.hr.attendance (原 tw.overtime.record 已移除)'].search([
            ('attendance_id', '=', attendance.id)
        ])
        
        self.assertEqual(len(overtime_records), 1)
        self.assertEqual(overtime_records.actual_overtime_hours, 2.0)
        self.assertEqual(overtime_records.employee_id, self.employee)
    
    def test_overtime_calculation_accuracy(self):
        """測試加班時數計算準確性"""
        # 測試不同工作時數的加班計算
        test_cases = [
            (8, 0),    # 標準工作時間，無加班
            (9, 1),    # 9小時工作，1小時加班
            (10, 2),   # 10小時工作，2小時加班
            (12, 4),   # 12小時工作，4小時加班
        ]
        
        for work_hours, expected_overtime in test_cases:
            with self.subTest(work_hours=work_hours):
                check_in = datetime.now().replace(hour=9, minute=0)
                check_out = check_in + timedelta(hours=work_hours)
                
                attendance = self.create_attendance_record(
                    check_in=check_in,
                    check_out=check_out
                )
                
                # 計算加班時數
                attendance._compute_tw_overtime_hours()
                
                self.assertEqual(attendance.tw_overtime_hours, expected_overtime)
```

### 薪資計算整合測試
```python
# tests/test_integration/test_payroll_calculation.py
from ..common import TwPayrollTestCase
from odoo import fields

class TestPayrollCalculation(TwPayrollTestCase):
    """薪資計算整合測試"""
    
    def setUp(self):
        super().setUp()
        
        # 建立薪資結構
        self.payroll_structure = self.env['hr.payroll.structure'].create({
            'name': '台灣薪資結構',
            'company_id': self.company.id,
        })
        
        # 建立合約
        self.contract = self.env['hr.contract'].create({
            'name': '測試合約',
            'employee_id': self.employee.id,
            'wage': self.employee.monthly_salary,
            'structure_type_id': self.env.ref('hr_payroll.structure_type_employee').id,
            'date_start': fields.Date.today().replace(day=1),
        })
    
    def test_dual_payslip_generation(self):
        """測試雙薪資單生成"""
        # 建立加班記錄
        overtime_record = self.create_overtime_record(
            hours=35.0,  # 超過顯示限制的時數
            state='approved'
        )
        
        # 建立薪資單
        payslip = self.env['hr.payslip'].create({
            'name': '測試薪資單',
            'employee_id': self.employee.id,
            'contract_id': self.contract.id,
            'date_from': fields.Date.today().replace(day=1),
            'date_to': fields.Date.today(),
        })
        
        # 生成雙薪資單
        payslip.action_generate_dual_payslips()
        
        # 驗證配對薪資單已建立
        self.assertTrue(payslip.paired_payslip_id)
        
        # 驗證標準版和完整版的差異
        standard_payslip = payslip if payslip.payslip_type == 'standard' else payslip.paired_payslip_id
        full_payslip = payslip if payslip.payslip_type == 'full' else payslip.paired_payslip_id
        
        self.assertLess(standard_payslip.displayed_overtime_hours, full_payslip.displayed_overtime_hours)
    
    def test_overtime_amount_calculation(self):
        """測試加班費計算"""
        # 建立不同類型的加班記錄
        weekday_overtime = self.create_overtime_record(
            hours=3.0,
            state='approved'
        )
        weekday_overtime.overtime_type = 'weekday'
        
        # 建立薪資單
        payslip = self.env['hr.payslip'].create({
            'name': '加班費測試',
            'employee_id': self.employee.id,
            'contract_id': self.contract.id,
            'date_from': fields.Date.today().replace(day=1),
            'date_to': fields.Date.today(),
        })
        
        # 計算加班費
        payslip._compute_overtime_amounts()
        
        # 驗證加班費計算
        expected_hourly_rate = self.employee.hourly_rate
        expected_overtime_amount = 3.0 * expected_hourly_rate * 1.34  # 平日加班費率
        
        self.assertAlmostEqual(payslip.standard_overtime_amount, expected_overtime_amount, places=2)
```

## 效能測試

### 批次操作效能測試
```python
# tests/test_performance/test_batch_operations.py
from ..common import TwPayrollTestCase
import time
from datetime import timedelta

class TestBatchOperations(TwPayrollTestCase):
    """批次操作效能測試"""
    
    def test_batch_overtime_approval_performance(self):
        """測試批次加班審核效能"""
        # 建立大量加班記錄
        record_count = 1000
        records = []
        
        start_time = time.time()
        
        for i in range(record_count):
            record = self.create_overtime_record(
                date=fields.Date.today() + timedelta(days=i % 30),
                hours=2.0
            )
            record.action_submit()
            records.append(record)
        
        creation_time = time.time() - start_time
        
        # 批次審核
        start_time = time.time()
        overtime_records = self.env['tw.hr.attendance (原 tw.overtime.record 已移除)'].browse([r.id for r in records])
        overtime_records.batch_approve_records()
        approval_time = time.time() - start_time
        
        # 效能斷言
        self.assertLess(creation_time, 30, "建立1000筆記錄應在30秒內完成")
        self.assertLess(approval_time, 10, "批次審核1000筆記錄應在10秒內完成")
        
        # 驗證結果正確性
        approved_count = self.env['tw.hr.attendance (原 tw.overtime.record 已移除)'].search_count([
            ('id', 'in', [r.id for r in records]),
            ('state', '=', 'approved')
        ])
        self.assertEqual(approved_count, record_count)
    
    def test_large_dataset_query_performance(self):
        """測試大資料集查詢效能"""
        # 建立大量員工和加班記錄
        employees = []
        for i in range(100):
            employee = self.env['hr.employee'].create({
                'name': f'員工{i:03d}',
                'tw_employee_id': f'EMP{i:03d}',
                'national_id': f'A{i:09d}',
                'monthly_salary': 50000,
            })
            employees.append(employee)
        
        # 為每個員工建立加班記錄
        for employee in employees:
            for day in range(30):
                self.create_overtime_record(
                    employee=employee,
                    date=fields.Date.today() + timedelta(days=day),
                    hours=2.0,
                    state='approved'
                )
        
        # 測試統計查詢效能
        start_time = time.time()
        
        # 執行複雜統計查詢
        result = self.env['tw.hr.attendance (原 tw.overtime.record 已移除)'].read_group(
            [('state', '=', 'approved')],
            ['employee_id', 'actual_overtime_hours:sum'],
            ['employee_id']
        )
        
        query_time = time.time() - start_time
        
        # 效能斷言
        self.assertLess(query_time, 5, "統計查詢應在5秒內完成")
        self.assertEqual(len(result), 100, "應該返回100個員工的統計")
```

### 記憶體使用測試
```python
# tests/test_performance/test_memory_usage.py
import psutil
import os
from ..common import TwPayrollTestCase

class TestMemoryUsage(TwPayrollTestCase):
    """記憶體使用測試"""
    
    def test_large_dataset_memory_usage(self):
        """測試大資料集處理的記憶體使用"""
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 處理大量資料
        record_count = 10000
        records = []
        
        for i in range(record_count):
            record = self.create_overtime_record(hours=2.0)
            records.append(record)
            
            # 每1000筆檢查一次記憶體
            if i % 1000 == 0:
                current_memory = process.memory_info().rss / 1024 / 1024
                memory_increase = current_memory - initial_memory
                
                # 記憶體增長不應超過合理範圍
                self.assertLess(memory_increase, 500, f"記憶體增長過多: {memory_increase}MB")
        
        # 清理資料
        self.env['tw.hr.attendance (原 tw.overtime.record 已移除)'].browse([r.id for r in records]).unlink()
        
        # 強制垃圾回收
        import gc
        gc.collect()
        
        final_memory = process.memory_info().rss / 1024 / 1024
        memory_leak = final_memory - initial_memory
        
        # 檢查記憶體洩漏
        self.assertLess(memory_leak, 100, f"可能存在記憶體洩漏: {memory_leak}MB")
```

## 安全測試

### 權限測試
```python
# tests/test_security/test_access_rights.py
from ..common import TwPayrollTestCase
from odoo.exceptions import AccessError

class TestAccessRights(TwPayrollTestCase):
    """權限測試"""
    
    def setUp(self):
        super().setUp()
        
        # 建立不同權限的使用者
        self.user_employee = self.env['res.users'].create({
            'name': '一般員工',
            'login': 'employee',
            'groups_id': [(6, 0, [self.env.ref('l10n_tw_hr_payroll.group_tw_payroll_user').id])]
        })
        
        self.user_manager = self.env['res.users'].create({
            'name': '部門主管',
            'login': 'manager',
            'groups_id': [(6, 0, [self.env.ref('l10n_tw_hr_payroll.group_tw_payroll_manager').id])]
        })
        
        self.user_admin = self.env['res.users'].create({
            'name': '薪資管理員',
            'login': 'admin',
            'groups_id': [(6, 0, [self.env.ref('l10n_tw_hr_payroll.group_tw_payroll_admin').id])]
        })
    
    def test_employee_access_rights(self):
        """測試一般員工權限"""
        # 切換到員工使用者
        overtime_model = self.env['tw.hr.attendance (原 tw.overtime.record 已移除)'].with_user(self.user_employee)
        
        # 員工應該可以建立自己的加班記錄
        record = overtime_model.create({
            'employee_id': self.employee.id,
            'date': fields.Date.today(),
            'actual_overtime_hours': 2.0,
        })
        self.assertTrue(record)
        
        # 員工不應該能夠直接核准加班記錄
        with self.assertRaises(AccessError):
            record.action_approve()
    
    def test_manager_access_rights(self):
        """測試主管權限"""
        overtime_model = self.env['tw.hr.attendance (原 tw.overtime.record 已移除)'].with_user(self.user_manager)
        
        # 建立加班記錄
        record = self.create_overtime_record()
        record.action_submit()
        
        # 主管應該可以審核加班記錄
        record_as_manager = overtime_model.browse(record.id)
        record_as_manager.action_approve()
        
        self.assertEqual(record.state, 'approved')
    
    def test_record_rules(self):
        """測試記錄規則"""
        # 建立其他員工的加班記錄
        other_employee = self.env['hr.employee'].create({
            'name': '其他員工',
            'tw_employee_id': 'EMP999',
        })
        
        other_record = self.create_overtime_record(employee=other_employee)
        
        # 一般員工不應該看到其他員工的記錄
        overtime_model = self.env['tw.hr.attendance (原 tw.overtime.record 已移除)'].with_user(self.user_employee)
        accessible_records = overtime_model.search([('id', '=', other_record.id)])
        
        self.assertEqual(len(accessible_records), 0)
```

### 資料驗證測試
```python
# tests/test_security/test_data_validation.py
from ..common import TwPayrollTestCase
from odoo.exceptions import ValidationError

class TestData