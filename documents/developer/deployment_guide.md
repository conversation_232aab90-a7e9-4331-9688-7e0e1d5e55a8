# 部署指南

## 概述
本文檔提供 l10n_tw_hr_payroll 模組的完整部署指南，包含系統需求、安裝配置、生產環境部署、監控維護等各個面向。

## 系統需求和環境準備

### 硬體需求

#### 最低需求
- **CPU**: 2 核心
- **記憶體**: 4GB RAM
- **硬碟**: 20GB 可用空間
- **網路**: 100Mbps

#### 建議需求
- **CPU**: 4 核心以上
- **記憶體**: 8GB RAM 以上
- **硬碟**: 50GB SSD
- **網路**: 1Gbps

#### 生產環境需求
- **CPU**: 8 核心以上
- **記憶體**: 16GB RAM 以上
- **硬碟**: 100GB SSD + 備份儲存
- **網路**: 高可用網路連線

### 軟體需求

#### 作業系統
- **Ubuntu**: 20.04 LTS 或更新版本
- **CentOS**: 8 或更新版本
- **RHEL**: 8 或更新版本
- **Debian**: 11 或更新版本

#### 核心軟體
```bash
# Python 環境
Python 3.8+ (建議 3.10+)
pip 21.0+
virtualenv

# 資料庫
PostgreSQL 12+ (建議 14+)

# Web 伺服器
Nginx 1.18+
或 Apache 2.4+

# 其他工具
Git 2.25+
Node.js 16+ (用於前端資源)
wkhtmltopdf 0.12.6+ (用於 PDF 報表)
```

### 網路和安全需求

#### 防火牆設定
```bash
# 開放必要端口
sudo ufw allow 22/tcp      # SSH
sudo ufw allow 80/tcp      # HTTP
sudo ufw allow 443/tcp     # HTTPS
sudo ufw allow 5432/tcp    # PostgreSQL (僅內部網路)
sudo ufw enable
```

#### SSL 憑證
- 生產環境必須使用 HTTPS
- 建議使用 Let's Encrypt 或商業憑證
- 定期更新憑證

## 安裝和配置步驟

### 1. 系統準備

#### Ubuntu/Debian 系統
```bash
# 更新系統
sudo apt update && sudo apt upgrade -y

# 安裝基礎套件
sudo apt install -y python3 python3-pip python3-venv
sudo apt install -y postgresql postgresql-contrib
sudo apt install -y nginx
sudo apt install -y git curl wget
sudo apt install -y build-essential libxml2-dev libxslt1-dev
sudo apt install -y libevent-dev libsasl2-dev libldap2-dev
sudo apt install -y libpq-dev libjpeg-dev libpng-dev
sudo apt install -y wkhtmltopdf
```

#### CentOS/RHEL 系統
```bash
# 更新系統
sudo dnf update -y

# 安裝 EPEL repository
sudo dnf install -y epel-release

# 安裝基礎套件
sudo dnf install -y python3 python3-pip python3-virtualenv
sudo dnf install -y postgresql postgresql-server postgresql-contrib
sudo dnf install -y nginx
sudo dnf install -y git curl wget
sudo dnf groupinstall -y "Development Tools"
sudo dnf install -y libxml2-devel libxslt-devel
sudo dnf install -y libevent-devel openldap-devel
sudo dnf install -y postgresql-devel libjpeg-devel libpng-devel
sudo dnf install -y wkhtmltopdf
```

### 2. PostgreSQL 設定

#### 初始化和設定
```bash
# Ubuntu/Debian
sudo systemctl start postgresql
sudo systemctl enable postgresql

# CentOS/RHEL (首次安裝需要初始化)
sudo postgresql-setup --initdb
sudo systemctl start postgresql
sudo systemctl enable postgresql

# 建立 Odoo 資料庫使用者
sudo -u postgres createuser -s odoo
sudo -u postgres psql -c "ALTER USER odoo PASSWORD 'secure_password';"

# 建立生產資料庫
sudo -u postgres createdb -O odoo odoo_production
```

#### PostgreSQL 效能調整
```bash
# 編輯 postgresql.conf
sudo nano /etc/postgresql/14/main/postgresql.conf

# 效能調整設定
shared_buffers = 256MB                    # 25% of RAM
effective_cache_size = 1GB                # 75% of RAM
work_mem = 4MB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1                    # SSD 使用較低值
effective_io_concurrency = 200            # SSD 使用較高值

# 連線設定
max_connections = 100
listen_addresses = 'localhost'
port = 5432

# 重啟 PostgreSQL
sudo systemctl restart postgresql
```

### 3. Odoo 安裝

#### 建立 Odoo 使用者
```bash
# 建立系統使用者
sudo adduser --system --home=/opt/odoo --group odoo
sudo mkdir -p /opt/odoo/{odoo,addons,logs,config}
sudo chown -R odoo:odoo /opt/odoo
```

#### 下載和安裝 Odoo
```bash
# 切換到 odoo 使用者
sudo su - odoo

# 建立虛擬環境
python3 -m venv /opt/odoo/venv
source /opt/odoo/venv/bin/activate

# 下載 Odoo 18
cd /opt/odoo
git clone --depth 1 --branch 18.0 https://github.com/odoo/odoo.git odoo

# 安裝 Python 相依套件
cd odoo
pip install --upgrade pip
pip install -r requirements.txt
pip install psycopg2-binary
```

#### 安裝台灣薪資模組
```bash
# 下載模組到 addons 目錄
cd /opt/odoo/addons
git clone https://github.com/your-repo/l10n_tw_hr_payroll.git

# 或複製本地模組
sudo cp -r /path/to/l10n_tw_hr_payroll /opt/odoo/addons/
sudo chown -R odoo:odoo /opt/odoo/addons/l10n_tw_hr_payroll
```

### 4. Odoo 配置

#### 建立配置檔
```bash
# 建立生產環境配置
sudo nano /opt/odoo/config/odoo.conf
```

```ini
[options]
# 基本設定
admin_passwd = your_master_password_here
db_host = localhost
db_port = 5432
db_user = odoo
db_password = secure_password
db_name = odoo_production

# 伺服器設定
http_port = 8069
workers = 4
max_cron_threads = 2
limit_memory_hard = 2684354560
limit_memory_soft = 2147483648
limit_request = 8192
limit_time_cpu = 600
limit_time_real = 1200

# 檔案路徑
addons_path = /opt/odoo/odoo/addons,/opt/odoo/addons
data_dir = /opt/odoo/.local/share/Odoo
logfile = /opt/odoo/logs/odoo.log
log_level = info

# 安全設定
list_db = False
proxy_mode = True

# 效能設定
db_maxconn = 64
server_wide_modules = base,web
```

#### 建立 systemd 服務
```bash
# 建立服務檔案
sudo nano /etc/systemd/system/odoo.service
```

```ini
[Unit]
Description=Odoo
Documentation=http://www.odoo.com
After=network.target postgresql.service

[Service]
Type=simple
SyslogIdentifier=odoo
PermissionsStartOnly=true
User=odoo
Group=odoo
ExecStart=/opt/odoo/venv/bin/python /opt/odoo/odoo/odoo-bin -c /opt/odoo/config/odoo.conf
StandardOutput=journal+console
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

```bash
# 啟用和啟動服務
sudo systemctl daemon-reload
sudo systemctl enable odoo
sudo systemctl start odoo

# 檢查服務狀態
sudo systemctl status odoo
```

### 5. Nginx 反向代理設定

#### 建立 Nginx 配置
```bash
# 建立站點配置
sudo nano /etc/nginx/sites-available/odoo
```

```nginx
upstream odoo {
    server 127.0.0.1:8069;
}

upstream odoochat {
    server 127.0.0.1:8072;
}

map $http_upgrade $connection_upgrade {
    default upgrade;
    ''      close;
}

server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # 重導向到 HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    # SSL 憑證設定
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    
    # 安全標頭
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-Frame-Options DENY always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # 日誌設定
    access_log /var/log/nginx/odoo.access.log;
    error_log /var/log/nginx/odoo.error.log;
    
    # 檔案上傳大小限制
    client_max_body_size 200M;
    
    # Gzip 壓縮
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # 靜態檔案快取
    location ~* /web/static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # WebSocket 支援 (即時通訊)
    location /websocket {
        proxy_pass http://odoochat;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_set_header X-Forwarded-Host $http_host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # 主要應用程式
    location / {
        proxy_pass http://odoo;
        proxy_set_header Host $http_host;
        proxy_set_header X-Forwarded-Host $http_host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_redirect off;
        
        # 超時設定
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }
}
```

```bash
# 啟用站點
sudo ln -s /etc/nginx/sites-available/odoo /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## 資料庫遷移

### 從開發環境遷移到生產環境

#### 1. 備份開發資料庫
```bash
# 在開發環境執行
pg_dump -h localhost -U odoo -d odoo_dev > odoo_dev_backup.sql

# 壓縮備份檔案
gzip odoo_dev_backup.sql
```

#### 2. 傳輸到生產環境
```bash
# 使用 scp 傳輸
scp odoo_dev_backup.sql.gz user@production-server:/tmp/

# 或使用 rsync
rsync -avz odoo_dev_backup.sql.gz user@production-server:/tmp/
```

#### 3. 在生產環境恢復
```bash
# 解壓縮
gunzip /tmp/odoo_dev_backup.sql.gz

# 停止 Odoo 服務
sudo systemctl stop odoo

# 恢復資料庫
sudo -u postgres dropdb odoo_production
sudo -u postgres createdb -O odoo odoo_production
sudo -u postgres psql -d odoo_production -f /tmp/odoo_dev_backup.sql

# 更新模組
sudo -u odoo /opt/odoo/venv/bin/python /opt/odoo/odoo/odoo-bin -c /opt/odoo/config/odoo.conf -d odoo_production -u l10n_tw_hr_payroll --stop-after-init

# 重啟服務
sudo systemctl start odoo
```

### 版本升級遷移

#### 準備升級
```bash
# 1. 完整備份
sudo -u postgres pg_dump odoo_production > /backup/odoo_pre_upgrade_$(date +%Y%m%d).sql

# 2. 備份檔案系統
sudo tar -czf /backup/odoo_filestore_$(date +%Y%m%d).tar.gz /opt/odoo/.local/share/Odoo/filestore/

# 3. 備份配置檔案
sudo cp /opt/odoo/config/odoo.conf /backup/odoo.conf.$(date +%Y%m%d)
```

#### 執行升級
```bash
# 1. 停止服務
sudo systemctl stop odoo

# 2. 更新 Odoo 核心
cd /opt/odoo/odoo
git fetch origin
git checkout 18.0
git pull origin 18.0

# 3. 更新模組
cd /opt/odoo/addons/l10n_tw_hr_payroll
git pull origin main

# 4. 更新 Python 相依套件
source /opt/odoo/venv/bin/activate
pip install --upgrade -r /opt/odoo/odoo/requirements.txt

# 5. 執行資料庫升級
sudo -u odoo /opt/odoo/venv/bin/python /opt/odoo/odoo/odoo-bin -c /opt/odoo/config/odoo.conf -d odoo_production -u all --stop-after-init

# 6. 重啟服務
sudo systemctl start odoo
```

## 生產環境部署

### 高可用性部署

#### 負載平衡設定
```nginx
# /etc/nginx/sites-available/odoo-ha
upstream odoo_backend {
    least_conn;
    server *********:8069 weight=1 max_fails=3 fail_timeout=30s;
    server *********:8069 weight=1 max_fails=3 fail_timeout=30s;
    server *********:8069 weight=1 max_fails=3 fail_timeout=30s;
}

upstream odoo_chat {
    ip_hash;
    server *********:8072;
    server *********:8072;
    server *********:8072;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL 和其他設定...
    
    location /websocket {
        proxy_pass http://odoo_chat;
        # WebSocket 設定...
    }
    
    location / {
        proxy_pass http://odoo_backend;
        # 代理設定...
    }
}
```

#### 資料庫叢集設定
```bash
# PostgreSQL 主從複製設定
# 主伺服器 postgresql.conf
wal_level = replica
max_wal_senders = 3
max_replication_slots = 3
synchronous_commit = on
synchronous_standby_names = 'standby1'

# 從伺服器設定
# recovery.conf
standby_mode = 'on'
primary_conninfo = 'host=********* port=5432 user=replicator password=replica_password'
```

### 容器化部署 (Docker)

#### Dockerfile
```dockerfile
FROM python:3.10-slim-bullseye

# 安裝系統相依套件
RUN apt-get update && apt-get install -y \
    git \
    postgresql-client \
    build-essential \
    libxml2-dev \
    libxslt1-dev \
    libevent-dev \
    libsasl2-dev \
    libldap2-dev \
    libpq-dev \
    libjpeg-dev \
    libpng-dev \
    wkhtmltopdf \
    && rm -rf /var/lib/apt/lists/*

# 建立 odoo 使用者
RUN useradd -ms /bin/bash odoo

# 設定工作目錄
WORKDIR /opt/odoo

# 複製 Odoo 和模組
COPY --chown=odoo:odoo odoo/ ./odoo/
COPY --chown=odoo:odoo addons/ ./addons/
COPY --chown=odoo:odoo config/ ./config/

# 安裝 Python 相依套件
USER odoo
RUN python -m venv venv && \
    . venv/bin/activate && \
    pip install --upgrade pip && \
    pip install -r odoo/requirements.txt

# 暴露端口
EXPOSE 8069 8072

# 啟動命令
CMD ["./venv/bin/python", "odoo/odoo-bin", "-c", "config/odoo.conf"]
```

#### Docker Compose
```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  db:
    image: postgres:14
    environment:
      POSTGRES_DB: odoo
      POSTGRES_USER: odoo
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgresql.conf:/etc/postgresql/postgresql.conf
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    restart: unless-stopped
    networks:
      - odoo_network

  odoo:
    build: .
    depends_on:
      - db
    environment:
      - DB_HOST=db
      - DB_PASSWORD=${DB_PASSWORD}
    volumes:
      - odoo_data:/opt/odoo/.local/share/Odoo
      - ./logs:/opt/odoo/logs
    restart: unless-stopped
    networks:
      - odoo_network
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - odoo
    restart: unless-stopped
    networks:
      - odoo_network

volumes:
  postgres_data:
  odoo_data:

networks:
  odoo_network:
    driver: bridge
```

### Kubernetes 部署

#### 部署清單
```yaml
# k8s/odoo-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: odoo-deployment
  labels:
    app: odoo
spec:
  replicas: 3
  selector:
    matchLabels:
      app: odoo
  template:
    metadata:
      labels:
        app: odoo
    spec:
      containers:
      - name: odoo
        image: your-registry/odoo-tw-payroll:latest
        ports:
        - containerPort: 8069
        env:
        - name: DB_HOST
          value: "postgres-service"
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: odoo-secret
              key: db-password
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        volumeMounts:
        - name: odoo-data
          mountPath: /opt/odoo/.local/share/Odoo
      volumes:
      - name: odoo-data
        persistentVolumeClaim:
          claimName: odoo-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: odoo-service
spec:
  selector:
    app: odoo
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8069
  type: LoadBalancer
```

## 監控和維護

### 系統監控

#### Prometheus 監控設定
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'odoo'
    static_configs:
      - targets: ['localhost:8069']
    metrics_path: '/web/health'
    scrape_interval: 30s

  - job_name: 'postgresql'
    static_configs:
      - targets: ['localhost:9187']

  - job_name: 'nginx'
    static_configs:
      - targets: ['localhost:9113']

  - job_name: 'node'
    static_configs:
      - targets: ['localhost:9100']
```

#### Grafana 儀表板
```json
{
  "dashboard": {
    "title": "Odoo 台灣薪資系統監控",
    "panels": [
      {
        "title": "系統負載",
        "type": "graph",
        "targets": [
          {
            "expr": "node_load1",
            "legendFormat": "1分鐘負載"
          }
        ]
      },
      {
        "title": "記憶體使用率",
        "type": "singlestat",
        "targets": [
          {
            "expr": "(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100"
          }
        ]
      },
      {
        "title": "資料庫連線數",
        "type": "graph",
        "targets": [
          {
            "expr": "pg_stat_database_numbackends{datname=\"odoo_production\"}"
          }
        ]
      }
    ]
  }
}
```

### 日誌管理

#### 日誌輪轉設定
```bash
# /etc/logrotate.d/odoo
/opt/odoo/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 odoo odoo
    postrotate
        systemctl reload odoo
    endscript
}
```

#### 集中化日誌 (ELK Stack)
```yaml
# filebeat.yml
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /opt/odoo/logs/*.log
  fields:
    service: odoo
    environment: production
  multiline.pattern: '^\d{4}-\d{2}-\d{2}'
  multiline.negate: true
  multiline.match: after

output.elasticsearch:
  hosts: ["elasticsearch:9200"]
  index: "odoo-logs-%{+yyyy.MM.dd}"

processors:
- add_host_metadata:
    when.not.contains.tags: forwarded
```

### 效能監控

#### 自訂監控腳本
```python
#!/usr/bin/env python3
# /opt/odoo/scripts/monitor.py

import psycopg2
import requests
import json
import time
from datetime import datetime

def check_database_performance():
    """檢查資料庫效能"""
    conn = psycopg2.connect(
        host="localhost",
        database="odoo_production",
        user="odoo",
        password="secure_password"
    )
    
    cur = conn.cursor()
    
    # 檢查慢查詢
    cur.execute("""
        SELECT query, mean_time, calls
        FROM pg_stat_statements
        WHERE mean_time > 1000
        ORDER BY mean_time DESC
        LIMIT 10;
    """)
    
    slow_queries = cur.fetchall()
    
    # 檢查資料庫大小
    cur.execute("""
        SELECT pg_size_pretty(pg_database_size('odoo_production'));
    """)
    
    db_size = cur.fetchone()[0]
    
    cur.close()
    conn.close()
    
    return {
        'slow_queries': slow_queries,
        'database_size': db_size,
        'timestamp': datetime.now().isoformat()
    }

def check_odoo_health():
    """檢查 Odoo 健康狀態"""
    try:
        response = requests.get('http://localhost:8069/web/health', timeout=10)
        return {
            'status': 'healthy' if response.status_code == 200 else 'unhealthy',
            'response_time': response.elapsed.total_seconds(),
            'timestamp': datetime.now().isoformat()
        }
    except Exception as e:
        return {
            'status': 'error',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }

if __name__ == '__main__':
    db_stats = check_database_performance()
    odoo_health = check_odoo_health()
    
    # 輸出監控結果
    monitoring_data = {
        'database': db_stats,
        'odoo': odoo_health
    }
    
    print(json.dumps(monitoring_data, indent=2))
    
    # 如果有問題，發送警報
    if odoo_health['status'] != 'healthy':
        # 發送警報邏輯
        pass
```

### 備份策略

#### 自動備份腳本
```bash
#!/bin/bash
# /opt/odoo/scripts/backup.sh

# 設定變數
BACKUP_DIR="/backup/odoo"
DB_NAME="odoo_production"
DB_USER="odoo"
RETENTION_DAYS=30
DATE=$(date +%Y%m%d_%H%M%S)

# 建立備份目錄
mkdir -p $BACKUP_DIR/{database,filestore}

# 資料庫備份
echo "開始資料庫備份..."
pg_dump -h localhost -U $DB_USER -d $DB_NAME | gzip > $BACKUP_DIR/database/odoo_db_$DATE.sql.gz

# 檔案系統備份
echo "開始檔案系統備份..."
tar -czf $BACKUP_DIR/filestore/odoo_filestore_$DATE.tar.gz /opt/odoo/.local/share/Odoo/filestore/

# 清理舊備份
echo "清理舊備份..."
find $BACKUP_DIR/database -name "*.sql.gz" -mtime +$RETENTION_DAYS -delete
find $BACKUP_DIR/filestore -name "*.tar.gz" -mtime +$RETENTION_DAYS -delete

# 驗證備份
echo "驗證備份..."
if [ -f "$BACKUP_DIR/database/odoo_db_$DATE.sql.gz" ] && [ -f "$BACKUP_DIR/filestore/odoo_filestore_$DATE.tar.gz" ]; then
    echo "備份完成: $DATE"
    
    # 發送成功通知
    curl -X POST -H 'Content-type: application/json' \
        --data '{"text":"Odoo 備份完成: '$DATE'"}' \
        YOUR_SLACK_WEBHOOK_URL
else
    echo "備份失敗: $DATE"
    
    # 發送失敗警報
    curl -X POST -H 'Content-type: application/json' \
        --data '{"text":"⚠️ Odoo 備份失敗: '$DATE'"}' \
        YOUR_SLACK_WEBHOOK_URL
fi
```

#### 定時任務設定
```bash
# 編輯 crontab
sudo crontab -e

# 每日凌晨 2 點執行備份
0 2 * * * /opt/odoo/scripts/backup.sh >> /opt/odoo/logs/backup.log 2>&1

# 每週日凌晨 3 點執行完整系統檢查
0 3 * * 0 /opt/odoo/scripts/system_check.sh >> /opt/odoo/logs/system_check.log 2>&1

# 每小時執行健康檢查
0 * * * * /opt/odoo/scripts/health_check.sh >> /opt/odoo/logs/health_check.log 2>&1
```

## 災難恢復

### 恢復程序

#### 完整系統恢復
```bash
#!/bin/bash