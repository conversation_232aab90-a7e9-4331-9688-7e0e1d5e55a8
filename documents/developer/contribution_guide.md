# 貢獻指南

## 概述
歡迎參與 l10n_tw_hr_payroll 專案的開發！本文檔提供完整的貢獻指南，包含參與方式、開發流程、程式碼規範等資訊。

## 如何參與專案開發

### 參與方式

#### 1. 程式碼貢獻
- 修復錯誤 (Bug Fixes)
- 新增功能 (New Features)
- 效能優化 (Performance Improvements)
- 程式碼重構 (Code Refactoring)

#### 2. 文檔貢獻
- 改善現有文檔
- 新增使用範例
- 翻譯文檔
- 撰寫教學文章

#### 3. 測試貢獻
- 撰寫單元測試
- 執行整合測試
- 回報測試結果
- 改善測試覆蓋率

#### 4. 社群參與
- 回答問題
- 參與討論
- 分享使用經驗
- 推廣專案

### 開始貢獻的步驟

#### 1. 了解專案
```bash
# 1. Fork 專案到你的 GitHub 帳號
# 2. Clone 到本地
git clone https://github.com/your-username/l10n_tw_hr_payroll.git
cd l10n_tw_hr_payroll

# 3. 設定上游倉庫
git remote add upstream https://github.com/original-repo/l10n_tw_hr_payroll.git

# 4. 閱讀文檔
# 詳細閱讀 README.md 和 documents/ 目錄下的文檔
```

#### 2. 設定開發環境
```bash
# 1. 安裝開發相依套件
pip install -r requirements-dev.txt

# 2. 設定 pre-commit hooks
pre-commit install

# 3. 執行測試確保環境正常
python -m pytest tests/
```

#### 3. 選擇貢獻項目
- 查看 [Issues](https://github.com/repo/issues) 尋找適合的任務
- 查看 `good first issue` 標籤的問題
- 查看 `help wanted` 標籤的需求
- 提出新的功能建議

## 問題回報流程

### 回報錯誤 (Bug Report)

#### 1. 檢查現有問題
在提交新問題前，請先搜尋現有的 Issues，避免重複回報。

#### 2. 收集資訊
```bash
# 收集系統資訊
python --version
pip list | grep odoo

# 收集錯誤日誌
tail -n 100 /opt/odoo/logs/odoo.log

# 收集模組資訊
grep version __manifest__.py
```

#### 3. 使用問題範本
```markdown
## 錯誤描述
簡潔描述遇到的問題

## 重現步驟
1. 進入...
2. 點擊...
3. 看到錯誤...

## 預期行為
描述預期應該發生什麼

## 實際行為
描述實際發生了什麼

## 環境資訊
- OS: Ubuntu 20.04
- Python: 3.10.0
- Odoo: 18.0
- 模組版本: 1.0.0

## 錯誤日誌
```
貼上相關的錯誤日誌
```

## 螢幕截圖
如果適用，請附上螢幕截圖

## 其他資訊
任何其他相關資訊
```

### 安全問題回報

對於安全相關問題，請不要在公開 Issues 中回報，而是：

1. 發送郵件到 <EMAIL>
2. 使用 GPG 加密敏感資訊
3. 等待安全團隊回應
4. 配合負責任的揭露流程

## 功能請求流程

### 提出功能請求

#### 1. 功能請求範本
```markdown
## 功能描述
清楚描述你希望新增的功能

## 問題背景
描述這個功能要解決什麼問題

## 解決方案
描述你期望的解決方案

## 替代方案
描述你考慮過的其他替代方案

## 使用場景
描述這個功能的使用場景和使用者

## 實作建議
如果有實作想法，請分享

## 相關資源
相關的文檔、連結或參考資料
```

#### 2. 功能討論
- 參與 GitHub Discussions
- 在 Issues 中討論實作細節
- 考慮向後相容性
- 評估對現有功能的影響

### 功能開發流程

#### 1. 規劃階段
```markdown
# 功能設計文檔範本

## 功能概述
- 功能名稱
- 功能目標
- 使用者故事

## 技術設計
- 資料模型變更
- API 設計
- 介面設計
- 安全考量

## 實作計畫
- 開發階段
- 時程規劃
- 測試策略
- 部署計畫

## 風險評估
- 技術風險
- 相容性風險
- 效能影響
- 緩解措施
```

#### 2. 開發階段
```bash
# 1. 建立功能分支
git checkout -b feature/new-awesome-feature

# 2. 實作功能
# 遵循程式碼規範
# 撰寫測試
# 更新文檔

# 3. 提交變更
git add .
git commit -m "feat: 新增超棒功能"

# 4. 推送分支
git push origin feature/new-awesome-feature
```

## 程式碼貢獻規範

### 程式碼風格

#### Python 程式碼規範
```python
# 1. 遵循 PEP 8
# 2. 使用 Black 格式化
# 3. 使用 isort 排序 import
# 4. 使用 flake8 檢查

# 範例：良好的程式碼風格
class TwOvertimeRecord(models.Model):
    """台灣加班記錄模型
    
    這個模型管理員工的加班申請、審核和統計功能。
    支援自動計算加班時數和費率。
    """
    _name = 'tw.hr.attendance (原 tw.overtime.record 已移除)'
    _description = '台灣加班記錄'
    _order = 'date desc, employee_id'
    
    # 欄位定義
    name = fields.Char(
        string='記錄名稱',
        compute='_compute_name',
        store=True,
        help='自動生成的記錄名稱'
    )
    
    @api.depends('employee_id', 'date')
    def _compute_name(self):
        """計算記錄名稱"""
        for record in self:
            if record.employee_id and record.date:
                record.name = f"{record.employee_id.name} - {record.date}"
            else:
                record.name = _('新加班記錄')
```

#### XML 程式碼規範
```xml
<!-- 1. 使用一致的縮排 (4 spaces) -->
<!-- 2. 屬性按字母順序排列 -->
<!-- 3. 使用有意義的 ID -->

<record id="view_overtime_record_form" model="ir.ui.view">
    <field name="name">tw.hr.attendance (原 tw.overtime.record 已移除).form</field>
    <field name="model">tw.hr.attendance (原 tw.overtime.record 已移除)</field>
    <field name="arch" type="xml">
        <form string="加班記錄">
            <header>
                <button name="action_submit" 
                        string="提交" 
                        type="object" 
                        class="btn-primary"
                        invisible="state != 'draft'"/>
            </header>
            <sheet>
                <group>
                    <group>
                        <field name="employee_id"/>
                        <field name="date"/>
                    </group>
                    <group>
                        <field name="actual_overtime_hours"/>
                        <field name="state"/>
                    </group>
                </group>
            </sheet>
        </form>
    </field>
</record>
```

### 提交訊息規範

#### 提交訊息格式
```
類型(範圍): 簡短描述

詳細描述 (可選)

相關問題: #123
```

#### 提交類型
- `feat`: 新功能
- `fix`: 錯誤修復
- `docs`: 文檔更新
- `style`: 程式碼格式調整 (不影響功能)
- `refactor`: 程式碼重構
- `perf`: 效能優化
- `test`: 測試相關
- `chore`: 建置或輔助工具變更
- `ci`: CI/CD 相關變更

#### 提交訊息範例
```bash
# 好的提交訊息
git commit -m "feat(overtime): 新增自動審核功能

- 新增自動審核設定選項
- 實作自動審核邏輯
- 更新相關視圖和權限
- 新增單元測試

相關問題: #156"

# 避免的提交訊息
git commit -m "修改檔案"
git commit -m "更新"
git commit -m "fix bug"
```

### Pull Request 流程

#### 1. 準備 Pull Request
```bash
# 1. 確保分支是最新的
git checkout main
git pull upstream main
git checkout feature/your-feature
git rebase main

# 2. 執行測試
python -m pytest tests/
flake8 .
black --check .

# 3. 推送分支
git push origin feature/your-feature
```

#### 2. 建立 Pull Request
```markdown
## 變更描述
簡潔描述這個 PR 的變更內容

## 變更類型
- [ ] 錯誤修復
- [ ] 新功能
- [ ] 程式碼重構
- [ ] 效能優化
- [ ] 文檔更新
- [ ] 測試改善

## 測試
- [ ] 已執行現有測試
- [ ] 已新增新的測試
- [ ] 已手動測試功能

## 檢查清單
- [ ] 程式碼遵循專案規範
- [ ] 已更新相關文檔
- [ ] 已新增或更新測試
- [ ] 所有測試通過
- [ ] 已檢查向後相容性

## 相關問題
關閉 #123

## 螢幕截圖
如果適用，請附上螢幕截圖

## 其他資訊
任何其他相關資訊
```

#### 3. 程式碼審查
- 回應審查意見
- 進行必要的修改
- 保持討論的建設性
- 學習和改進

### 程式碼審查標準

#### 審查者檢查清單

##### 功能性
- [ ] 功能是否按預期工作
- [ ] 是否處理了邊界情況
- [ ] 錯誤處理是否適當
- [ ] 是否有安全問題

##### 程式碼品質
- [ ] 程式碼是否清晰易讀
- [ ] 是否遵循專案規範
- [ ] 是否有重複程式碼
- [ ] 變數和函數命名是否合適

##### 測試
- [ ] 是否有足夠的測試覆蓋
- [ ] 測試是否有意義
- [ ] 是否測試了錯誤情況
- [ ] 測試是否可靠

##### 文檔
- [ ] 是否更新了相關文檔
- [ ] 程式碼註解是否充足
- [ ] API 文檔是否完整
- [ ] 變更日誌是否更新

#### 審查回饋範例
```markdown
# 建設性的回饋
## 建議
這個函數可能會在大量資料時效能不佳，建議考慮使用批次處理：

```python
# 目前的實作
for record in records:
    record.process()

# 建議的實作
records.batch_process()
```

## 問題
這裡缺少錯誤處理，如果 API 呼叫失敗會怎麼樣？

## 讚賞
這個測試案例很全面，涵蓋了各種邊界情況！
```

## 文檔貢獻指南

### 文檔類型

#### 1. 技術文檔
- API 參考文檔
- 架構設計文檔
- 開發指南
- 部署指南

#### 2. 使用者文檔
- 使用手冊
- 快速入門指南
- 常見問題
- 故障排除

#### 3. 貢獻文檔
- 貢獻指南
- 程式碼規範
- 發布流程
- 社群準則

### 文檔撰寫規範

#### Markdown 格式規範
```markdown
# 一級標題

## 二級標題

### 三級標題

#### 四級標題

## 程式碼區塊
```python
# Python 程式碼
def example_function():
    return "Hello, World!"
```

## 表格
| 欄位 | 類型 | 描述 |
|------|------|------|
| name | Char | 名稱 |
| date | Date | 日期 |

## 清單
- 項目 1
- 項目 2
  - 子項目 2.1
  - 子項目 2.2

## 連結
[連結文字](URL)
[內部連結](../other-doc.md)

## 圖片
![替代文字](image-path.png)

## 警告區塊
> **注意**: 這是重要資訊
> 
> **警告**: 這是警告資訊
```

#### 文檔結構規範
```markdown
# 文檔標題

## 概述
簡潔描述文檔內容和目的

## 目錄 (如果需要)
- [章節 1](#章節-1)
- [章節 2](#章節-2)

## 主要內容
### 章節 1
內容...

### 章節 2
內容...

## 範例
實際使用範例

## 常見問題
常見問題和解答

## 相關資源
- [相關文檔 1](link1)
- [相關文檔 2](link2)

---
**最後更新**: YYYY-MM-DD
**版本**: x.x.x
```

### 文檔翻譯

#### 翻譯流程
1. 選擇要翻譯的文檔
2. 建立翻譯分支
3. 進行翻譯工作
4. 提交 Pull Request
5. 審查和合併

#### 翻譯規範
- 保持原文的結構和格式
- 使用一致的術語翻譯
- 考慮目標語言的文化背景
- 保持技術術語的準確性

## 社群參與方式

### 溝通管道

#### 1. GitHub
- Issues: 問題回報和功能請求
- Discussions: 一般討論和問答
- Pull Requests: 程式碼審查和討論

#### 2. 社群平台
- Discord/Slack: 即時討論
- 論壇: 深度技術討論
- 郵件清單: 重要公告

#### 3. 社交媒體
- Twitter: 專案動態
- LinkedIn: 專業網路
- 部落格: 技術文章

### 社群準則

#### 行為準則
1. **尊重他人**: 尊重不同的觀點和經驗
2. **建設性溝通**: 提供有建設性的回饋
3. **包容性**: 歡迎所有背景的貢獻者
4. **專業態度**: 保持專業和友善的態度

#### 溝通原則
- 使用清晰、簡潔的語言
- 提供具體的範例和說明
- 承認他人的貢獻
- 耐心回答新手問題

### 社群活動

#### 1. 定期會議
- 每月開發者會議
- 季度規劃會議
- 年度社群大會

#### 2. 活動參與
- 技術研討會
- 開源活動
- 使用者聚會
- 線上工作坊

#### 3. 知識分享
- 撰寫技術部落格
- 製作教學影片
- 參與播客訪談
- 舉辦技術分享

## 認可和獎勵

### 貢獻者認可

#### 1. 貢獻者清單
- README 中的貢獻者名單
- 發布說明中的感謝
- 專案網站的貢獻者頁面

#### 2. 特殊認可
- 月度最佳貢獻者
- 年度傑出貢獻獎
- 特殊成就徽章

#### 3. 職涯發展
- 推薦信
- 技能認證
- 就業機會介紹
- 會議演講機會

### 維護者晉升

#### 晉升條件
1. **持續貢獻**: 至少 6 個月的活躍貢獻
2. **程式碼品質**: 高品質的程式碼貢獻
3. **社群參與**: 積極參與社群討論和幫助他人
4. **技術能力**: 展現深度的技術理解
5. **責任感**: 可靠和負責任的態度

#### 維護者職責
- 審查 Pull Requests
- 管理 Issues 和 Discussions
- 指導新貢獻者
- 參與技術決策
- 維護專案品質

## 發布流程

### 版本控制

#### 語意化版本
```
主版本.次版本.修訂版本 (MAJOR.MINOR.PATCH)

- MAJOR: 不相容的 API 變更
- MINOR: 向後相容的功能新增
- PATCH: 向後相容的錯誤修復
```

#### 版本範例
- `1.0.0`: 首次穩定發布
- `1.1.0`: 新增功能
- `1.1.1`: 錯誤修復
- `2.0.0`: 重大變更

### 發布準備

#### 1. 程式碼準備
```bash
# 1. 確保所有測試通過
python -m pytest tests/

# 2. 更新版本號
# 編輯 __manifest__.py
'version': '1.1.0',

# 3. 更新變更日誌
# 編輯 CHANGELOG.md
```

#### 2. 文檔更新
- 更新 README.md
- 更新 API 文檔
- 更新使用手冊
- 檢查連結有效性

#### 3. 發布檢查清單
- [ ] 所有測試通過
- [ ] 文檔已更新
- [ ] 版本號已更新
- [ ] 變更日誌已更新
- [ ] 安全檢查完成
- [ ] 效能測試通過

### 發布執行

#### 1. 建立發布
```bash
# 1. 建立發布分支
git checkout -b release/v1.1.0

# 2. 最終測試
python -m pytest tests/

# 3. 合併到主分支
git checkout main
git merge --no-ff release/v1.1.0

# 4. 建立標籤
git tag -a v1.1.0 -m "Release version 1.1.0"

# 5. 推送變更
git push origin main --tags
```

#### 2. 發布公告
```markdown
# l10n_tw_hr_payroll v1.1.0 發布

我們很高興宣布 l10n_tw_hr_payroll v1.1.0 正式發布！

## 新功能
- 新增自動審核功能
- 改善薪資計算效能
- 支援新的假日類型

## 錯誤修復
- 修復加班時數計算問題
- 解決權限檢查錯誤
- 改善資料庫查詢效能

## 升級說明
請參考 [升級指南](docs/upgrade-guide.md) 進行升級。

## 感謝
感謝所有貢獻者的努力！

完整變更日誌請參考 [CHANGELOG.md](CHANGELOG.md)
```

## 聯絡資訊

### 專案維護者
- **主要維護者**: <EMAIL>
- **技術負責人**: [GitHub 用戶名]
- **社群管理**: [聯絡資訊]

### 支援管道
- **GitHub Issues**: 技術問題和錯誤回報
- **GitHub Discussions**: 一般討論和問答
- **電子郵件**: 私人或敏感問題
- **社群聊天**: 即時討論和協助

---

**感謝您對 l10n_tw_hr_payroll 專案的貢獻！**

每一個貢獻，無論大小，都讓這個專案變得更好。我們期待與您一起建設更強大的台灣薪資管理解決方案。

**最後更新**: 2025-06-12  
**版本**: 1.0  
**適用專案版本**: l10n_tw_hr_payroll v1.0+