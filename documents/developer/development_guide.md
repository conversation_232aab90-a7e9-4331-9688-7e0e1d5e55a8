# 開發指南

## 概述
本文檔提供 l10n_tw_hr_payroll 模組的完整開發指南，包含開發環境設定、程式碼結構說明、開發規範和最佳實踐。

## 開發環境設定

### 系統需求

#### 基礎環境
- **作業系統**: Ubuntu 20.04+ / CentOS 8+ / macOS 10.15+
- **Python**: 3.8+ (建議 3.10+)
- **PostgreSQL**: 12+ (建議 14+)
- **Node.js**: 16+ (用於前端資源編譯)
- **Git**: 2.25+

#### Odoo 環境
- **Odoo**: 18.0+ (Community 或 Enterprise)
- **記憶體**: 最少 4GB，建議 8GB+
- **硬碟空間**: 最少 10GB 可用空間

### 開發環境安裝

#### 1. 安裝 Odoo 18

##### 使用 Docker (推薦)
```bash
# 1. 建立開發目錄
mkdir -p ~/odoo-dev/addons
cd ~/odoo-dev

# 2. 下載 Odoo 18
git clone --depth 1 --branch 18.0 https://github.com/odoo/odoo.git

# 3. 建立 Docker Compose 檔案
cat > docker-compose.yml << EOF
version: '3.8'
services:
  db:
    image: postgres:14
    environment:
      POSTGRES_DB: odoo
      POSTGRES_USER: odoo
      POSTGRES_PASSWORD: odoo
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  odoo:
    image: odoo:18.0
    depends_on:
      - db
    ports:
      - "8069:8069"
    volumes:
      - ./odoo:/usr/lib/python3/dist-packages/odoo
      - ./addons:/mnt/extra-addons
      - ./config:/etc/odoo
    environment:
      - HOST=db
      - USER=odoo
      - PASSWORD=odoo

volumes:
  postgres_data:
EOF

# 4. 啟動服務
docker-compose up -d
```

##### 本地安裝
```bash
# 1. 安裝相依套件 (Ubuntu)
sudo apt update
sudo apt install python3-pip python3-dev python3-venv
sudo apt install postgresql postgresql-contrib
sudo apt install libxml2-dev libxslt1-dev libevent-dev libsasl2-dev libldap2-dev

# 2. 建立 Python 虛擬環境
python3 -m venv ~/odoo-venv
source ~/odoo-venv/bin/activate

# 3. 下載並安裝 Odoo
git clone --depth 1 --branch 18.0 https://github.com/odoo/odoo.git ~/odoo18
cd ~/odoo18
pip install -r requirements.txt

# 4. 設定 PostgreSQL
sudo -u postgres createuser -s $USER
createdb odoo18
```

#### 2. 設定開發配置

##### 建立 Odoo 配置檔
```bash
# 建立配置目錄
mkdir -p ~/odoo-dev/config

# 建立配置檔案
cat > ~/odoo-dev/config/odoo.conf << EOF
[options]
# 資料庫設定
db_host = localhost
db_port = 5432
db_user = odoo
db_password = odoo

# 伺服器設定
http_port = 8069
workers = 0

# 開發設定
dev_mode = reload,qweb,werkzeug,xml
log_level = debug
log_handler = :DEBUG

# 模組路徑
addons_path = /path/to/odoo/addons,/path/to/extra-addons

# 安全設定
admin_passwd = admin123
list_db = True

# 效能設定
limit_memory_hard = **********
limit_memory_soft = **********
limit_request = 8192
limit_time_cpu = 600
limit_time_real = 1200
EOF
```

#### 3. IDE 設定

##### VS Code 設定
```json
// .vscode/settings.json
{
    "python.defaultInterpreter": "~/odoo-venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": true,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "black",
    "python.sortImports.args": ["--profile", "black"],
    "files.associations": {
        "*.xml": "xml",
        "*.rng": "xml"
    },
    "xml.validation.enabled": true,
    "xml.format.enabled": true
}
```

##### PyCharm 設定
1. 開啟專案目錄
2. 設定 Python 解譯器為虛擬環境
3. 設定程式碼風格為 Black
4. 啟用 XML 語法檢查

### 開發工具安裝

#### 程式碼品質工具
```bash
# 啟用虛擬環境
source ~/odoo-venv/bin/activate

# 安裝開發工具
pip install black flake8 pylint isort pre-commit

# 安裝 Odoo 開發工具
pip install odoo-tools click-odoo-contrib
```

#### Git Hooks 設定
```bash
# 建立 .pre-commit-config.yaml
cat > .pre-commit-config.yaml << EOF
repos:
  - repo: https://github.com/psf/black
    rev: 22.3.0
    hooks:
      - id: black
        language_version: python3

  - repo: https://github.com/pycqa/isort
    rev: 5.10.1
    hooks:
      - id: isort
        args: ["--profile", "black"]

  - repo: https://github.com/pycqa/flake8
    rev: 4.0.1
    hooks:
      - id: flake8
        args: ["--max-line-length=88", "--extend-ignore=E203,W503"]

  - repo: local
    hooks:
      - id: odoo-lint
        name: Odoo Lint
        entry: python -m odoo_tools.lint
        language: system
        files: \.py$
EOF

# 安裝 pre-commit hooks
pre-commit install
```

## 程式碼結構說明

### 模組架構
```
l10n_tw_hr_payroll/
├── __init__.py                 # 模組初始化
├── __manifest__.py             # 模組清單檔
├── controllers/                # 控制器
│   └── __init__.py
├── data/                       # 資料檔案
│   ├── attendance_cron_data.xml
│   ├── holiday_data.xml
│   └── payroll_structure_data.xml
├── models/                     # 模型定義
│   ├── __init__.py
│   ├── hr_employee_extension.py
│   ├── hr_attendance_extension.py
│   ├── overtime_management.py
│   ├── payslip_extension.py
│   ├── working_calendar.py
│   └── holiday_calendar.py
├── reports/                    # 報表
│   └── payslip_report_templates.xml
├── security/                   # 安全設定
│   ├── ir.model.access.csv
│   ├── tw_payroll_groups.xml
│   └── tw_payroll_security.xml
├── static/                     # 靜態資源
│   └── description/
│       └── icon.png
├── views/                      # 視圖定義
│   ├── menuitems.xml
│   ├── hr_employee_views.xml
│   ├── hr_attendance_views.xml
│   ├── overtime_views.xml
│   ├── payslip_views.xml
│   ├── working_calendar_views.xml
│   ├── holiday_import_views.xml
│   └── ~~overtime_integration_views.xml~~ **[已移除]**
├── wizards/                    # 嚮導
│   ├── __init__.py
│   ├── holiday_import_wizard.py
│   ├── holiday_import_wizard_views.xml
│   ├── makeup_day_wizard.py
│   ├── makeup_day_wizard_views.xml
│   ├── overtime_limit_wizard.py
│   └── overtime_limit_wizard_views.xml
└── documents/                  # 文檔
    ├── README.md
    ├── technical/
    ├── user/
    └── developer/
```

### 檔案命名規範

#### Python 檔案
- **模型檔案**: `{model_name}.py` (例: `hr_employee_extension.py`)
- **嚮導檔案**: `{wizard_name}_wizard.py` (例: `holiday_import_wizard.py`)
- **控制器檔案**: `{controller_name}_controller.py`

#### XML 檔案
- **視圖檔案**: `{model_name}_views.xml` (例: `hr_employee_views.xml`)
- **資料檔案**: `{data_type}_data.xml` (例: `holiday_data.xml`)
- **安全檔案**: `{security_type}.xml` (例: `tw_payroll_groups.xml`)

#### 模型命名
- **繼承模型**: 保持原模型名稱 (例: `hr.employee`)
- **新模型**: 使用前綴 `tw.` (例: `tw.hr.attendance (原 tw.overtime.record 已移除)`)

### 程式碼組織原則

#### 1. 模型組織
```python
# models/hr_employee_extension.py
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError

class HrEmployee(models.Model):
    _inherit = 'hr.employee'
    
    # 1. 欄位定義 (按類型分組)
    # 台灣特有欄位
    tw_employee_id = fields.Char(...)
    national_id = fields.Char(...)
    
    # 薪資相關欄位
    monthly_salary = fields.Monetary(...)
    hourly_rate = fields.Monetary(...)
    
    # 計算欄位
    current_month_overtime = fields.Float(...)
    
    # 2. 約束條件
    @api.constrains('national_id')
    def _check_national_id(self):
        pass
    
    # 3. 計算方法
    @api.depends('monthly_salary', 'standard_working_hours')
    def _compute_hourly_rate(self):
        pass
    
    # 4. 業務邏輯方法
    def get_working_calendar(self):
        pass
    
    # 5. 覆寫方法
    @api.model
    def create(self, vals):
        pass
```

#### 2. 視圖組織
```xml
<!-- views/hr_employee_views.xml -->
<odoo>
    <!-- 1. 表單視圖 -->
    <record id="view_employee_form_tw_payroll" model="ir.ui.view">
        <field name="name">hr.employee.form.tw.payroll</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_form"/>
        <field name="arch" type="xml">
            <!-- 視圖定義 -->
        </field>
    </record>
    
    <!-- 2. 列表視圖 -->
    <record id="view_employee_list_tw_payroll" model="ir.ui.view">
        <!-- 列表視圖定義 -->
    </record>
    
    <!-- 3. 搜尋視圖 -->
    <record id="view_employee_search_tw_payroll" model="ir.ui.view">
        <!-- 搜尋視圖定義 -->
    </record>
</odoo>
```

## 開發規範和最佳實踐

### 程式碼風格

#### Python 程式碼規範
遵循 [PEP 8](https://pep8.org/) 和 [Odoo 開發指南](https://www.odoo.com/documentation/18.0/developer/reference/backend/guidelines.html)：

```python
# 1. 匯入順序
import os
import logging
from datetime import datetime, timedelta

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError

# 2. 類別定義
class TwOvertimeRecord(models.Model):
    """台灣加班記錄模型
    
    管理員工的加班申請、審核和統計功能。
    支援自動計算加班時數和費率。
    """
    _name = 'tw.hr.attendance (原 tw.overtime.record 已移除)'
    _description = '台灣加班記錄'
    _order = 'date desc, employee_id'
    _rec_name = 'name'
    
    # 3. 欄位定義 (按邏輯分組)
    name = fields.Char(
        string='記錄名稱',
        compute='_compute_name',
        store=True,
        help='自動生成的記錄名稱'
    )
    
    # 4. 方法定義
    @api.depends('employee_id', 'date', 'actual_overtime_hours')
    def _compute_name(self):
        """計算記錄名稱"""
        for record in self:
            if record.employee_id and record.date:
                record.name = f"{record.employee_id.name} - {record.date} ({record.actual_overtime_hours}h)"
            else:
                record.name = _('新加班記錄')
```

#### XML 程式碼規範
```xml
<!-- 1. 檔案結構 -->
<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- 2. 視圖定義 -->
    <record id="view_overtime_record_form" model="ir.ui.view">
        <field name="name">tw.hr.attendance (原 tw.overtime.record 已移除).form</field>
        <field name="model">tw.hr.attendance (原 tw.overtime.record 已移除)</field>
        <field name="arch" type="xml">
            <form string="加班記錄">
                <!-- 3. 表單結構 -->
                <header>
                    <!-- 按鈕和狀態列 -->
                </header>
                <sheet>
                    <!-- 主要內容 -->
                    <group>
                        <group>
                            <!-- 左欄欄位 -->
                        </group>
                        <group>
                            <!-- 右欄欄位 -->
                        </group>
                    </group>
                </sheet>
                <div class="oe_chatter">
                    <!-- 訊息和活動 -->
                </div>
            </form>
        </field>
    </record>
</odoo>
```

### 資料庫設計原則

#### 1. 欄位命名
```python
# 好的命名
monthly_salary = fields.Monetary('月薪')
overtime_rate = fields.Float('加班費率')
is_working_day = fields.Boolean('是否為工作日')

# 避免的命名
salary = fields.Monetary('薪水')  # 不夠明確
rate = fields.Float('費率')       # 太模糊
working = fields.Boolean('工作')   # 不清楚
```

#### 2. 關聯關係
```python
# Many2one 關聯
employee_id = fields.Many2one(
    'hr.employee',
    string='員工',
    required=True,
    ondelete='cascade',
    index=True
)

# One2many 關聯
overtime_record_ids = fields.One2many(
    'tw.hr.attendance (原 tw.overtime.record 已移除)',
    'employee_id',
    string='加班記錄'
)

# Many2many 關聯
department_ids = fields.Many2many(
    'hr.department',
    'tw_overtime_limit_department_rel',
    'limit_id',
    'department_id',
    string='適用部門'
)
```

#### 3. 索引設計
```python
# 在 _sql_constraints 中定義唯一約束
_sql_constraints = [
    ('unique_national_id', 'UNIQUE(national_id)', '身分證字號必須唯一'),
    ('positive_overtime_hours', 'CHECK(actual_overtime_hours >= 0)', '加班時數不能為負數'),
]

# 在欄位定義中加入索引
date = fields.Date('日期', required=True, index=True)
employee_id = fields.Many2one('hr.employee', index=True)
```

### 安全性最佳實踐

#### 1. 權限設計
```python
# 使用記錄規則限制資料存取
<record id="overtime_record_rule_employee" model="ir.rule">
    <field name="name">員工只能看到自己的加班記錄</field>
    <field name="model_id" ref="model_tw_overtime_record"/>
    <field name="domain_force">[('employee_id.user_id', '=', user.id)]</field>
    <field name="groups" eval="[(4, ref('group_tw_payroll_user'))]"/>
</record>
```

#### 2. 資料驗證
```python
@api.constrains('actual_overtime_hours')
def _check_overtime_hours(self):
    """驗證加班時數合理性"""
    for record in self:
        if record.actual_overtime_hours < 0:
            raise ValidationError(_('加班時數不能為負數'))
        if record.actual_overtime_hours > 24:
            raise ValidationError(_('單日加班時數不能超過24小時'))

@api.constrains('national_id')
def _check_national_id_format(self):
    """驗證身分證字號格式"""
    import re
    for record in self:
        if record.national_id:
            if not re.match(r'^[A-Z][0-9]{9}$', record.national_id):
                raise ValidationError(_('身分證字號格式錯誤'))
```

#### 3. SQL 注入防護
```python
# 好的做法 - 使用參數化查詢
def _get_overtime_summary(self, employee_id, date_from, date_to):
    self.env.cr.execute("""
        SELECT SUM(actual_overtime_hours)
        FROM tw_overtime_record
        WHERE employee_id = %s
        AND date BETWEEN %s AND %s
        AND state = 'approved'
    """, (employee_id, date_from, date_to))
    
# 避免的做法 - 字串拼接
def _get_overtime_summary_bad(self, employee_id, date_from, date_to):
    query = f"""
        SELECT SUM(actual_overtime_hours)
        FROM tw_overtime_record
        WHERE employee_id = {employee_id}
        AND date BETWEEN '{date_from}' AND '{date_to}'
    """  # 容易受到 SQL 注入攻擊
```

### 效能優化

#### 1. 查詢優化
```python
# 好的做法 - 批次查詢
def _compute_monthly_overtime(self):
    """批次計算月加班時數"""
    if not self:
        return
    
    # 一次查詢所有需要的資料
    overtime_data = self.env['tw.hr.attendance (原 tw.overtime.record 已移除)'].read_group(
        [('employee_id', 'in', self.ids),
         ('date', '>=', fields.Date.today().replace(day=1)),
         ('state', '=', 'approved')],
        ['employee_id', 'actual_overtime_hours:sum'],
        ['employee_id']
    )
    
    # 建立查詢結果對應表
    overtime_map = {data['employee_id'][0]: data['actual_overtime_hours'] for data in overtime_data}
    
    # 設定計算結果
    for employee in self:
        employee.current_month_overtime = overtime_map.get(employee.id, 0.0)

# 避免的做法 - 逐筆查詢
def _compute_monthly_overtime_bad(self):
    """逐筆計算月加班時數 (效能差)"""
    for employee in self:
        records = self.env['tw.hr.attendance (原 tw.overtime.record 已移除)'].search([
            ('employee_id', '=', employee.id),
            ('date', '>=', fields.Date.today().replace(day=1)),
            ('state', '=', 'approved')
        ])
        employee.current_month_overtime = sum(records.mapped('actual_overtime_hours'))
```

#### 2. 快取使用
```python
from functools import lru_cache

class HrEmployee(models.Model):
    _inherit = 'hr.employee'
    
    @lru_cache(maxsize=128)
    def _get_working_calendar_cached(self, date):
        """快取工作行事曆查詢結果"""
        return self.get_working_calendar().is_working_day(date)
```

## 新功能開發流程

### 1. 需求分析
1. **需求收集**: 明確功能需求和業務邏輯
2. **技術評估**: 評估技術可行性和影響範圍
3. **設計規劃**: 設計資料模型和介面

### 2. 開發準備
```bash
# 1. 建立功能分支
git checkout -b feature/new-feature-name

# 2. 更新開發環境
git pull origin 18.0
pip install -r requirements.txt

# 3. 建立測試資料庫
createdb odoo_test_new_feature
```

### 3. 開發實作

#### 步驟 1: 建立模型
```python
# models/new_feature.py
from odoo import models, fields, api

class NewFeature(models.Model):
    _name = 'tw.new.feature'
    _description = '新功能'
    
    name = fields.Char('名稱', required=True)
    # 其他欄位定義
```

#### 步驟 2: 建立視圖
```xml
<!-- views/new_feature_views.xml -->
<odoo>
    <record id="view_new_feature_form" model="ir.ui.view">
        <!-- 視圖定義 -->
    </record>
</odoo>
```

#### 步驟 3: 設定權限
```csv
# security/ir.model.access.csv
id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_tw_new_feature_user,tw.new.feature.user,model_tw_new_feature,group_tw_payroll_user,1,0,0,0
```

#### 步驟 4: 更新 __manifest__.py
```python
# __manifest__.py
{
    'data': [
        # 現有檔案...
        'views/new_feature_views.xml',
        'security/ir.model.access.csv',
    ],
}
```

### 4. 測試驗證
```bash
# 1. 執行單元測試
python -m pytest tests/test_new_feature.py

# 2. 執行整合測試
python -m odoo -d odoo_test_new_feature -i l10n_tw_hr_payroll --test-enable

# 3. 手動測試
python -m odoo -d odoo_test_new_feature --dev=reload,qweb,werkzeug,xml
```

### 5. 程式碼審查準備
```bash
# 1. 程式碼格式化
black .
isort .

# 2. 程式碼檢查
flake8 .
pylint models/ wizards/

# 3. 提交變更
git add .
git commit -m "feat: 新增新功能模組"
git push origin feature/new-feature-name
```

## 程式碼審查標準

### 審查檢查清單

#### 1. 程式碼品質
- [ ] 遵循 PEP 8 程式碼風格
- [ ] 變數和函數命名清晰
- [ ] 程式碼註解充足
- [ ] 無重複程式碼
- [ ] 錯誤處理完整

#### 2. Odoo 特定檢查
- [ ] 模型定義正確
- [ ] 視圖結構合理
- [ ] 權限設定適當
- [ ] 翻譯字串完整
- [ ] 相依性正確

#### 3. 安全性檢查
- [ ] 輸入驗證完整
- [ ] SQL 查詢安全
- [ ] 權限控制正確
- [ ] 敏感資料保護

#### 4. 效能檢查
- [ ] 查詢效率良好
- [ ] 避免 N+1 查詢
- [ ] 適當使用索引
- [ ] 記憶體使用合理

### 審查流程
1. **自我審查**: 開發者自行檢查程式碼
2. **同儕審查**: 其他開發者進行程式碼審查
3. **技術主管審查**: 技術主管最終審查
4. **測試驗證**: 通過所有測試後合併

## Git 工作流程

### 分支策略
```
main (生產環境)
├── develop (開發環境)
│   ├── feature/new-feature-1
│   ├── feature/new-feature-2
│   └── hotfix/urgent-fix
└── release/v1.1.0
```

### 分支命名規範
- **功能分支**: `feature/功能名稱`
- **修復分支**: `bugfix/問題描述`
- **熱修復**: `hotfix/緊急修復`
- **發布分支**: `release/版本號`

### 提交訊息規範
```
類型(範圍): 簡短描述

詳細描述 (可選)

相關問題: #123
```

#### 提交類型
- `feat`: 新功能
- `fix`: 錯誤修復
- `docs`: 文檔更新
- `style`: 程式碼格式調整
- `refactor`: 程式碼重構
- `test`: 測試相關
- `chore`: 建置或輔助工具變更

#### 範例
```bash
git commit -m "feat(overtime): 新增自動審核功能

- 新增自動審核設定選項
- 實作自動審核邏輯
- 更新相關視圖和權限

相關問題: #156"
```

### 發布流程
```bash
# 1. 建立發布分支
git checkout -b release/v1.1.0 develop

# 2. 更新版本號
# 編輯 __manifest__.py 中的版本號

# 3. 執行完整測試
python -m pytest
python -m odoo --test-enable

# 4. 合併到主分支
git checkout main
git merge --no-ff release/v1.1.0

# 5. 建立標籤
git tag -a v1.1.0 -m "Release version 1.1.0"

# 6. 推送變更
git push origin main --tags
```

## 常見問題和解決方案

### 開發環境問題

#### 1. 模組載入失敗
```bash
# 檢查模組路徑
python -c "import sys; print(sys.path)"

# 重新安裝模組
python -m odoo -d database_name -i l10n_tw_hr_payroll --stop-after-init

# 檢查日誌
tail -f /var/log/odoo/odoo.log
```

#### 2. 資料庫遷移問題
```bash
# 備份資料庫
pg_dump database_name > backup.sql

# 更新模組
python -m odoo -d database_name -u l10n_tw_hr_payroll --stop-after-init

# 如果失敗，恢復備份
dropdb database_name
createdb database_name
psql database_name < backup.sql
```

#### 3. 權限問題
```python
# 檢查使用者群組
user = self.env.user
groups = user.groups_id.mapped('name')
print(f"使用者群組: {groups}")

# 檢查記錄規則
rules = self.env['ir.rule'].search([('model_id.model', '=', 'tw.hr.attendance (原 tw.overtime.record 已移除)')])
for rule in rules:
    print(f"規則: {rule.name}, 條件: {rule.domain_force}")
```

### 效能問題

#### 1. 查詢效能優化
```python
# 使用 explain 分析查詢
self.env.cr.execute("EXPLAIN ANALYZE SELECT * FROM tw_overtime_record WHERE employee_id = %s", (employee_id,))
result = self.env.cr.fetchall()
print(result)

# 新增索引
self.env.cr.execute("CREATE INDEX IF NOT EXISTS idx_overtime_employee_date ON tw_overtime_record(employee_id, date)")
```

#### 2. 記憶體使用優化
```python
# 使用生成器處理大量資料
def process_large_dataset(self):
    batch_size = 1000
    offset = 0
    
    while True:
        records = self.search([], limit=batch_size, offset=offset)
        if not records:
            break
            
        for record in records:
            # 處理記錄
            pass
            
        offset += batch_size
        # 清理快取
        self.env.clear()
```

## 相關資源

### 官方文檔
- [Odoo