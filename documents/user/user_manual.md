# 台灣薪資管理系統 - 使用手冊

## 系統概覽

### 系統介紹
台灣薪資管理系統 (l10n_tw_hr_payroll) 是專為台灣企業設計的薪資管理解決方案，完全符合台灣勞基法規範，提供完整的出勤管理、加班計算、薪資處理功能。

### 主要功能
- ✅ **智慧出勤管理**: 自動計算加班時數，支援台灣勞基法規範
- ✅ **彈性加班管理**: 完整的加班申請、審核和統計功能
- ✅ **雙薪資單機制**: 支援標準版和完整版薪資單
- ✅ **假日管理**: 支援假日匯入和補班日設定
- ✅ **權限控制**: 多層級權限管理，保護敏感薪資資訊

### 系統架構
```
台灣薪資管理系統
├── 出勤管理
│   ├── 打卡記錄
│   ├── 加班計算
│   └── 工作時間管理
├── 加班管理
│   ├── 加班申請
│   ├── 審核流程
│   └── 時數限制
├── 薪資管理
│   ├── 薪資計算
│   ├── 雙薪資單
│   └── 法定扣除
└── 系統設定
    ├── 假日管理
    ├── 工作行事曆
    └── 權限設定
```

## 權限角色說明

### 權限群組架構
系統採用分層權限管理，確保資料安全和操作權限的合理分配。

#### 基礎權限群組

##### 1. 台灣薪資使用者
**適用對象**: 一般員工
**主要權限**:
- 查看自己的薪資單和加班記錄
- 提交加班申請
- 查看自己的出勤記錄

**操作範圍**: 僅限個人資料

##### 2. 部門主管
**適用對象**: 各部門主管
**主要權限**:
- 查看部門員工的加班記錄
- 審核部門員工的加班申請
- 查看部門薪資統計

**操作範圍**: 所管理的部門

##### 3. 薪資管理員
**適用對象**: 人資薪資專員
**主要權限**:
- 管理所有員工的薪資和加班記錄
- 生成和管理薪資單
- 設定加班限制規則
- 執行批次操作

**操作範圍**: 全公司員工

##### 4. 超級管理員
**適用對象**: 系統管理員、高階主管
**主要權限**:
- 擁有所有功能的完整權限
- 查看完整的加班時數（包含隱藏時數）
- 系統設定和配置
- 權限管理

**操作範圍**: 系統全部功能

#### 功能特定權限群組

##### 假日管理員
- 管理假日和補班日設定
- 匯入假日資料
- 設定工作行事曆

##### 出勤整合管理員
- 管理出勤與加班記錄的整合
- 處理出勤異常
- 執行定時任務

##### 完整權限存取
- 查看完整的加班時數資訊
- 存取隱藏的薪資資料
- 生成完整版薪資單

## 功能模組使用說明

### 1. 出勤管理

#### 1.1 打卡記錄管理

##### 查看出勤記錄
1. 進入 **人力資源 > 出勤 > 出勤記錄**
2. 可以看到所有出勤記錄列表
3. 使用篩選器快速查找：
   - **有加班時數**: 顯示有加班的記錄
   - **未創建加班記錄**: 顯示需要處理的記錄
   - **平日加班** / **假日加班**: 按加班類型篩選

##### 出勤記錄詳細資訊
點擊任一出勤記錄，可查看：
- **基本資訊**: 員工、打卡時間、工作時數
- **台灣加班資訊**: 加班時數、加班類型、顯示時數
- **工作日資訊**: 是否為工作日、預期工作時數
- **本地時間**: 轉換為台灣時區的時間顯示

##### 手動創建加班記錄
對於系統未自動創建加班記錄的出勤：
1. 開啟出勤記錄詳細頁面
2. 點擊 **創建加班記錄** 按鈕
3. 系統會自動根據出勤時間計算加班時數
4. 創建對應的加班申請記錄

#### 1.2 加班時數計算邏輯

##### 智慧休息時間計算
系統採用動態休息時間計算機制，自動從工作行事曆中計算所有休息時段：
- **支援多休息時段**: 可處理午休、晚班休息等多個時段
- **動態時間計算**: 根據實際工作行事曆設定自動計算休息時間
- **準確加班計算**: 避免重複扣除休息時間的錯誤

##### 計算公式
```
加班時數 = MAX(0, 實際工作時數 - (標準工作時數 - 休息時數))

其中：
- 實際工作時數 = 下班打卡時間 - 上班打卡時間 - 總休息時數
- 標準工作時數 = 8小時 (可調整)
- 休息時數 = 從工作行事曆動態計算的所有休息時間總和
```

##### 計算範例
```
員工打卡時間：08:00-21:30 (13.5小時)
工作行事曆設定：
- 上午：08:00-12:00 (4小時)
- 午休：12:00-13:00 (1小時休息)
- 下午：13:00-17:00 (4小時)
- 晚班休息：17:00-17:30 (0.5小時休息)
- 晚班：17:30-21:30 (4小時)

計算結果：
- 總休息時數：1 + 0.5 = 1.5小時
- 實際工作時數：13.5 - 1.5 = 12小時
- 標準工作時數：8小時
- 加班時數：12 - 8 = 4小時
```

##### 加班類型判斷
- **平日加班**: 週一至週五的加班
- **週末加班**: 週六、週日的加班
- **假日加班**: 國定假日的加班

### 2. 加班管理

#### 2.1 加班記錄管理

##### 查看加班記錄
1. 進入 **台灣薪資 > 加班管理 > 加班記錄**
2. 系統顯示加班記錄列表，包含：
   - 員工姓名
   - 加班日期
   - 加班類型
   - 實際加班時數
   - 顯示加班時數
   - 審核狀態

##### 加班記錄狀態
- **草稿**: 剛創建的記錄，可以編輯
- **已提交**: 等待主管審核
- **已核准**: 審核通過，納入薪資計算
- **已拒絕**: 審核未通過

##### 手動創建加班記錄
1. 點擊 **新增** 按鈕
2. 填寫必要資訊：
   - **員工**: 選擇加班員工
   - **日期**: 加班日期
   - **實際加班時數**: 加班時數
   - **加班類型**: 平日/週末/假日
3. 點擊 **儲存** 完成創建

#### 2.2 加班審核流程

##### 提交加班申請
1. 開啟草稿狀態的加班記錄
2. 點擊 **提交** 按鈕
3. 記錄狀態變更為 **已提交**

##### 審核加班申請 (主管/管理員)
1. 進入待審核的加班記錄
2. 檢查加班資訊是否正確
3. 選擇操作：
   - **核准**: 點擊 **核准** 按鈕
   - **拒絕**: 點擊 **拒絕** 按鈕並填寫拒絕原因

##### 批次審核功能
對於大量加班記錄：
1. 在加班記錄列表中選擇多筆記錄
2. 點擊 **動作 > 批次核准** 或 **批次拒絕**
3. 系統會批次處理選中的記錄

#### 2.3 自動審核機制

##### 自動審核條件
系統可以自動審核符合以下條件的加班申請：
- 加班時數在設定的自動審核限制內
- 員工有適用的加班限制設定
- 限制設定啟用了自動審核功能

##### 設定自動審核
1. 進入 **台灣薪資 > 加班管理 > 加班限制設定**
2. 創建或編輯限制設定
3. 勾選 **自動審核**
4. 設定 **自動審核時數上限**

### 3. 薪資管理

#### 3.1 雙薪資單機制

##### 薪資單類型
系統提供兩種薪資單：
- **標準版薪資單**: 顯示限制內的加班時數
- **完整版薪資單**: 顯示所有加班時數（需要特殊權限）

##### 生成雙薪資單
1. 進入 **薪資 > 薪資單**
2. 創建新的薪資單
3. 點擊 **生成雙薪資單** 按鈕
4. 系統自動創建標準版和完整版薪資單

##### 查看配對薪資單
在薪資單詳細頁面：
1. 查看 **雙薪資單資訊** 區塊
2. 點擊 **查看配對薪資單** 按鈕
3. 切換查看不同版本的薪資單

#### 3.2 加班費計算

##### 加班費率設定
加班費率現在統一在工作行事曆中設定：
- 進入 **台灣薪資 > 設定 > 工作行事曆**
- 選擇台灣行事曆
- 在 **加班費率設定** 頁籤中設定：
  - **加班費率**: 平日加班前2小時 (預設1.34倍)
  - **延長加班費率**: 平日加班2小時後 (預設1.67倍)
  - **假日加班費率**: 假日加班 (預設2.0倍)

##### 加班費計算公式
```
時薪 = 合約月薪 ÷ 30天 ÷ 8小時
加班費 = 加班時數 × 時薪 × 加班倍率
```

##### 費率取得邏輯
系統會自動從員工的工作行事曆取得對應的加班費率，確保計算的準確性和一致性。

#### 3.3 薪資單管理

##### 薪資單資訊
每份薪資單包含：
- **基本薪資資訊**: 底薪、津貼等
- **加班時數資訊**: 顯示時數、隱藏時數、總時數
- **加班費計算**: 標準加班費、完整加班費
- **法定扣除**: 勞保、健保、勞退等

##### 薪資單確認流程
1. 檢查薪資單內容是否正確
2. 確認加班時數和加班費計算
3. 點擊 **確認** 完成薪資單

### 4. 系統設定

#### 4.1 假日管理

##### 假日匯入功能
1. 進入 **台灣薪資 > 設定 > 假日匯入**
2. 點擊 **新增** 創建匯入任務
3. 填寫匯入設定：
   - **匯入名稱**: 為匯入任務命名
   - **工作行事曆**: 選擇要套用的行事曆
   - **年度**: 設定假日年度
4. 上傳假日檔案 (支援 CSV 和 Excel)
5. 設定欄位對應：
   - **日期欄位名稱**: 假日日期欄位
   - **假日名稱欄位**: 假日名稱欄位
   - **補班日欄位名稱**: 補班日期欄位 (可選)

##### 假日檔案格式
**CSV 格式範例**:
```csv
date,name,makeup_date
2024-01-01,元旦,
2024-02-08,農曆除夕,
2024-02-10,農曆新年,2024-02-17
```

**Excel 格式範例**:
| 日期 | 假日名稱 | 補班日期 |
|------|----------|----------|
| 2024-01-01 | 元旦 | |
| 2024-02-08 | 農曆除夕 | |
| 2024-02-10 | 農曆新年 | 2024-02-17 |

##### 執行假日匯入
1. 點擊 **預覽資料** 檢查匯入內容
2. 確認無誤後點擊 **執行匯入**
3. 系統顯示匯入結果和統計資訊

#### 4.2 補班日設定

##### 手動設定補班日
1. 進入 **台灣薪資 > 設定 > 工作行事曆**
2. 選擇要設定的行事曆
3. 在 **覆蓋設定** 頁籤中新增記錄：
   - **日期**: 補班日期
   - **是否為工作日**: 勾選
   - **覆蓋類型**: 選擇 "補班"
   - **工作時數**: 設定工作時數 (通常為8小時)

##### 使用補班日嚮導
1. 進入補班日設定嚮導
2. 填寫基本資訊：
   - **設定名稱**: 為設定命名
   - **年度**: 設定年度
   - **工作行事曆**: 選擇行事曆
3. 點擊 **生成預設補班日** 自動產生常見補班日
4. 手動調整補班日清單
5. 點擊 **創建補班日** 完成設定

#### 4.3 加班限制設定

##### 創建加班限制
1. 進入 **台灣薪資 > 加班管理 > 加班限制設定**
2. 點擊 **新增** 創建新的限制設定
3. 填寫基本設定：
   - **設定名稱**: 為限制規則命名
   - **每月加班時數上限**: 依勞基法設定 (通常46小時)
   - **顯示時數上限**: 薪資單顯示的時數上限
   - **優先級**: 設定規則優先順序

##### 適用範圍設定
選擇限制規則的適用範圍：
- **全公司**: 套用至所有員工
- **指定部門**: 選擇特定部門
- **指定員工**: 選擇特定員工

##### 自動審核設定
- **自動審核**: 啟用自動審核功能
- **自動審核時數上限**: 設定自動審核的時數門檻

## 常用操作流程

### 流程1: 員工查看個人薪資和加班記錄

1. **登入系統**
   - 使用員工帳號登入 Odoo 系統

2. **查看薪資單**
   - 進入 **薪資 > 我的薪資單**
   - 選擇要查看的薪資期間
   - 檢視薪資明細和加班費

3. **查看加班記錄**
   - 進入 **台灣薪資 > 加班管理 > 加班記錄**
   - 系統自動篩選顯示個人記錄
   - 查看加班時數和審核狀態

### 流程2: 主管審核部門員工加班申請

1. **查看待審核申請**
   - 進入 **台灣薪資 > 加班管理 > 加班記錄**
   - 使用篩選器選擇 **已提交** 狀態
   - 系統顯示部門內待審核的申請

2. **審核個別申請**
   - 點擊要審核的加班記錄
   - 檢查加班資訊是否合理
   - 點擊 **核准** 或 **拒絕**

3. **批次審核**
   - 選擇多筆待審核記錄
   - 點擊 **動作 > 批次核准**
   - 確認批次處理結果

### 流程3: 薪資專員處理月薪資

1. **收集加班資料**
   - 確認當月所有加班記錄已審核完成
   - 檢查出勤記錄是否已正確轉換為加班記錄

2. **生成薪資單**
   - 進入 **薪資 > 薪資單**
   - 創建新的薪資期間
   - 選擇要處理的員工
   - 點擊 **計算薪資單**

3. **生成雙薪資單**
   - 對需要的薪資單點擊 **生成雙薪資單**
   - 檢查標準版和完整版薪資單內容
   - 確認薪資單無誤後進行確認

### 流程4: 管理員設定年度假日

1. **準備假日資料**
   - 下載政府公告的國定假日
   - 整理成 CSV 或 Excel 格式
   - 包含假日日期、名稱和補班日資訊

2. **執行假日匯入**
   - 進入假日匯入嚮導
   - 上傳假日檔案
   - 設定欄位對應
   - 預覽匯入資料

3. **確認匯入結果**
   - 執行匯入操作
   - 檢查匯入統計和錯誤日誌
   - 驗證假日和補班日設定是否正確

## 操作注意事項

### 權限相關注意事項

1. **資料可見性**
   - 一般員工只能看到自己的資料
   - 主管可以看到部門員工的資料
   - 管理員可以看到所有員工的資料

2. **隱藏時數**
   - 只有具備完整權限的使用者才能看到隱藏的加班時數
   - 標準使用者看到的是經過限制處理的顯示時數

3. **操作權限**
   - 不同角色有不同的操作權限
   - 系統會根據使用者權限自動顯示或隱藏功能按鈕

### 資料處理注意事項

1. **出勤記錄**
   - 確保打卡時間正確，避免影響加班時數計算
   - 異常出勤記錄需要手動處理

2. **加班申請**
   - 加班記錄一旦審核通過就會納入薪資計算
   - 如需修改已審核的記錄，需要適當的權限

3. **薪資計算**
   - 薪資單生成前請確認所有加班記錄已完成審核
   - 雙薪資單機制確保資料的完整性和合規性

### 系統效能注意事項

1. **大量資料處理**
   - 批次操作時建議分批處理，避免系統負載過重
   - 大量假日匯入時請在系統負載較低時進行

2. **定時任務**
   - 系統會自動執行定時任務處理出勤記錄
   - 如有異常請聯絡系統管理員

## 系統整合說明

### 與 Odoo 標準模組整合

1. **人力資源模組**
   - 員工基本資料管理
   - 部門和職位設定

2. **出勤模組**
   - 打卡記錄管理
   - 出勤統計

3. **薪資模組**
   - 薪資結構設定
   - 薪資單生成

### 資料同步機制

1. **出勤與加班同步**
   - 出勤記錄自動轉換為加班記錄
   - 支援手動同步和定時同步

2. **加班與薪資同步**
   - 已審核的加班記錄自動納入薪資計算
   - 薪資單生成時自動更新加班資料

## 系統維護

### 定期維護作業

1. **資料備份**
   - 定期備份薪資和加班資料
   - 確保資料安全性

2. **權限檢查**
   - 定期檢查使用者權限設定
   - 確保權限分配合理

3. **效能監控**
   - 監控系統效能
   - 優化查詢和批次處理

### 故障處理

1. **資料不一致**
   - 檢查出勤記錄與加班記錄的一致性
   - 使用同步功能修復資料

2. **權限問題**
   - 檢查使用者群組設定
   - 確認權限規則正確

3. **計算錯誤**
   - 檢查薪資結構和費率設定
   - 驗證加班時數計算邏輯

---

**版本資訊**: v1.0  
**最後更新**: 2025-06-12  
**適用版本**: Odoo 18.0+