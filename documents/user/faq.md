# 台灣薪資管理系統 - 常見問題

**最新更新 (2025-06-17)**：
- ✅ External ID 錯誤已完全修復
- ✅ 模組安裝問題已解決
- ✅ 系統現在可以正常運行

## 系統使用相關問題

### Q1: 我無法看到其他員工的薪資資料，這是正常的嗎？
**A**: 是的，這是正常的安全機制。系統採用分層權限管理：
- **一般員工**：只能查看自己的薪資和加班記錄
- **部門主管**：可以查看部門員工的資料
- **薪資管理員**：可以查看所有員工的資料
- **超級管理員**：擁有完整權限

如需查看其他員工資料，請聯絡系統管理員申請適當權限。

### Q2: 為什麼我的加班時數和實際加班時間不一致？
**A**: 系統採用時數顯示限制機制：
- **顯示時數**：在薪資單上顯示的加班時數（受限制設定影響）
- **隱藏時數**：超過顯示限制的加班時數
- **實際時數**：真實的加班時數

只有具備完整權限的使用者才能看到隱藏時數。這是為了符合台灣勞基法規範和公司政策。

### Q3: 如何申請加班？
**A**: 加班申請有兩種方式：

**自動創建**（推薦）：
1. 正常打卡上下班
2. 系統自動計算加班時數
3. 自動創建加班記錄
4. 等待主管審核

**手動創建**：
1. 進入 **台灣薪資 > 加班管理 > 加班記錄**
2. 點擊 **新增** 創建加班申請
3. 填寫加班資訊
4. 點擊 **提交** 送審

### Q4: 加班申請被拒絕了，該怎麼辦？
**A**: 加班申請被拒絕時：
1. 查看拒絕原因（在加班記錄的詳細頁面）
2. 確認加班資訊是否正確
3. 如有疑問，請聯絡直屬主管
4. 必要時可以重新提交修正後的申請

### Q5: 為什麼我看不到「生成雙薪資單」按鈕？
**A**: 雙薪資單功能需要特定權限：
- 需要 **薪資管理員** 或更高權限
- 薪資單必須處於 **草稿** 狀態
- 不能已經有配對的薪資單

如需使用此功能，請聯絡薪資管理員或系統管理員。

## 出勤和打卡相關問題

### Q6: 忘記打卡怎麼辦？
**A**: 忘記打卡的處理方式：
1. 立即聯絡直屬主管或人資部門
2. 說明忘記打卡的原因和實際工作時間
3. 主管或人資可以手動補登出勤記錄
4. 系統會根據補登的時間計算加班時數

### Q7: 打卡時間錯誤，如何修正？
**A**: 打卡時間修正流程：
1. 聯絡有權限的主管或人資人員
2. 提供正確的上下班時間
3. 由有權限的人員修正出勤記錄
4. 系統會重新計算加班時數

### Q8: 為什麼我的加班時數是0，明明有加班？
**A**: 可能的原因：
1. **午休時間扣除**：系統會自動扣除1小時午休時間
2. **標準工時設定**：檢查個人的標準工作時數設定
3. **工作日判斷**：確認該日是否被系統認定為工作日
4. **計算公式**：加班時數 = 實際工時 - 標準工時 - 午休時間

如仍有問題，請聯絡系統管理員檢查設定。

### Q9: 週末加班的費率是多少？
**A**: 根據台灣勞基法，加班費率如下：
- **平日前2小時**：1.34倍
- **平日2小時後**：1.67倍
- **週末加班**：2.0倍
- **國定假日**：2.0倍

系統會自動根據加班日期和時數計算正確的費率。

### Q10: 補班日如何處理？
**A**: 補班日處理方式：
1. 系統管理員會預先設定年度補班日
2. 補班日當天正常打卡上班
3. 系統會將補班日視為正常工作日
4. 超過標準工時的部分仍計算為加班

## 薪資計算相關問題

### Q11: 薪資單上的加班費如何計算？
**A**: 加班費計算公式：
```
時薪 = 月薪 ÷ 30天 ÷ 8小時
加班費 = 加班時數 × 時薪 × 加班倍率
```

**範例**：
- 月薪：36,000元
- 時薪：36,000 ÷ 30 ÷ 8 = 150元
- 平日加班2小時：2 × 150 × 1.34 = 402元

### Q12: 為什麼有兩份薪資單？
**A**: 雙薪資單機制說明：
- **標準版薪資單**：顯示限制內的加班時數，用於一般薪資發放
- **完整版薪資單**：顯示所有加班時數，用於內部管理和法規遵循

這個機制確保：
1. 符合台灣勞基法規範
2. 保護員工權益
3. 滿足公司管理需求

### Q13: 勞保、健保如何計算？
**A**: 法定扣除計算：

**勞保費**：
- 投保薪資上限：45,800元
- 費率：11.5%（2024年）
- 員工負擔：20%
- 計算：MIN(月薪, 45,800) × 11.5% × 20%

**健保費**：
- 投保薪資上限：182,000元
- 費率：5.17%（2024年）
- 員工負擔：30%
- 計算：MIN(月薪, 182,000) × 5.17% × 30%

### Q14: 薪資單確認後還能修改嗎？
**A**: 薪資單確認後的修改：
1. **已確認的薪資單**：一般情況下不能修改
2. **特殊情況**：需要系統管理員權限才能修改
3. **建議做法**：在確認前仔細檢查所有資訊
4. **修正流程**：如需修改，請聯絡薪資管理員

## 權限和安全相關問題

### Q15: 如何申請更高的系統權限？
**A**: 權限申請流程：
1. 向直屬主管說明需要權限的原因
2. 主管評估後向人資或IT部門申請
3. 系統管理員審核並設定權限
4. 通知申請人權限變更結果

**注意**：權限申請需要有合理的業務需求。

### Q16: 忘記密碼怎麼辦？
**A**: 密碼重設方式：
1. 在登入頁面點擊「忘記密碼」
2. 輸入註冊的電子郵件地址
3. 檢查郵件中的重設連結
4. 設定新密碼

如無法收到郵件，請聯絡系統管理員。

### Q17: 可以在家裡查看薪資資料嗎？
**A**: 遠端存取說明：
1. **一般情況**：可以透過網路存取系統
2. **安全考量**：建議使用安全的網路連線
3. **公司政策**：請遵守公司的資訊安全政策
4. **VPN需求**：某些公司可能要求使用VPN

具體政策請詢問IT部門。

## 系統操作相關問題

### Q18: 系統運行很慢，該怎麼辦？
**A**: 效能問題處理：
1. **檢查網路連線**：確保網路穩定
2. **清除瀏覽器快取**：定期清理瀏覽器資料
3. **關閉不必要的分頁**：減少瀏覽器負載
4. **聯絡IT支援**：如問題持續，請聯絡技術支援

### Q19: 如何匯出薪資資料？
**A**: 資料匯出方式：
1. 進入要匯出的資料列表頁面
2. 選擇要匯出的記錄
3. 點擊 **動作 > 匯出**
4. 選擇要匯出的欄位
5. 選擇匯出格式（Excel、CSV等）
6. 點擊 **匯出** 下載檔案

### Q20: 可以批次處理多筆記錄嗎？
**A**: 批次操作功能：
1. **批次審核**：選擇多筆加班記錄進行批次審核
2. **批次匯出**：選擇多筆記錄進行批次匯出
3. **批次修改**：某些欄位支援批次修改
4. **權限需求**：批次操作通常需要管理員權限

## 假日和行事曆相關問題

### Q21: 如何查看公司的假日安排？
**A**: 查看假日安排：
1. 進入 **台灣薪資 > 設定 > 工作行事曆**
2. 選擇公司的工作行事曆
3. 查看假日列表和補班日設定
4. 也可以在出勤記錄中看到工作日標記

### Q22: 新的國定假日如何更新？
**A**: 假日更新流程：
1. **管理員操作**：由假日管理員負責更新
2. **匯入方式**：使用假日匯入功能批次更新
3. **手動新增**：在工作行事曆中手動新增
4. **通知員工**：更新後會影響加班計算

一般員工無法自行更新假日設定。

### Q23: 颱風假如何處理？
**A**: 颱風假處理方式：
1. **臨時假日**：管理員可以臨時新增假日
2. **出勤記錄**：當日出勤會被標記為假日加班
3. **加班費率**：按假日加班費率計算（2.0倍）
4. **事後調整**：如有需要可以事後調整設定

## 錯誤訊息和故障排除

### Q24: 出現「權限不足」錯誤怎麼辦？
**A**: 權限錯誤處理：
1. **確認操作權限**：檢查是否有執行該操作的權限
2. **聯絡主管**：向直屬主管確認是否需要該權限
3. **申請權限**：如有業務需求，申請相應權限
4. **替代方案**：尋找其他有權限的同事協助

### Q25: 加班記錄無法提交，顯示「超過限制」？
**A**: 加班限制錯誤：
1. **檢查月累計時數**：查看本月已累計的加班時數
2. **了解限制設定**：確認適用的加班時數限制
3. **聯絡主管**：如有特殊情況，請聯絡主管
4. **調整申請**：考慮調整加班時數或分散到其他月份

### Q26: 薪資計算結果不正確？
**A**: 薪資計算問題排查：
1. **檢查基本資料**：確認員工基本薪資設定
2. **驗證加班記錄**：檢查加班記錄是否正確
3. **確認費率設定**：檢查加班費率設定
4. **聯絡薪資管理員**：如仍有問題，請聯絡薪資管理員

### Q27: 系統顯示「資料不一致」錯誤？
**A**: 資料一致性問題：
1. **同步資料**：使用系統的資料同步功能
2. **重新計算**：重新計算相關的計算欄位
3. **聯絡技術支援**：如問題持續，請聯絡技術支援
4. **避免並發操作**：避免多人同時修改同一筆資料

## 行動裝置和瀏覽器相關問題

### Q28: 可以用手機查看薪資資料嗎？
**A**: 行動裝置支援：
1. **響應式設計**：系統支援行動裝置瀏覽
2. **瀏覽器要求**：建議使用現代瀏覽器
3. **功能限制**：某些複雜操作建議使用電腦
4. **安全考量**：注意行動裝置的資訊安全

### Q29: 建議使用哪個瀏覽器？
**A**: 瀏覽器建議：
1. **推薦瀏覽器**：Chrome、Firefox、Safari、Edge
2. **版本要求**：使用最新版本瀏覽器
3. **避免使用**：過舊的IE瀏覽器
4. **設定建議**：啟用JavaScript和Cookie

### Q30: 如何聯絡技術支援？
**A**: 技術支援聯絡方式：
1. **內部IT部門**：優先聯絡公司IT部門
2. **系統管理員**：聯絡指定的系統管理員
3. **問題描述**：詳細描述問題和錯誤訊息
4. **螢幕截圖**：提供相關的螢幕截圖協助診斷

**聯絡時請提供**：
- 使用者帳號
- 操作步驟
- 錯誤訊息
- 瀏覽器版本
- 發生時間

---

## 快速參考

### 常用功能快速連結
- **查看薪資單**：薪資 > 我的薪資單
- **查看加班記錄**：台灣薪資 > 加班管理 > 加班記錄
- **申請加班**：台灣薪資 > 加班管理 > 加班記錄 > 新增
- **查看出勤記錄**：人力資源 > 出勤 > 出勤記錄

### 緊急聯絡資訊
- **系統管理員**：[請填入實際聯絡資訊]
- **人資部門**：[請填入實際聯絡資訊]
- **IT支援**：[請填入實際聯絡資訊]

### 相關文檔
- [使用者手冊](user_manual.md)
- [故障排除指南](troubleshooting.md)
- [系統更新說明](../README.md)

### Q31: 系統安裝時出現 External ID 錯誤怎麼辦？ ✨ **[已修復]**
**A**: 這個問題已經在最新版本中完全修復：
- ✅ **問題已解決**：`ValueError: External ID not found in the system: l10n_tw_hr_payroll.model_tw_overtime_record` 錯誤已修復
- ✅ **清理完成**：72 個無效模型引用已清理
- ✅ **架構優化**：移除了複雜的模型依賴關係
- ✅ **安裝測試**：模組現在可以正常安裝無錯誤

**如果仍遇到問題**：
1. 確保使用最新版本的模組
2. 重新安裝模組
3. 聯絡系統管理員

### Q32: tw.overtime.record 模型不存在的錯誤？ ✨ **[架構變更]**
**A**: 這是正常的架構變更：
- ✅ **模型移除**：`tw.overtime.record` 模型已完全移除
- ✅ **功能整合**：相關功能已整合到 `tw.hr.attendance` 模型
- ✅ **架構簡化**：新架構更穩定且易於維護
- ✅ **功能保留**：所有原有功能都已保留並優化

**新的操作方式**：
- 加班管理現在通過 `tw.hr.attendance` 模型處理
- 所有加班相關功能都在考勤管理中統一處理

### Q33: 模組升級後資料會丟失嗎？
**A**: 資料安全保障：
1. **資料保護**：升級過程會保護現有資料
2. **自動遷移**：系統會自動遷移相關資料
3. **備份建議**：升級前建議備份資料庫
4. **測試環境**：建議先在測試環境驗證

---

**版本資訊**: v1.1
**最後更新**: 2025-06-17
**適用版本**: Odoo 18.0+
**修復狀態**: ✅ External ID 錯誤已修復，模組可正常安裝

**重要更新**：
- ✅ 修復 External ID 錯誤
- ✅ 清理 72 個無效引用
- ✅ 架構優化完成
- ✅ 模組安裝測試通過

**注意**：本文檔會持續更新，如有新的問題或建議，請聯絡系統管理員。