# 台灣薪資管理系統 - 故障排除指南

## 概述

本指南提供台灣薪資管理系統常見問題的診斷方法和解決步驟，幫助使用者快速解決系統使用過程中遇到的問題。

## 問題分類

### 🔍 診斷流程
1. **確認問題類型** - 根據症狀分類問題
2. **收集錯誤資訊** - 記錄錯誤訊息和操作步驟
3. **嘗試基本解決方案** - 執行對應的故障排除步驟
4. **聯絡技術支援** - 如問題持續，提供詳細資訊給技術支援

## 1. 登入和權限問題

### 1.1 無法登入系統

#### 症狀
- 輸入帳號密碼後無法進入系統
- 顯示「帳號或密碼錯誤」訊息
- 登入頁面一直重新載入

#### 診斷步驟
1. **檢查帳號密碼**
   ```
   ✓ 確認帳號拼寫正確
   ✓ 檢查大小寫是否正確
   ✓ 確認沒有多餘的空格
   ✓ 嘗試重新輸入密碼
   ```

2. **檢查網路連線**
   ```
   ✓ 確認網路連線正常
   ✓ 嘗試存取其他網站
   ✓ 檢查公司網路是否正常
   ```

3. **檢查瀏覽器設定**
   ```
   ✓ 清除瀏覽器快取和Cookie
   ✓ 嘗試使用無痕模式
   ✓ 嘗試其他瀏覽器
   ```

#### 解決方案
1. **密碼重設**
   - 點擊「忘記密碼」連結
   - 輸入註冊的電子郵件
   - 檢查郵件中的重設連結

2. **聯絡管理員**
   - 如無法收到重設郵件
   - 帳號可能被停用
   - 需要管理員重新啟用帳號

### 1.2 權限不足錯誤

#### 症狀
- 顯示「您沒有權限執行此操作」
- 某些選單項目看不到
- 按鈕呈現灰色無法點擊

#### 診斷步驟
1. **確認使用者角色**
   ```
   ✓ 檢查個人資料中的使用者群組
   ✓ 確認是否有適當的權限群組
   ✓ 檢查權限是否過期
   ```

2. **檢查操作權限**
   ```
   ✓ 確認該功能需要的權限等級
   ✓ 檢查是否在正確的選單位置
   ✓ 確認資料範圍權限
   ```

#### 解決方案
1. **申請權限**
   - 向直屬主管說明需求
   - 填寫權限申請表單
   - 等待管理員審核

2. **尋求協助**
   - 請有權限的同事代為操作
   - 聯絡系統管理員確認權限設定

## 2. 出勤和打卡問題

### 2.1 加班時數計算錯誤

#### 症狀
- 加班時數顯示為0但實際有加班
- 加班時數與預期不符
- 出勤記錄正常但沒有產生加班記錄

#### 診斷步驟
1. **檢查出勤記錄**
   ```
   ✓ 確認上下班打卡時間正確
   ✓ 檢查是否有缺少打卡記錄
   ✓ 確認工作時數計算正確
   ```

2. **檢查工作日設定**
   ```
   ✓ 確認該日期是否為工作日
   ✓ 檢查是否為假日或補班日
   ✓ 確認工作行事曆設定
   ```

3. **檢查計算參數**
   ```
   ✓ 確認標準工作時數設定（通常8小時）
   ✓ 檢查午休時間設定（通常1小時）
   ✓ 確認個人工作時間設定
   ```

#### 解決方案
1. **手動重新計算**
   ```python
   # 加班時數計算公式
   加班時數 = MAX(0, 實際工作時數 - 標準工作時數 - 午休時間)
   
   # 範例：
   # 實際工作：10小時
   # 標準工時：8小時
   # 午休時間：1小時
   # 加班時數：10 - 8 - 1 = 1小時
   ```

2. **修正出勤記錄**
   - 聯絡有權限的管理員
   - 提供正確的上下班時間
   - 重新計算加班時數

3. **檢查系統設定**
   - 確認員工的工作行事曆設定
   - 檢查標準工作時數設定
   - 驗證午休時間設定

### 2.2 出勤記錄無法同步

#### 症狀
- 打卡記錄存在但沒有產生加班記錄
- 出勤時間修改後加班時數沒有更新
- 系統顯示「資料不一致」錯誤

#### 診斷步驟
1. **檢查同步狀態**
   ```
   ✓ 確認出勤記錄是否完整（有上班和下班時間）
   ✓ 檢查是否有關聯的加班記錄
   ✓ 確認同步處理狀態
   ```

2. **檢查定時任務**
   ```
   ✓ 確認定時任務是否正常執行
   ✓ 檢查最後執行時間
   ✓ 查看是否有錯誤日誌
   ```

#### 解決方案
1. **手動同步**
   - 在出勤記錄中點擊「創建加班記錄」
   - 使用系統的資料同步功能
   - 重新計算相關欄位

2. **聯絡技術支援**
   - 提供出勤記錄ID
   - 說明預期的加班時數
   - 請求手動處理同步

## 3. 加班管理問題

### 3.1 加班申請無法提交

#### 症狀
- 點擊「提交」按鈕沒有反應
- 顯示「超過加班時數限制」錯誤
- 系統顯示驗證錯誤訊息

#### 診斷步驟
1. **檢查加班限制**
   ```
   ✓ 查看本月累計加班時數
   ✓ 確認適用的加班限制設定
   ✓ 檢查是否超過月度限制
   ```

2. **檢查資料完整性**
   ```
   ✓ 確認所有必填欄位已填寫
   ✓ 檢查日期格式是否正確
   ✓ 確認加班時數為正數
   ```

#### 解決方案
1. **調整加班時數**
   - 減少申請的加班時數
   - 分散到其他月份申請
   - 聯絡主管討論特殊情況

2. **檢查限制設定**
   - 確認適用的加班限制規則
   - 聯絡管理員檢查設定是否合理
   - 申請臨時調整限制

### 3.2 加班審核異常

#### 症狀
- 主管無法看到待審核的加班申請
- 審核後狀態沒有更新
- 批次審核功能無法使用

#### 診斷步驟
1. **檢查審核權限**
   ```
   ✓ 確認審核者有適當權限
   ✓ 檢查是否為該員工的直屬主管
   ✓ 確認權限群組設定正確
   ```

2. **檢查記錄狀態**
   ```
   ✓ 確認加班記錄狀態為「已提交」
   ✓ 檢查是否有其他審核流程
   ✓ 確認沒有系統鎖定
   ```

#### 解決方案
1. **重新提交申請**
   - 將記錄改回草稿狀態
   - 重新提交審核
   - 通知審核者

2. **聯絡管理員**
   - 檢查審核流程設定
   - 確認權限配置
   - 手動處理審核

## 4. 薪資計算問題

### 4.1 薪資計算結果錯誤

#### 症狀
- 加班費金額與預期不符
- 法定扣除額計算錯誤
- 薪資單總額不正確

#### 診斷步驟
1. **檢查基礎資料**
   ```
   ✓ 確認員工基本薪資設定
   ✓ 檢查合約資料是否正確
   ✓ 確認生效日期範圍
   ```

2. **檢查加班資料**
   ```
   ✓ 確認加班記錄已審核通過
   ✓ 檢查加班時數是否正確
   ✓ 確認加班費率設定
   ```

3. **檢查計算公式**
   ```
   ✓ 驗證時薪計算：月薪 ÷ 30 ÷ 8
   ✓ 檢查加班費率：平日1.34/1.67倍，假日2.0倍
   ✓ 確認法定扣除費率
   ```

#### 解決方案
1. **重新計算薪資單**
   - 點擊「重新計算」按鈕
   - 更新加班記錄後重新計算
   - 檢查計算結果

2. **手動調整**
   - 聯絡薪資管理員
   - 提供正確的計算依據
   - 申請手動調整薪資項目

### 4.2 雙薪資單問題

#### 症狀
- 無法生成完整版薪資單
- 兩份薪資單資料不一致
- 配對薪資單連結失效

#### 診斷步驟
1. **檢查權限設定**
   ```
   ✓ 確認有雙薪資單生成權限
   ✓ 檢查完整版薪資單存取權限
   ✓ 確認薪資單狀態為草稿
   ```

2. **檢查資料完整性**
   ```
   ✓ 確認加班記錄資料完整
   ✓ 檢查顯示/隱藏時數計算
   ✓ 確認沒有重複的薪資單
   ```

#### 解決方案
1. **重新生成薪資單**
   - 刪除有問題的薪資單
   - 重新執行雙薪資單生成
   - 檢查配對關係

2. **修復配對關係**
   - 聯絡系統管理員
   - 手動修復薪資單配對
   - 重新建立關聯

## 5. 系統效能問題

### 5.1 系統運行緩慢

#### 症狀
- 頁面載入時間過長
- 操作回應延遲
- 批次處理執行緩慢

#### 診斷步驟
1. **檢查網路環境**
   ```
   ✓ 測試網路速度
   ✓ 檢查網路穩定性
   ✓ 確認沒有網路阻塞
   ```

2. **檢查瀏覽器狀態**
   ```
   ✓ 清除瀏覽器快取
   ✓ 關閉不必要的分頁
   ✓ 檢查瀏覽器記憶體使用
   ```

3. **檢查系統負載**
   ```
   ✓ 確認同時使用人數
   ✓ 檢查是否有大量批次處理
   ✓ 確認系統資源使用狀況
   ```

#### 解決方案
1. **優化瀏覽器**
   - 使用最新版本瀏覽器
   - 定期清理瀏覽器資料
   - 關閉不必要的擴充功能

2. **調整使用習慣**
   - 避免在系統繁忙時段操作
   - 分批處理大量資料
   - 使用篩選器減少資料載入量

3. **聯絡技術支援**
   - 報告效能問題
   - 提供具體的操作場景
   - 協助系統優化

### 5.2 批次處理失敗

#### 症狀
- 批次操作中途停止
- 部分記錄處理失敗
- 顯示逾時錯誤

#### 診斷步驟
1. **檢查處理數量**
   ```
   ✓ 確認批次處理的記錄數量
   ✓ 檢查是否超過系統限制
   ✓ 確認資料複雜度
   ```

2. **檢查錯誤日誌**
   ```
   ✓ 查看具體的錯誤訊息
   ✓ 確認失敗的記錄
   ✓ 檢查是否有資料衝突
   ```

#### 解決方案
1. **分批處理**
   - 減少每次處理的記錄數量
   - 分多次執行批次操作
   - 監控處理進度

2. **修復資料問題**
   - 修正有問題的記錄
   - 重新執行批次處理
   - 驗證處理結果

## 6. 資料一致性問題

### 6.1 資料同步異常

#### 症狀
- 出勤記錄與加班記錄不一致
- 加班記錄與薪資單不一致
- 顯示「資料不一致」警告

#### 診斷步驟
1. **檢查資料關聯**
   ```
   ✓ 確認記錄之間的關聯關係
   ✓ 檢查外鍵參照完整性
   ✓ 確認沒有孤立記錄
   ```

2. **檢查時間戳記**
   ```
   ✓ 比較記錄的建立和修改時間
   ✓ 確認同步處理時間
   ✓ 檢查是否有並發修改
   ```

#### 解決方案
1. **執行資料同步**
   - 使用系統的同步功能
   - 重新計算相關欄位
   - 驗證同步結果

2. **手動修復**
   - 聯絡系統管理員
   - 提供不一致的記錄ID
   - 申請手動修復資料

### 6.2 計算欄位錯誤

#### 症狀
- 統計數字不正確
- 計算欄位顯示空白
- 總計與明細不符

#### 診斷步驟
1. **檢查計算依賴**
   ```
   ✓ 確認計算欄位的依賴資料
   ✓ 檢查是否有循環依賴
   ✓ 確認計算公式正確
   ```

2. **檢查快取狀態**
   ```
   ✓ 確認計算結果是否已快取
   ✓ 檢查快取是否過期
   ✓ 確認沒有快取衝突
   ```

#### 解決方案
1. **重新計算**
   - 觸發計算欄位重新計算
   - 清除相關快取
   - 驗證計算結果

2. **修復計算邏輯**
   - 聯絡技術支援
   - 提供錯誤的計算案例
   - 協助修復計算邏輯

## 7. 匯入匯出問題

### 7.1 假日匯入失敗

#### 症狀
- 匯入檔案格式錯誤
- 部分假日匯入失敗
- 補班日設定不正確

#### 診斷步驟
1. **檢查檔案格式**
   ```
   ✓ 確認檔案為CSV或Excel格式
   ✓ 檢查欄位名稱是否正確
   ✓ 確認日期格式統一
   ```

2. **檢查資料內容**
   ```
   ✓ 確認日期資料有效
   ✓ 檢查是否有重複記錄
   ✓ 確認補班日資料格式
   ```

#### 解決方案
1. **修正匯入檔案**
   ```csv
   # 正確的CSV格式範例
   date,name,makeup_date
   2024-01-01,元旦,
   2024-02-10,農曆新年,2024-02-17
   ```

2. **分批匯入**
   - 將大檔案分割成小檔案
   - 逐批匯入並檢查結果
   - 處理匯入錯誤

### 7.2 資料匯出問題

#### 症狀
- 匯出檔案不完整
- 匯出格式錯誤
- 無法下載匯出檔案

#### 診斷步驟
1. **檢查匯出權限**
   ```
   ✓ 確認有資料匯出權限
   ✓ 檢查欄位存取權限
   ✓ 確認沒有資料限制
   ```

2. **檢查匯出設定**
   ```
   ✓ 確認選擇的匯出欄位
   ✓ 檢查匯出格式設定
   ✓ 確認資料篩選條件
   ```

#### 解決方案
1. **調整匯出設定**
   - 重新選擇匯出欄位
   - 調整資料篩選條件
   - 嘗試不同的匯出格式

2. **分批匯出**
   - 減少匯出的資料量
   - 分時段匯出資料
   - 合併匯出結果

## 8. 緊急處理程序

### 8.1 系統無法存取

#### 立即行動
1. **確認問題範圍**
   - 詢問其他使用者是否有相同問題
   - 檢查是否為個人網路問題
   - 確認系統是否完全無法存取

2. **聯絡技術支援**
   - 立即通知IT部門或系統管理員
   - 提供問題發生時間和症狀
   - 說明影響範圍和緊急程度

#### 臨時解決方案
- 使用備用網路連線
- 嘗試行動裝置存取
- 聯絡有權限的同事協助處理緊急事務

### 8.2 資料遺失或損毀

#### 立即行動
1. **停止相關操作**
   - 立即停止可能影響資料的操作
   - 不要嘗試自行修復
   - 保留錯誤畫面截圖

2. **聯絡管理員**
   - 立即通知系統管理員
   - 詳細說明操作步驟和發生時間
   - 提供相關的錯誤訊息

#### 資料恢復
- 等待技術人員評估損害程度
- 配合提供必要的資訊
- 協助驗證恢復後的資料正確性

## 9. 預防措施

### 9.1 定期維護

#### 使用者端維護
```
□ 定期清理瀏覽器快取和Cookie
□ 保持瀏覽器版本更新
□ 定期檢查網路連線品質
□ 備份重要的個人設定
```

#### 資料維護
```
□ 定期檢查個人資料正確性
□ 及時提交和審核加班申請
□ 定期查看薪資單內容
□ 報告發現的資料異常
```

### 9.2 最佳實踐

#### 操作建議
1. **避免並發操作**
   - 不要同時在多個分頁修改同一筆資料
   - 避免多人同時處理相同記錄
   - 完成一個操作後再進行下一個

2. **資料備份**
   - 重要操作前先匯出相關資料
   - 保留操作前的資料快照
   - 定期下載個人薪資資料

3. **及時處理**
   - 發現問題立即報告
   - 不要延遲處理異常狀況
   - 配合系統維護時間安排

## 10. 聯絡支援

### 10.1 問題回報格式

#### 必要資訊
```
問題類型：[登入/權限/計算/效能/其他]
發生時間：[具體時間]
使用者帳號：[您的帳號]
瀏覽器版本：[瀏覽器和版本]
操作步驟：[詳細的操作步驟]
錯誤訊息：[完整的錯誤訊息]
影響範圍：[個人/部門/全公司]
緊急程度：[高/中/低]
```

#### 附加資訊
- 螢幕截圖
- 相關記錄ID
- 預期結果說明
- 已嘗試的解決方法

### 10.2 支援聯絡方式

#### 內部支援
- **系統管理員**：[請填入實際聯絡資訊]
- **IT支援熱線**：[請填入實際聯絡資訊]
- **人資部門**：[請填入實際聯絡資訊]

#### 支援時間
- **一般問題**：工作日 09:00-18:00
- **緊急問題**：24小時支援
- **系統維護**：每週日 02:00-06:00

### 10.3 問題追蹤

#### 問題狀態
- **已接收**：問題已記錄，等待處理
- **處理中**：技術人員正在處理
- **等待回饋**：需要使用者提供更多資訊
- **已解決**：問題已修復，等待確認
- **已關閉**：問題處理完成

#### 追蹤方式
- 記錄問題單號
- 定期詢問處理進度
- 配合提供必要資訊
- 確認問題解決後關閉

---

## 快速參考

### 常見錯誤代碼
- **AUTH_001**：帳號或密碼錯誤
- **PERM_002**：權限不足
- **CALC_003**：計算錯誤
- **SYNC_004**：資料同步失敗
- **LIMIT_005**：超過限制

### 緊急聯絡清單
- **系統管理員**：[聯絡資訊]
- **技術支援**：[聯絡資訊]
- **人資主管**：[聯絡資訊]

### 相關文檔
- [使用者手冊](user_manual.md)
- [常見問題](faq.md)
- [系統更新說明](../README.md)

---

**版本資訊**: v1.0  
**最後更新**: 2025-06-12  
**適用版本**: Odoo 18.0+

**重要提醒**：遇到問題時請保持冷靜，按照本指南的步驟進行診斷和處理。如果問題無法解決，請及時聯絡技術支援，避免問題擴大。