# l10n_tw_hr_payroll 技術文檔

## 概述
本目錄包含 l10n_tw_hr_payroll (台灣薪資管理) 模組的完整技術文檔，涵蓋系統架構、API 參考、業務流程、安全權限等各個面向。

## 文檔結構

### 📁 技術文檔 (technical/)
核心技術文檔，面向開發人員和系統管理員。

#### 🔧 核心文檔
- **[API 參考文檔](technical/api_reference.md)** - 所有模型的方法、屬性和使用範例
- **[模型定義文檔](technical/models_definition.md)** - 詳細的模型結構和關聯關係
- **[功能流程文檔](technical/function_flows.md)** - 業務流程圖和邏輯說明
- **[算法文檔](technical/algorithms.md)** - 薪資計算、加班計算等核心算法

#### 🎨 介面文檔
- **[視圖文檔](technical/views_documentation.md)** - 所有視圖的說明和配置
- **[嚮導文檔](technical/wizards_documentation.md)** - 所有嚮導的功能和使用方式

#### 🔒 安全文檔
- **[權限文檔](technical/security_permissions.md)** - 完整的權限架構和安全控制
- **[資料結構文檔](technical/data_structures.md)** - 資料結構和初始資料說明

### 📁 使用者文檔 (user/)
面向最終使用者的操作指南，提供完整的系統使用說明。

#### 📖 操作指南
- **[使用者手冊](user/user_manual.md)** - 完整的系統使用指南
  - 系統概覽和功能介紹
  - 權限角色說明和操作範圍
  - 各功能模組詳細使用說明
  - 常用操作流程和最佳實踐
  - 系統整合和維護說明

#### ❓ 支援文檔
- **[常見問題](user/faq.md)** - 30+ 個常見問題解答
  - 系統使用相關問題
  - 出勤和打卡問題處理
  - 薪資計算和雙薪資單說明
  - 權限和安全相關問題
  - 系統操作和效能問題

- **[故障排除](user/troubleshooting.md)** - 系統問題診斷和解決方案
  - 登入和權限問題處理
  - 出勤和加班計算異常
  - 薪資計算錯誤診斷
  - 系統效能和資料一致性問題
  - 緊急處理程序和預防措施

### 📁 開發者文檔 (developer/)
面向模組開發和維護人員的完整開發指南。

#### 🛠️ 開發指南
- **[開發指南](developer/development_guide.md)** - 完整的開發環境設定和編碼規範
  - 開發環境安裝和配置 (Docker/本地安裝)
  - IDE 設定和開發工具
  - 程式碼結構說明和組織原則
  - 開發規範和最佳實踐
  - 新功能開發流程
  - Git 工作流程和分支策略

#### 🧪 測試指南
- **[測試指南](developer/testing_guide.md)** - 全面的測試策略和實作指南
  - 測試環境設定和工具安裝
  - 單元測試撰寫和最佳實踐
  - 整合測試和端到端測試
  - 效能測試和記憶體使用測試
  - 安全測試和權限驗證
  - 自動化測試設定和 CI/CD 整合

#### 🚀 部署指南
- **[部署指南](developer/deployment_guide.md)** - 生產環境部署和維護指南
  - 系統需求和環境準備
  - 詳細的安裝和配置步驟
  - 資料庫設定和效能調整
  - Nginx 反向代理和 SSL 設定
  - 高可用性和負載平衡部署
  - 容器化部署 (Docker/Kubernetes)
  - 監控、日誌管理和備份策略
  - 災難恢復和故障排除

#### 🤝 貢獻指南
- **[貢獻指南](developer/contribution_guide.md)** - 參與專案開發的完整指南
  - 如何參與專案開發
  - 問題回報和功能請求流程
  - 程式碼貢獻規範和審查標準
  - Pull Request 流程和最佳實踐
  - 文檔貢獻和翻譯指南
  - 社群參與方式和行為準則
  - 發布流程和版本管理

## 快速導航

### 👥 使用者入門
1. 閱讀 [使用者手冊](user/user_manual.md) 了解系統功能和操作方式
2. 查看 [常見問題](user/faq.md) 解決使用過程中的疑問
3. 參考 [故障排除](user/troubleshooting.md) 處理系統問題

### � 開發者入門
1. 閱讀 [模型定義文檔](technical/models_definition.md) 了解系統架構
2. 查看 [功能流程文檔](technical/function_flows.md) 理解業務邏輯
3. 參考 [API 參考文檔](technical/api_reference.md) 開始開發

### 🔍 常用查詢
- **使用者操作**: [使用者手冊](user/user_manual.md)
- **問題解答**: [常見問題](user/faq.md) | [故障排除](user/troubleshooting.md)
- **模型方法查詢**: [API 參考文檔](technical/api_reference.md)
- **權限設定**: [權限文檔](technical/security_permissions.md)
- **視圖配置**: [視圖文檔](technical/views_documentation.md)
- **算法邏輯**: [算法文檔](technical/algorithms.md)

### 🛠️ 開發相關
- **開發環境**: [開發指南](developer/development_guide.md)
- **測試執行**: [測試指南](developer/testing_guide.md)
- **部署流程**: [部署指南](developer/deployment_guide.md)

## 模組功能概覽

### 核心功能
- ✅ **台灣考勤管理**: tw.hr.attendance 專屬模型，完整的考勤記錄管理
- ✅ **智慧同步機制**: 與原生 hr.attendance 的雙向同步，確保資料一致性
- ✅ **隱藏加班功能**: 根據權限控制加班時數的可見性
- ✅ **出勤管理**: 自動計算加班時數，支援台灣勞基法規範
- ✅ **加班管理**: 完整的加班申請、審核和統計功能
- ✅ **薪資計算**: 雙薪資單機制，支援顯示/隱藏時數控制，修復計算錯誤
- ✅ **假日管理**: 支援假日匯入和補班日設定
- ✅ **權限控制**: 多層級權限管理，保護敏感薪資資訊
- ✅ **薪資單整合**: 考勤資料與薪資計算完整整合，修復 compute_sheet 功能

### 技術特色
- 🔧 **Odoo 18 相容**: 完全支援最新版本 Odoo
- 🏗️ **模組化設計**: 清晰的架構，易於維護和擴展
- 🔒 **安全第一**: 完整的權限控制和資料保護
- ⚡ **效能優化**: 批次處理和快取機制
- 🌐 **國際化**: 支援繁體中文和多語言
- 🔄 **架構優化**: 移除複雜依賴，簡化模型關係

## 系統需求

### 基礎需求
- **Odoo**: 18.0+
- **Python**: 3.8+
- **PostgreSQL**: 12+

### 相依模組
- `hr_payroll`: 薪資管理基礎模組
- `hr_holidays`: 假期管理模組
- `hr_attendance`: 出勤管理模組
- `resource`: 資源管理模組
- `base_import`: 資料匯入模組

## 安裝和設定

### 快速安裝
```bash
# 1. 複製模組到 addons 目錄
cp -r l10n_tw_hr_payroll /path/to/odoo/addons/

# 2. 重啟 Odoo 服務
sudo systemctl restart odoo

# 3. 在 Odoo 中安裝模組
# 應用程式 > 更新應用程式清單 > 搜尋 "Taiwan - Payroll" > 安裝
```

### 詳細設定
請參考 [部署指南](developer/deployment_guide.md) 獲取完整的安裝和配置說明。

## 版本資訊

### 當前版本: 1.0
- 初始版本發布
- 支援基本的台灣薪資管理功能
- 完整的出勤和加班管理
- 雙薪資單機制
- 多層級權限控制
- ✅ **台灣考勤管理系統** (tw.hr.attendance)
- ✅ **智慧同步機制** (sync_management)
- ✅ **隱藏加班功能** (權限控制)
- ✅ **架構優化** (移除循環依賴)
- ✅ **External ID 錯誤修復** (完全修復)
- ✅ **大規模代碼清理** (72個無效引用清理)

### 最新更新 (2025-06-17)
- ✅ 完成 tw.hr.attendance 核心模型
- ✅ 實現完整的權限控制系統
- ✅ 修復薪資單計算錯誤
- ✅ 完善加班費計算邏輯
- ✅ 修復 check_in_date 欄位相關問題
- ✅ 整合考勤資料與薪資計算
- ✅ 修復標準版與完整版薪資單差異計算
- ✅ 模組可正常安裝和運行
- ✅ 更新專案文檔反映最新狀態

### 開發路線圖
- **v1.1**: 增強報表功能
- **v1.2**: 支援更多台灣法規
- **v1.3**: 行動端支援
- **v2.0**: 完整的人力資源整合

## 支援和貢獻

### 取得支援
- 📖 **文檔**: 查閱本技術文檔
- 🐛 **問題回報**: 透過 GitHub Issues
- 💬 **討論**: 參與社群討論

### 貢獻程式碼
歡迎貢獻程式碼！請參考 [貢獻指南](developer/contribution_guide.md) 了解如何參與開發。

### 開發團隊
- **主要開發者**: <EMAIL>
- **技術架構**: 基於 Odoo 18 框架
- **授權**: OEEL-1

## 文檔維護

### 文檔更新原則
1. **同步更新**: 程式碼變更時同步更新文檔
2. **版本控制**: 文檔版本與模組版本保持一致
3. **品質保證**: 定期檢查文檔的準確性和完整性

### 文檔貢獻
- 發現文檔錯誤或不完整之處，歡迎提交修正
- 建議新增文檔內容或改善現有結構
- 協助翻譯文檔到其他語言

## 授權資訊

本模組採用 OEEL-1 授權，詳細授權條款請參考 Odoo 官方授權文件。

---

**最後更新**: 2025-06-17
**文檔版本**: 1.2
**適用模組版本**: l10n_tw_hr_payroll v1.0
**修復狀態**: ✅ 薪資單計算錯誤已修復，考勤整合完成
**部署狀態**: ✅ 模組安裝測試通過，準備最終測試