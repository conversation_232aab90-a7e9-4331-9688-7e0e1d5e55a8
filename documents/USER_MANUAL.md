# l10n_tw_hr_payroll 使用說明書

## 系統概述

### 系統介紹
l10n_tw_hr_payroll 是專為台灣企業設計的 Odoo 18 薪酬管理本地化套件，提供符合台灣勞基法的完整薪資管理解決方案。

### 核心特色
- **雙薪資單系統**: 根據權限顯示不同版本的薪資單
- **智慧加班管理**: 動態休息時間計算，準確的加班時數計算
- **台灣假日整合**: 完整的台灣假日和補班日管理
- **友善操作介面**: 提供多個精靈程式簡化複雜操作

### 適用對象
- 台灣企業 HR 人員
- 薪資管理人員
- 系統管理員
- 一般員工 (查看個人薪資)

## 安裝和設定指南

### 系統需求
- **Odoo 版本**: 18.0 或以上
- **資料庫**: PostgreSQL 12+
- **Python 套件**: pandas
- **作業系統**: Linux (推薦 Ubuntu 20.04+)

### 安裝步驟

#### 1. 模組安裝
```bash
# 1. 將模組放置到 addons 目錄
cp -r l10n_tw_hr_payroll /path/to/odoo/addons/

# 2. 重啟 Odoo 服務
sudo systemctl restart odoo

# 3. 在 Odoo 介面中安裝模組
# 應用程式 → 搜尋 "Taiwan - Payroll" → 安裝
```

#### 2. 基本設定
1. **建立使用者群組**
   - 進入 `設定 → 使用者與公司 → 群組`
   - 確認台灣薪酬管理相關群組已建立

2. **設定工作行事曆**
   - 進入 `員工 → 設定 → 工作時間`
   - 建立或修改台灣標準工作時間

3. **匯入基礎資料**
   - 系統會自動載入台灣假日資料
   - 可透過假日匯入功能補充最新資料

### 權限設定

#### 使用者群組說明
| 群組名稱 | 權限範圍 | 適用人員 |
|----------|----------|----------|
| 薪酬用戶 | 查看標準薪資單 | 一般員工 |
| 薪酬管理員 | 管理薪資結構和設定 | HR 人員 |
| 加班時數完整查看 | 查看完整加班時數 | 主管、HR |
| 假日管理員 | 管理假日和補班日 | HR 人員 |
| 工作時間管理員 | 管理工作時間設定 | HR 人員 |
| 超級薪酬管理員 | 最高權限 | 系統管理員 |

#### 權限分配建議
```
一般員工: 薪酬用戶
HR 人員: 薪酬管理員 + 假日管理員 + 工作時間管理員
部門主管: 薪酬用戶 + 加班時數完整查看
系統管理員: 超級薪酬管理員
```

## 功能模組使用指南

### 1. 工作行事曆管理

#### 1.1 基本工作時間設定
**路徑**: `員工 → 設定 → 工作時間`

**操作步驟**:
1. 點擊「建立」新增工作時間
2. 填寫基本資訊:
   - 名稱: 例如「台灣標準工時」
   - 公司: 選擇對應公司
   - 時區: Asia/Taipei
3. 設定每日工作時間:
   - 週一至週五: 09:00-12:00, 13:00-18:00
   - 週六、週日: 不工作
4. 儲存設定

#### 1.2 工作日覆蓋設定
**用途**: 處理補班日或特殊工作安排

**操作步驟**:
1. 進入 `薪資 → 設定 → 工作行事曆覆蓋`
2. 點擊「建立」
3. 填寫資訊:
   - 工作行事曆: 選擇對應行事曆
   - 日期: 選擇要覆蓋的日期
   - 是否為工作日: 勾選或取消
   - 原因: 說明覆蓋原因
   - 工作時數: 設定該日工作時數
4. 點擊「確認」生效

#### 1.3 補班日批量設定
**使用精靈**: 補班日設定精靈

**操作步驟**:
1. 進入 `薪資 → 精靈 → 補班日設定`
2. 選擇工作行事曆和年度
3. 添加補班日期:
   - 點擊「新增行」
   - 選擇補班日期
   - 填寫補班原因
   - 設定工作時數 (預設 8 小時)
4. 點擊「創建補班日」

### 2. 假日管理

#### 2.1 假日資料匯入
**使用精靈**: 假日匯入精靈

**支援格式**: CSV, Excel (.xlsx, .xls)

**檔案格式要求**:
```csv
date,name,makeup_date
2024-01-01,元旦,
2024-02-08,農曆除夕,
2024-02-09,農曆新年,
2024-02-10,農曆新年,2024-02-17
```

**操作步驟**:
1. 準備假日資料檔案
2. 進入 `薪資 → 精靈 → 假日匯入`
3. 填寫匯入設定:
   - 匯入名稱: 例如「2024年台灣假日」
   - 工作行事曆: 選擇對應行事曆
   - 年度: 選擇年份
   - 上傳檔案: 選擇準備好的檔案
4. 設定欄位對應:
   - 日期欄位名稱: date
   - 假日名稱欄位: name
   - 補班日欄位名稱: makeup_date (可選)
5. 點擊「預覽資料」檢查
6. 確認無誤後點擊「匯入假日」

#### 2.2 假日資料管理
**路徑**: `薪資 → 假日管理 → 假日匯入記錄`

**功能說明**:
- 查看匯入歷史記錄
- 檢查匯入結果和錯誤日誌
- 管理假日資料的覆蓋設定

### 3. 加班管理

#### 3.1 多段式打卡加班時數計算

##### 多段式打卡支援
系統全面支援多段式打卡制度，適用於以下工作模式：
- **分段工作制**: 上午班、下午班、加班時段分別打卡
- **彈性工時**: 員工可在一天內多次進出辦公室
- **輪班制度**: 支援複雜的輪班打卡模式

##### 多段式打卡計算邏輯
```
當天總工作時數 = 時段1工作時數 + 時段2工作時數 + ... + 時段N工作時數
加班時數 = MAX(0, 當天總工作時數 - 標準工作時數)

顯示規則：
- 只在當天最後一筆記錄顯示加班時數
- 其他記錄的加班時數顯示為 0
- 所有記錄關聯到同一個加班記錄
```

##### 多段式打卡範例
```
場景：員工一天內多次打卡
08:00-12:00  上午工作 (4小時)
13:00-17:00  下午工作 (4小時)
18:00-21:00  加班工作 (3小時)

計算過程：
1. 當天總工作時數 = 4 + 4 + 3 = 11小時
2. 標準工作時數 = 9 - 1 = 8小時 (假設9小時預期工作時數，1小時休息)
3. 加班時數 = 11 - 8 = 3小時
4. 顯示：只在21:00的記錄顯示3小時加班，其他記錄顯示0小時
5. 薪資計算：系統會正確從加班記錄中取得3小時進行薪資計算
```

##### 動態休息時間計算
系統採用先進的動態休息時間計算機制：
- **智慧休息時間計算**: 系統會自動從工作行事曆計算所有休息時間
- **支援多休息時段**: 可處理午休、晚班休息等多個時段
- **準確加班計算**: 避免重複扣除休息時間的錯誤
- **多段式相容**: 正確處理跨多個時段的休息時間

##### 傳統單次打卡範例
```
員工打卡時間：08:00-21:30 (13.5小時)
工作行事曆設定：
- 上午：08:00-12:00 (4小時)
- 午休：12:00-13:00 (1小時休息)
- 下午：13:00-17:00 (4小時)
- 晚班休息：17:00-17:30 (0.5小時休息)
- 晚班：17:30-21:30 (4小時)

計算結果：
- 總休息時數：1 + 0.5 = 1.5小時
- 實際工作時數：13.5 - 1.5 = 12小時
- 標準工作時數：8小時
- 加班時數：12 - 8 = 4小時
```

#### 3.2 加班時數限制設定
**使用精靈**: 加班限制設定精靈

**操作步驟**:
1. 進入 `薪資 → 精靈 → 加班限制設定`
2. 填寫限制設定:
   - 設定名稱: 例如「一般員工加班限制」
   - 每月加班時數上限: 46 (台灣勞基法規定)
   - 顯示時數上限: 30 (超過部分將隱藏)
3. 選擇適用範圍:
   - 全公司: 套用至所有員工
   - 指定部門: 選擇特定部門
   - 指定員工: 選擇特定員工
4. 設定生效時間:
   - 開始日期: 設定生效日期
   - 結束日期: 可選，設定失效日期
5. 點擊「創建限制設定」

#### 3.3 加班記錄管理
**路徑**: `薪資 → 加班管理 → 加班記錄`

**新增加班記錄**:
1. 點擊「建立」
2. 填寫加班資訊:
   - 員工: 選擇加班員工
   - 加班日期: 選擇日期
   - 實際加班時數: 輸入時數
   - 加班類型: 選擇類型 (平日/假日/國定假日/補班日)
3. 儲存後狀態為「草稿」

**加班審核流程**:
1. 員工提交: 點擊「提交」
2. 主管審核:
   - 核准: 點擊「核准」
   - 拒絕: 點擊「拒絕」並填寫原因
3. 核准後自動計算顯示/隱藏時數

#### 3.4 加班統計查看
**路徑**: `薪資 → 報表 → 加班統計`

**功能說明**:
- 查看員工月度加班統計
- 分析加班時數分布
- 監控加班限制執行情況

#### 3.5 多段式打卡操作指南

##### 出勤記錄管理
**路徑**: `員工 → 出勤 → 出勤記錄`

**多段式打卡操作**:
1. **上午上班打卡**:
   - 員工: 選擇員工
   - 打卡時間: 08:00 (上班)
   - 狀態: 系統自動設為「上班」

2. **上午下班打卡** (午休前):
   - 同一員工
   - 打卡時間: 12:00 (下班)
   - 狀態: 系統自動設為「下班」
   - 加班時數: 顯示 0 (非最後一筆)

3. **下午上班打卡**:
   - 同一員工
   - 打卡時間: 13:00 (上班)
   - 狀態: 系統自動設為「上班」

4. **下午下班打卡**:
   - 同一員工
   - 打卡時間: 17:00 (下班)
   - 狀態: 系統自動設為「下班」
   - 加班時數: 顯示 0 (非最後一筆)

5. **加班上班打卡**:
   - 同一員工
   - 打卡時間: 18:00 (上班)
   - 狀態: 系統自動設為「上班」

6. **加班下班打卡**:
   - 同一員工
   - 打卡時間: 21:00 (下班)
   - 狀態: 系統自動設為「下班」
   - 加班時數: 顯示 3 (當天最後一筆，顯示總加班時數)

##### 系統自動處理
**自動計算功能**:
- 系統會自動識別同一天同一員工的所有打卡記錄
- 計算當天總工作時數 = 所有時段工作時數總和
- 只在最後一筆記錄顯示加班時數
- 自動創建加班記錄並關聯所有出勤記錄

**薪資整合**:
- 薪資計算時會正確從加班記錄取得總加班時數
- 支援標準版和完整版薪資單的不同顯示邏輯
- 遵守 display_limit 設定的時數限制

##### 注意事項
1. **打卡順序**: 必須按時間順序打卡，系統會驗證時間邏輯
2. **同日限制**: 同一天的打卡記錄會自動關聯
3. **權限控制**: 不同權限用戶看到的加班時數可能不同
4. **自動審核**: 符合條件的加班記錄會自動提交審核

### 4. 雙薪資單管理

#### 4.1 雙薪資單概念
- **標準版薪資單**: 顯示部分加班時數，供一般員工查看
- **完整版薪資單**: 顯示完整加班時數，供管理層查看
- **自動配對**: 系統自動建立兩版本間的關聯
- **多段式支援**: 正確處理多段式打卡的加班時數計算

#### 4.2 批量生成雙薪資單
**使用精靈**: 雙薪資單生成精靈

**操作步驟**:
1. 進入 `薪資 → 精靈 → 雙薪資單生成`
2. 設定期間:
   - 開始日期: 薪資期間開始
   - 結束日期: 薪資期間結束
3. 選擇員工範圍:
   - 所有員工: 為所有有效員工生成
   - 指定部門: 選擇特定部門
   - 指定員工: 選擇特定員工
4. 生成選項:
   - 自動確認薪資單: 可選
   - 包含草稿狀態的加班記錄: 可選
5. 點擊「生成薪資單」

#### 4.3 薪資單查看和管理
**路徑**: `薪資 → 薪資單`

**查看權限**:
- 一般員工: 只能看到標準版薪資單
- 有完整查看權限的用戶: 可看到完整版薪資單
- 管理員: 可查看所有版本

**操作功能**:
- 查看配對薪資單: 點擊「查看配對薪資單」
- 薪資單計算: 點擊「計算薪資單」
- 確認薪資單: 點擊「確認」

### 5. 員工資料管理

#### 5.1 台灣特有欄位設定
**路徑**: `員工 → 員工`

**台灣特有欄位**:
- 員工編號: 公司內部編號
- 身分證字號: 台灣身分證 (格式驗證)
- 勞保證號: 勞工保險證號
- 健保證號: 全民健康保險證號

**薪資相關欄位**:
- 月薪: 基本月薪
- 時薪: 自動計算 (月薪/30/8)
- 加班費率: 平日加班費率設定
- 延長加班費率: 超過2小時的加班費率
- 假日加班費率: 假日加班費率

#### 5.2 員工工作時間設定
**個人化設定**:
- 個人工作行事曆: 可設定個別的工作時間
- 標準工作時數: 每日標準工作時數
- 每週工作時數: 每週標準工作時數

#### 5.3 員工統計資訊查看
**統計功能**:
- 本月加班時數: 當月總加班時數
- 本月顯示加班時數: 當月顯示的加班時數
- 本月隱藏加班時數: 當月隱藏的加班時數
- 年假餘額: 剩餘年假天數

### 6. 報表功能

#### 6.1 薪資單報表
**路徑**: `薪資 → 報表 → 薪資單報表`

**報表內容**:
- 員工基本資訊
- 基本薪資和各項津貼
- 加班費計算 (標準版/完整版)
- 薪資總額比較
- 加班時數統計

#### 6.2 加班分析報表
**功能說明**:
- 部門加班統計
- 員工加班排行
- 加班隱藏比例分析
- 月度趨勢分析

## 常見問題解答

### Q1: 為什麼需要雙薪資單系統？
**A**: 台灣勞基法規定每月加班不得超過46小時，但實際上可能有超時情況。雙薪資單系統可以：
- 對一般員工顯示合規的加班時數
- 對管理層顯示實際的加班時數
- 確保薪資計算的準確性和法規合規性

### Q2: 如何設定加班時數的隱藏規則？
**A**: 透過加班限制設定：
1. 設定月度加班上限 (通常為46小時)
2. 設定顯示時數上限 (例如30小時)
3. 超過顯示上限的部分會自動隱藏
4. 可針對不同部門或員工設定不同規則

### Q3: 假日匯入失敗怎麼辦？
**A**: 檢查以下項目：
1. 檔案格式是否正確 (CSV或Excel)
2. 必要欄位是否存在 (date, name)
3. 日期格式是否正確 (YYYY-MM-DD)
4. 檔案編碼是否為UTF-8
5. 查看錯誤日誌了解具體問題

### Q4: 如何處理補班日？
**A**: 有兩種方式：
1. **假日匯入時自動處理**: 在匯入檔案中包含補班日欄位
2. **手動設定**: 使用補班日設定精靈或工作日覆蓋功能

### Q5: 員工看不到薪資單怎麼辦？
**A**: 檢查權限設定：
1. 確認員工有「薪酬用戶」權限
2. 檢查薪資單的狀態是否為「確認」
3. 確認員工查看的是正確的薪資期間
4. 檢查部門隔離設定是否正確

### Q6: 多段式打卡的加班時數計算不正確？
**A**: 檢查以下項目：
1. **確認所有打卡記錄完整**: 每個時段都要有上班和下班打卡
2. **檢查打卡時間順序**: 時間必須按邏輯順序排列
3. **驗證工作行事曆設定**: 確認標準工作時數和休息時間設定正確
4. **查看加班記錄關聯**: 確認所有當天記錄都關聯到同一個加班記錄
5. **重新計算薪資單**: 如果薪資計算有問題，嘗試重新計算

### Q7: 為什麼只有最後一筆打卡記錄顯示加班時數？
**A**: 這是多段式打卡的設計邏輯：
1. **避免重複計算**: 防止同一天的加班時數被重複累加
2. **統一顯示**: 在最後一筆記錄統一顯示當天總加班時數
3. **薪資整合**: 薪資計算會從加班記錄中正確取得總時數
4. **報表一致性**: 確保各種報表的加班時數統計一致

### Q8: 多段式打卡後薪資單沒有顯示加班費？
**A**: 可能的解決方案：
1. **檢查加班記錄狀態**: 確認加班記錄已提交或核准
2. **重新計算薪資單**: 點擊薪資單的「計算薪資單」按鈕
3. **檢查薪資單類型**: 確認查看的是正確的薪資單版本
4. **驗證權限設定**: 確認用戶有查看對應加班時數的權限
5. **查看系統日誌**: 檢查是否有計算錯誤的日誌訊息
### Q9: 加班記錄無法核准？
**A**: 可能原因：
1. 使用者沒有核准權限 (需要薪酬管理員權限)
2. 加班記錄狀態不是「已提交」
3. 超過月度加班限制
4. 加班日期或時數設定錯誤

### Q10: 如何備份薪資資料？
**A**: 建議方式：
1. 定期備份整個 Odoo 資料庫
2. 匯出重要的薪資報表
3. 備份加班記錄和假日設定
4. 建立資料備份排程

## 故障排除指南

### 安裝問題

#### 問題: 模組安裝失敗
**可能原因**:
- 缺少依賴模組
- Python 套件未安裝
- 檔案權限問題

**解決方案**:
```bash
# 1. 安裝依賴套件
pip install pandas

# 2. 檢查檔案權限
chown -R odoo:odoo /path/to/addons/l10n_tw_hr_payroll

# 3. 重啟 Odoo 服務
sudo systemctl restart odoo
```

#### 問題: 資料載入錯誤
**解決方案**:
1. 檢查資料庫連線
2. 確認 PostgreSQL 版本 (需要12+)
3. 檢查資料庫權限設定

### 功能問題

#### 問題: 薪資單計算錯誤
**檢查項目**:
1. 員工合約設定是否正確
2. 加班記錄是否已核准
3. 薪資結構設定是否完整
4. 工作時間設定是否正確

**解決步驟**:
1. 重新計算薪資單
2. 檢查加班費率設定
3. 驗證工作日數計算
4. 確認各項津貼設定

#### 問題: 權限設定無效
**解決方案**:
1. 清除瀏覽器快取
2. 重新登入系統
3. 檢查群組繼承關係
4. 重啟 Odoo 服務

### 效能問題

#### 問題: 報表查詢緩慢
**優化方案**:
1. 為常用查詢欄位建立索引
2. 限制報表查詢期間
3. 使用分頁顯示大量資料
4. 定期清理過期資料

#### 問題: 大量資料匯入緩慢
**解決方案**:
1. 分批匯入資料
2. 在非尖峰時間執行
3. 暫時停用不必要的計算欄位
4. 使用資料庫直接匯入 (進階用戶)

## 技術支援

### 聯絡資訊
- **維護者**: <EMAIL>
- **專案版本**: 1.0
- **Odoo 版本**: 18.0
- **最後更新**: 2025-06-17

### 技術文檔
- [專案架構文檔](../PROJECT_ARCHITECTURE.md)
- [考勤架構設計文檔](../ATTENDANCE_ARCHITECTURE_DESIGN.md)
- [專案完成記錄](../TASK_INCOMPLETE_WORK.md)

### 系統狀態
- ✅ 所有功能已完成並測試
- ✅ 符合 Odoo 18 最新標準
- ✅ External ID 錯誤已完全修復
- ✅ 薪資單計算錯誤修復完成
- ✅ 考勤資料與薪資計算整合完成
- ✅ 模組安裝測試通過
- ✅ 準備最終測試階段

### 最新修復成果 (2025-06-17)
- ✅ 修復薪資單 compute_sheet 功能
- ✅ 完善加班費計算邏輯
- ✅ 修復 check_in_date 欄位相關問題
- ✅ 整合考勤資料與薪資計算
- ✅ 修復標準版與完整版薪資單差異計算
- ✅ 模組現在可以正常安裝和運行

### 薪資單功能說明 ✨ **[新增]**
- **compute_sheet 功能**: 薪資單計算功能已修復，可正常計算加班費
- **加班費計算**: 支援每日分段計算（前2小時1.34倍，後續1.67倍）
- **權限控制**: 不同權限用戶看到不同版本的薪資單內容
- **測試建議**: 建議測試薪資單計算功能，驗證加班費計算準確性

---

**使用說明書版本**: 1.2
**適用系統版本**: l10n_tw_hr_payroll v1.0
**文檔建立日期**: 2025-06-17
**文檔狀態**: ✅ 完整版本，包含薪資單修復說明，準備測試指南