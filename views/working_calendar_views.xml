<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- views/working_calendar_views.xml -->
    
    <!-- 工作行事曆覆蓋設定 Tree View -->
    <record id="view_tw_working_calendar_override_tree" model="ir.ui.view">
        <field name="name">tw.working.calendar.override.tree</field>
        <field name="model">tw.working.calendar.override</field>
        <field name="arch" type="xml">
            <list decoration-info="state=='draft'" decoration-success="state=='confirmed'" decoration-muted="state=='cancelled'">
                <field name="date"/>
                <field name="calendar_id"/>
                <field name="is_working_day"/>
                <field name="working_hours" invisible="is_working_day == False"/>
                <field name="reason"/>
                <field name="state"/>
                <button name="action_confirm" string="確認" type="object"
                        icon="fa-check" invisible="state != 'draft'"/>
                <button name="action_cancel" string="取消" type="object"
                        icon="fa-times" invisible="state != 'confirmed'"/>
            </list>
        </field>
    </record>

    <!-- 工作行事曆覆蓋設定 Calendar View -->
    <record id="view_tw_working_calendar_override_calendar" model="ir.ui.view">
        <field name="name">tw.working.calendar.override.calendar</field>
        <field name="model">tw.working.calendar.override</field>
        <field name="arch" type="xml">
            <calendar string="工作日覆蓋行事曆" date_start="date" color="state" mode="month">
                <field name="reason"/>
                <field name="is_working_day"/>
                <field name="working_hours"/>
                <field name="state"/>
            </calendar>
        </field>
    </record>

    <!-- 工作行事曆覆蓋設定 Search View -->
    <record id="view_tw_working_calendar_override_search" model="ir.ui.view">
        <field name="name">tw.working.calendar.override.search</field>
        <field name="model">tw.working.calendar.override</field>
        <field name="arch" type="xml">
            <search>
                <field name="date"/>
                <field name="calendar_id"/>
                <field name="reason"/>
                
                <filter name="working_days" string="工作日" domain="[('is_working_day', '=', True)]"/>
                <filter name="non_working_days" string="非工作日" domain="[('is_working_day', '=', False)]"/>
                <filter name="confirmed" string="已確認" domain="[('state', '=', 'confirmed')]"/>
                <filter name="this_month" string="本月"
                        domain="[('date', '>=', context_today().strftime('%Y-%m-01'))]"/>
                <filter name="saturday" string="週六" domain="[('date', 'like', '%-06')]"/>
                
                <group expand="1" string="分組">
                    <filter name="group_calendar" string="行事曆" context="{'group_by': 'calendar_id'}"/>
                    <filter name="group_state" string="狀態" context="{'group_by': 'state'}"/>
                    <filter name="group_month" string="月份" context="{'group_by': 'date:month'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- 擴展資源行事曆視圖 -->
    <record id="view_resource_calendar_form_tw" model="ir.ui.view">
        <field name="name">resource.calendar.form.tw</field>
        <field name="model">resource.calendar</field>
        <field name="inherit_id" ref="resource.resource_calendar_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='company_id']" position="after">
                <field name="is_tw_calendar"/>
                <field name="default_daily_hours" invisible="is_tw_calendar == False"/>
                <field name="default_weekly_hours" invisible="is_tw_calendar == False"/>
            </xpath>
            
            <!-- 移除加班費率設定頁面 - 交由 salary rule 處理 -->
            
            <xpath expr="//page[@name='working_hours']" position="after">
                <page name="tw_overrides" string="覆蓋設定" invisible="is_tw_calendar == False">
                    <field name="tw_override_ids">
                        <list>
                            <field name="date"/>
                            <field name="is_working_day"/>
                            <field name="working_hours"/>
                            <field name="reason"/>
                            <field name="state"/>
                            <button name="action_confirm" string="確認" type="object"
                                    icon="fa-check" invisible="state != 'draft'"/>
                        </list>
                    </field>
                </page>
            </xpath>
        </field>
    </record>

    <!-- 工作行事曆出勤記錄擴展 -->
    <record id="view_resource_calendar_attendance_tree_tw" model="ir.ui.view">
        <field name="name">resource.calendar.attendance.tree.tw</field>
        <field name="model">resource.calendar.attendance</field>
        <field name="inherit_id" ref="resource.view_resource_calendar_attendance_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='hour_to']" position="after">
                <field name="is_makeup_day"/>
                <field name="tw_override_id" invisible="tw_override_id == False"/>
            </xpath>
        </field>
    </record>

</odoo>