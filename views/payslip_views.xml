<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- views/payslip_views.xml -->
    
    <!-- 雙薪資單生成嚮導視圖 -->
    <record id="view_tw_dual_payslip_wizard_form" model="ir.ui.view">
        <field name="name">tw.dual.payslip.wizard.form</field>
        <field name="model">tw.dual.payslip.wizard</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group string="薪資期間">
                            <field name="date_from"/>
                            <field name="date_to"/>
                        </group>
                        <group string="生成選項">
                            <field name="auto_confirm"/>
                            <field name="include_draft_overtime"/>
                        </group>
                    </group>
                    
                    <group string="員工選擇">
                        <field name="employee_selection" widget="radio"/>
                        <field name="department_ids" widget="many2many_tags"
                               invisible="employee_selection != 'departments'" required="employee_selection == 'departments'"/>
                        <field name="employee_ids" widget="many2many_tags"
                               invisible="employee_selection != 'employees'" required="employee_selection == 'employees'"/>
                    </group>
                    
                    <div class="alert alert-info" role="alert">
                        <strong>預估生成數量：</strong>
                        <field name="estimated_count"/> 份薪資單 (每位員工生成標準版和完整版各一份)
                    </div>
                    
                    <div class="alert alert-warning" role="alert">
                        <strong>注意事項：</strong>
                        <ul>
                            <li>標準版薪資單：顯示限制範圍內的加班時數</li>
                            <li>完整版薪資單：顯示所有實際加班時數，僅特定用戶可查看</li>
                            <li>生成後請檢查並確認薪資單內容</li>
                        </ul>
                    </div>
                </sheet>
                
                <footer>
                    <button name="action_generate_payslips" string="生成薪資單" type="object" class="btn-primary"/>
                    <button string="取消" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- 薪資單列表視圖擴展 -->
    <record id="view_hr_payslip_tree_tw" model="ir.ui.view">
        <field name="name">hr.payslip.tree.tw</field>
        <field name="model">hr.payslip</field>
        <field name="inherit_id" ref="hr_payroll.view_hr_payslip_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='employee_id']" position="after">
                <field name="payslip_type" optional="show"/>
            </xpath>
            <xpath expr="//field[@name='net_wage']" position="after">
                <field name="displayed_overtime_hours" optional="show" widget="float_time"/>
                <field name="total_overtime_hours" optional="hide" widget="float_time"
                       groups="l10n_tw_hr_payroll.group_hr_manager"/>
                <field name="hidden_overtime_hours" optional="hide" widget="float_time"
                       groups="l10n_tw_hr_payroll.group_hr_manager"/>
            </xpath>
        </field>
    </record>

    <!-- 薪資單搜尋視圖擴展 -->
    <record id="view_hr_payslip_search_tw" model="ir.ui.view">
        <field name="name">hr.payslip.search.tw</field>
        <field name="model">hr.payslip</field>
        <field name="inherit_id" ref="hr_payroll.view_hr_payslip_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='employee_id']" position="after">
                <field name="payslip_type"/>
            </xpath>
            
            <xpath expr="//filter[@name='done']" position="after">
                <filter name="standard_payslips" string="標準版薪資單" domain="[('payslip_type', '=', 'standard')]"/>
                <filter name="full_payslips" string="完整版薪資單" domain="[('payslip_type', '=', 'full')]" 
                        groups="l10n_tw_hr_payroll.group_hr_manager"/>
                <filter name="dual_payslips" string="雙薪資單" domain="[('paired_payslip_id', '!=', False)]"/>
                <filter name="with_hidden_overtime" string="含隱藏加班" domain="[('payslip_type', '=', 'full')]"
                        groups="l10n_tw_hr_payroll.group_hr_manager"/>
            </xpath>
            
            <xpath expr="//filter[@name='group_by_structure']" position="after">
                <filter name="group_payslip_type" string="薪資單類型" context="{'group_by': 'payslip_type'}"/>
            </xpath>
        </field>
    </record>

    <!-- 薪資單明細視圖擴展 -->
    <record id="view_hr_payslip_line_tree_tw" model="ir.ui.view">
        <field name="name">hr.payslip.line.tree.tw</field>
        <field name="model">hr.payslip.line</field>
        <field name="inherit_id" ref="hr_payroll.view_hr_payslip_line_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='amount']" position="after">
                <field name="is_hidden_item" optional="hide" groups="l10n_tw_hr_payroll.group_hr_manager"/>
                <field name="visible_to_employee" optional="hide"/>
            </xpath>
        </field>
    </record>

    <!-- 薪資單報表分析視圖 -->
    <record id="view_tw_payslip_report_graph" model="ir.ui.view">
        <field name="name">tw.payslip.report.graph</field>
        <field name="model">tw.payslip.report</field>
        <field name="arch" type="xml">
            <graph string="薪資單分析" type="bar">
                <field name="employee_id"/>
                <field name="basic_wage" type="measure"/>
                <field name="displayed_overtime_hours" type="measure"/>
                <field name="hidden_overtime_hours" type="measure" groups="l10n_tw_hr_payroll.group_hr_manager"/>
            </graph>
        </field>
    </record>

    <record id="view_tw_payslip_report_pivot" model="ir.ui.view">
        <field name="name">tw.payslip.report.pivot</field>
        <field name="model">tw.payslip.report</field>
        <field name="arch" type="xml">
            <pivot string="薪資分析透視表">
                <field name="department_id" type="row"/>
                <field name="date_from" interval="month" type="col"/>
                <field name="basic_wage" type="measure"/>
                <field name="displayed_overtime_hours" type="measure"/>
                <field name="hidden_overtime_hours" type="measure" groups="l10n_tw_hr_payroll.group_hr_manager"/>
                <field name="total_overtime_hours" type="measure" groups="l10n_tw_hr_payroll.group_hr_manager"/>
                <field name="overtime_hide_ratio" type="measure" groups="l10n_tw_hr_payroll.group_hr_manager"/>
            </pivot>
        </field>
    </record>

    <record id="view_tw_payslip_report_tree" model="ir.ui.view">
        <field name="name">tw.payslip.report.tree</field>
        <field name="model">tw.payslip.report</field>
        <field name="arch" type="xml">
            <list>
                <field name="employee_id"/>
                <field name="department_id"/>
                <field name="date_from"/>
                <field name="date_to"/>
                <field name="basic_wage"/>
                <!-- 移除加班費欄位 - 交由 salary rule 處理 -->
                <field name="total_overtime_hours" widget="float_time" groups="l10n_tw_hr_payroll.group_hr_manager"/>
                <field name="displayed_overtime_hours" widget="float_time"/>
                <field name="hidden_overtime_hours" widget="float_time" groups="l10n_tw_hr_payroll.group_hr_manager"/>
                <field name="overtime_hide_ratio" groups="l10n_tw_hr_payroll.group_hr_manager"/>
            </list>
        </field>
    </record>

    <!-- 薪資單批次視圖增強 - 暫時移除以避免欄位不存在的問題 -->
    <!--
    <record id="view_hr_payslip_run_tree_tw" model="ir.ui.view">
        <field name="name">hr.payslip.run.tree.tw</field>
        <field name="model">hr.payslip.run</field>
        <field name="inherit_id" ref="hr_payroll.view_hr_payslip_run_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='date_start']" position="after">
                <field name="batch_type" optional="show"/>
                <field name="standard_payslip_count" optional="show"/>
                <field name="full_payslip_count" optional="show" groups="l10n_tw_hr_payroll.group_hr_manager"/>
            </xpath>
        </field>
    </record>
    -->

    <!-- 薪資單比較視圖 -->
    <record id="view_payslip_comparison_form" model="ir.ui.view">
        <field name="name">payslip.comparison.form</field>
        <field name="model">hr.payslip</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_title">
                        <h1>薪資單比較 - <field name="employee_id" readonly="1"/></h1>
                        <h2><field name="date_from"/> 至 <field name="date_to"/></h2>
                    </div>
                    
                    <group>
                        <group string="標準版薪資單">
                            <field name="displayed_overtime_hours" widget="float_time"/>
                            <!-- 移除加班費欄位 - 交由 salary rule 處理 -->
                        </group>
                        <group string="完整版薪資單" groups="l10n_tw_hr_payroll.group_hr_manager">
                            <field name="total_overtime_hours" widget="float_time"/>
                            <field name="hidden_overtime_hours" widget="float_time"/>
                            <!-- 移除加班費欄位 - 交由 salary rule 處理 -->
                        </group>
                    </group>
                    
                    <div class="row" groups="l10n_tw_hr_payroll.group_hr_manager">
                        <div class="col-12">
                            <div class="alert alert-warning" role="alert">
                                <strong>差異分析：</strong>
                                <br/>
                                隱藏加班時數：<field name="hidden_overtime_hours" widget="float_time"/> 
                                <!-- 移除加班費顯示 - 交由 salary rule 處理 -->
                                隱藏比例：計算中...
                            </div>
                        </div>
                    </div>
                </sheet>
            </form>
        </field>
    </record>

    <!-- 員工薪資單檢視動作 -->
    <record id="action_employee_payslips" model="ir.actions.act_window">
        <field name="name">我的薪資單</field>
        <field name="res_model">hr.payslip</field>
        <field name="view_mode">list,form</field>
        <field name="domain">[('employee_id.user_id', '=', uid), ('payslip_type', 'in', ['standard', False])]</field>
        <field name="context">{'search_default_employee_id': uid}</field>
    </record>

    <!-- 管理員完整薪資單檢視 -->
    <record id="action_full_payslips" model="ir.actions.act_window">
        <field name="name">完整版薪資單</field>
        <field name="res_model">hr.payslip</field>
        <field name="view_mode">list,form</field>
        <field name="domain">[('payslip_type', '=', 'full')]</field>
        <field name="context">{'search_default_full_payslips': 1}</field>
    </record>

    <!-- 薪資單對比分析動作 -->
    <record id="action_payslip_comparison" model="ir.actions.act_window">
        <field name="name">薪資單比較分析</field>
        <field name="res_model">tw.payslip.report</field>
        <field name="view_mode">graph,pivot,list</field>
    </record>

</odoo>