<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- views/overtime_views.xml -->
    
    <!-- 加班時數限制 Tree View -->
    <record id="view_tw_overtime_limit_tree" model="ir.ui.view">
        <field name="name">tw.overtime.limit.tree</field>
        <field name="model">tw.overtime.limit</field>
        <field name="arch" type="xml">
            <list decoration-success="active==True" decoration-muted="active==False">
                <field name="name"/>
                <field name="monthly_limit" widget="float_time"/>
                <field name="display_limit" widget="float_time"/>
                <field name="auto_approve"/>
                <field name="auto_approve_limit" widget="float_time"/>
                <field name="use_random_display"/>
                <field name="apply_to_all"/>
                <field name="department_ids" widget="many2many_tags" invisible="apply_to_all == True"/>
                <field name="priority"/>
                <field name="date_from"/>
                <field name="date_to"/>
                <field name="active"/>
            </list>
        </field>
    </record>

    <!-- 加班時數限制 Form View -->
    <record id="view_tw_overtime_limit_form" model="ir.ui.view">
        <field name="name">tw.overtime.limit.form</field>
        <field name="model">tw.overtime.limit</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="toggle_active" type="object" class="oe_stat_button" icon="fa-archive">
                            <field name="active" widget="boolean_button" options='{"terminology": "archive"}'/>
                        </button>
                    </div>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="限制設定名稱..."/>
                        </h1>
                    </div>
                    
                    <group>
                        <group string="時數設定">
                            <field name="monthly_limit" widget="float_time"/>
                            <field name="display_limit" widget="float_time"/>
                            <field name="priority"/>
                        </group>
                        <group string="生效期間">
                            <field name="date_from"/>
                            <field name="date_to"/>
                            <field name="company_id" options="{'no_create': True}"/>
                        </group>
                    </group>
                    
                    <group string="審核設定">
                        <group>
                            <field name="auto_approve"/>
                            <field name="auto_approve_limit" invisible="auto_approve == False"/>
                        </group>
                    </group>
                    
                    <group string="亂數顯示設定">
                        <group>
                            <field name="use_random_display"/>
                            <field name="random_hide_probability" invisible="use_random_display == False"/>
                            <field name="random_partial_probability" invisible="use_random_display == False"/>
                        </group>
                        <group invisible="use_random_display == False">
                            <div class="alert alert-info" role="alert">
                                <strong>亂數顯示說明：</strong><br/>
                                • 完全隱藏機率：員工加班完全不顯示的機率<br/>
                                • 部分顯示機率：員工加班部分顯示的機率<br/>
                                • 剩餘機率：員工加班完全顯示的機率<br/>
                                • 總機率不能超過 1.0
                            </div>
                        </group>
                    </group>
                    
                    <group string="適用範圍">
                        <field name="apply_to_all"/>
                        <field name="department_ids" widget="many2many_tags"
                               invisible="apply_to_all == True"/>
                        <field name="employee_ids" widget="many2many_tags"
                               invisible="apply_to_all == True"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- 加班時數限制 Search View -->
    <record id="view_tw_overtime_limit_search" model="ir.ui.view">
        <field name="name">tw.overtime.limit.search</field>
        <field name="model">tw.overtime.limit</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="department_ids"/>
                <field name="employee_ids"/>
                
                <filter name="active" string="啟用" domain="[('active', '=', True)]"/>
                <filter name="inactive" string="停用" domain="[('active', '=', False)]"/>
                <filter name="company_wide" string="全公司適用" domain="[('apply_to_all', '=', True)]"/>
                <filter name="current" string="目前生效" 
                        domain="['|', ('date_from', '=', False), ('date_from', '&lt;=', context_today()),
                                 '|', ('date_to', '=', False), ('date_to', '&gt;=', context_today())]"/>
                
                <group expand="1" string="分組">
                    <filter name="group_priority" string="優先級" context="{'group_by': 'priority'}"/>
                    <filter name="group_company" string="公司" context="{'group_by': 'company_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- 加班時數限制 Action -->
    <record id="action_tw_overtime_limit" model="ir.actions.act_window">
        <field name="name">加班時數限制</field>
        <field name="res_model">tw.overtime.limit</field>
        <field name="view_mode">list,form</field>
        <field name="view_id" ref="view_tw_overtime_limit_tree"/>
        <field name="search_view_id" ref="view_tw_overtime_limit_search"/>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                設定加班時數限制
            </p>
            <p>
                根據台灣勞基法設定員工的加班時數限制，包括每月上限和顯示限制。
            </p>
        </field>
    </record>

</odoo>