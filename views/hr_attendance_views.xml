<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- 重構 hr.attendance 主要動作 - 重定向到 tw.hr.attendance -->
        <record id="hr_attendance.hr_attendance_action" model="ir.actions.act_window">
            <field name="name">台灣考勤記錄</field>
            <field name="res_model">tw.hr.attendance</field>
            <field name="view_mode">list,form,gantt</field>
            <field name="search_view_id" ref="view_tw_hr_attendance_search"/>
            <field name="context">{
                'search_default_my_attendances': 1,
                'search_default_this_month': 1
            }</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    建立新的台灣考勤記錄
                </p>
                <p>
                    台灣考勤系統提供完整的考勤管理功能，包含加班時數計算和權限控制。
                </p>
            </field>
        </record>

        <!-- 重構 hr.attendance 管理動作 - 重定向到 tw.hr.attendance -->
        <record id="hr_attendance.hr_attendance_management_action" model="ir.actions.act_window">
            <field name="name">台灣考勤管理</field>
            <field name="res_model">tw.hr.attendance</field>
            <field name="view_mode">list,form</field>
            <field name="search_view_id" ref="view_tw_hr_attendance_search"/>
            <field name="context">{
                'search_default_pending_approval': 1
            }</field>
            <field name="domain">[('check_out', '!=', False)]</field>
        </record>

        <!-- 重構 hr.attendance 報表動作 - 重定向到 tw.hr.attendance -->
        <record id="hr_attendance.hr_attendance_reporting" model="ir.actions.act_window">
            <field name="name">台灣考勤報表</field>
            <field name="res_model">tw.hr.attendance</field>
            <field name="view_mode">pivot,graph,list</field>
            <field name="search_view_id" ref="view_tw_hr_attendance_search"/>
            <field name="context">{
                'search_default_this_month': 1,
                'search_default_group_employee': 1
            }</field>
        </record>

        <!-- 為管理員保留原生 hr.attendance 視圖的存取 -->
        <record id="hr_attendance_native_action" model="ir.actions.act_window">
            <field name="name">原生考勤記錄 (管理員)</field>
            <field name="res_model">hr.attendance</field>
            <field name="view_mode">list,form</field>
            <field name="context">{
                'search_default_groupby_name': 1,
                'search_default_employee': 2
            }</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_empty_folder">
                    原生考勤記錄管理
                </p>
                <p>
                    此視圖僅供系統管理員進行底層資料管理使用。
                </p>
            </field>
        </record>

        <!-- 簡化的 hr.attendance 表單視圖 (僅供管理員使用) -->
        <record id="hr_attendance_view_form_inherit" model="ir.ui.view">
            <field name="name">hr.attendance.form.inherit</field>
            <field name="model">hr.attendance</field>
            <field name="inherit_id" ref="hr_attendance.hr_attendance_view_form"/>
            <field name="arch" type="xml">
                <!-- 移除所有自定義欄位和按鈕，保持原生功能 -->
                <xpath expr="//header" position="inside">
                    <div class="alert alert-info" role="alert">
                        <strong>提示：</strong>此為原生考勤記錄視圖，建議使用台灣考勤系統進行管理。
                        <a href="/web#action=l10n_tw_hr_payroll.action_tw_hr_attendance" class="btn btn-link" role="button">
                            前往台灣考勤系統
                        </a>
                    </div>
                </xpath>
            </field>
        </record>

        <!-- 移除有問題的視圖繼承 - 不是必需的功能 -->

        <!-- 台灣出勤報表視圖 (基本版本) -->
        <record id="hr_attendance_tw_report_view" model="ir.ui.view">
            <field name="name">hr.attendance.tw.report</field>
            <field name="model">hr.attendance</field>
            <field name="arch" type="xml">
                <list string="原生出勤報表" 
                      create="false" 
                      edit="false" 
                      delete="false">
                    <field name="employee_id"/>
                    <field name="check_in"/>
                    <field name="check_out"/>
                    <field name="worked_hours" widget="float_time"/>
                </list>
            </field>
        </record>

        <!-- 台灣出勤報表動作 -->
        <record id="action_hr_attendance_tw_report" model="ir.actions.act_window">
            <field name="name">原生出勤報表</field>
            <field name="res_model">hr.attendance</field>
            <field name="view_mode">list</field>
            <field name="view_id" ref="hr_attendance_tw_report_view"/>
            <field name="domain">[]</field>
            <field name="context">{
                'search_default_groupby_employee': 1,
                'search_default_this_month': 1
            }</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    原生出勤報表
                </p>
                <p>
                    此報表顯示原生的出勤記錄。建議使用台灣考勤系統獲得完整功能。
                </p>
            </field>
        </record>

    </data>
</odoo>