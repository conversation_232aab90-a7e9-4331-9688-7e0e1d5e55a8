<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- 台灣考勤記錄 - 列表視圖 -->
    <record id="view_tw_hr_attendance_list" model="ir.ui.view">
        <field name="name">tw.hr.attendance.list</field>
        <field name="model">tw.hr.attendance</field>
        <field name="arch" type="xml">
            <list string="台灣考勤記錄"
                  default_order="check_in desc"
                  decoration-success="state == 'validated'"
                  decoration-warning="state == 'confirmed'"
                  decoration-info="state == 'draft'">
                <header>
                    <button name="action_sync_from_hr_attendance" string="全部手動同步" type="object"
                            groups="l10n_tw_hr_payroll.group_hr_manager"
                            class="btn-primary"/>
                    <button name="action_create_from_hr_attendance" string="批量同步選中記錄" type="object"
                            groups="l10n_tw_hr_payroll.group_hr_manager"
                            class="btn-secondary"/>
                    <button name="action_batch_approve_overtime" string="批量核准加班" type="object"
                            groups="l10n_tw_hr_payroll.group_hr_officer,l10n_tw_hr_payroll.group_hr_manager"
                            class="btn-success"/>
                    <button name="action_batch_refuse_overtime" string="批量拒絕加班" type="object"
                            groups="l10n_tw_hr_payroll.group_hr_officer,l10n_tw_hr_payroll.group_hr_manager"
                            class="btn-danger"/>
                    <button name="action_delete_with_sync" string="批量刪除系統記錄" type="object"
                            groups="l10n_tw_hr_payroll.group_hr_manager,l10n_tw_hr_payroll.group_system_admin"
                            class="btn-danger"
                            confirm="確定要刪除選中的記錄及其對應的系統考勤記錄嗎？此操作無法復原。"/>
                </header>
                <field name="employee_id" widget="many2one_avatar_user"/>
                <field name="department_id"/>
                <field name="check_in"/>
                <field name="check_out_real" groups="l10n_tw_hr_payroll.group_hr_manager,l10n_tw_hr_payroll.group_system_admin" optional="hide"/>
                <field name="check_out"/>
                <field name="worked_hours" sum="總工作時數" widget="float_time"/>
                <field name="displayed_overtime_hours" sum="總顯示加班時數" widget="float_time"
                       groups="l10n_tw_hr_payroll.group_hr_officer,l10n_tw_hr_payroll.group_hr_manager,l10n_tw_hr_payroll.group_system_admin"
                       optional="show"/>
                <field name="hidden_overtime_hours" sum="總隱藏加班時數" widget="float_time"
                       groups="l10n_tw_hr_payroll.group_hr_manager,l10n_tw_hr_payroll.group_system_admin"
                       optional="hide"/>
                <field name="overtime_hours" sum="總加班時數" widget="float_time"
                       groups="l10n_tw_hr_payroll.group_hr_manager,l10n_tw_hr_payroll.group_system_admin"
                       optional="show"/>
                <field name="validated_overtime_hours" sum="總核准加班時數" widget="float_time"
                       groups="l10n_tw_hr_payroll.group_hr_officer,l10n_tw_hr_payroll.group_hr_manager,l10n_tw_hr_payroll.group_system_admin"
                       optional="show"/>
                <field name="overtime_status" groups="l10n_tw_hr_payroll.group_hr_officer,l10n_tw_hr_payroll.group_hr_manager,l10n_tw_hr_payroll.group_system_admin" optional="show"
                       widget="badge"
                       decoration-warning="overtime_status == 'to_approve'"
                       decoration-success="overtime_status == 'approved'"
                       decoration-danger="overtime_status == 'refused'"
                       invisible="overtime_hours == 0"/>
                <field name="state" widget="badge"
                       decoration-success="state == 'validated'"
                       decoration-warning="state == 'confirmed'"
                       decoration-info="state == 'draft'"/>
                <field name="hr_attendance_id" optional="hide"/>
                <field name="show_overtime_details" column_invisible="True"/>
                <field name="show_hidden_overtime" column_invisible="True"/>
            </list>
        </field>
    </record>

    <!-- 台灣考勤記錄 - 甘特圖視圖 -->
    <record id="view_tw_hr_attendance_gantt" model="ir.ui.view">
        <field name="name">tw.hr.attendance.gantt</field>
        <field name="model">tw.hr.attendance</field>
        <field name="arch" type="xml">
            <gantt string="台灣考勤甘特圖"
                   date_start="check_in"
                   date_stop="check_out"
                   default_group_by="employee_id"
                   color="employee_id"
                   precision="{'day': 'hour:quarter', 'week': 'day:hour', 'month': 'day:hour'}"
                   scales="day,week,month"
                   sample="1">
                <field name="employee_id"/>
                <field name="department_id"/>
                <field name="worked_hours"/>
                <field name="overtime_hours"/>
                <field name="validated_overtime_hours"/>
                <field name="overtime_status"/>
                <field name="state"/>
                <templates>
                    <div t-name="gantt-popover">
                        <div class="o_gantt_popover_content">
                            <div><strong t-esc="employee_id[1]"/></div>
                            <div>部門: <t t-esc="department_id and department_id[1] or '未設定'"/></div>
                            <div>上班: <t t-esc="check_in"/></div>
                            <div>下班: <t t-esc="check_out or '進行中'"/></div>
                            <div>工作時數: <t t-esc="worked_hours"/> 小時</div>
                            <div t-if="overtime_hours > 0">加班時數: <t t-esc="overtime_hours"/> 小時</div>
                            <div t-if="validated_overtime_hours > 0">核准加班: <t t-esc="validated_overtime_hours"/> 小時</div>
                            <div>狀態: <t t-esc="state"/></div>
                        </div>
                    </div>
                </templates>
            </gantt>
        </field>
    </record>

    <!-- 台灣考勤記錄 - 表單視圖 -->
    <record id="view_tw_hr_attendance_form" model="ir.ui.view">
        <field name="name">tw.hr.attendance.form</field>
        <field name="model">tw.hr.attendance</field>
        <field name="arch" type="xml">
            <form string="台灣考勤記錄">
                <header>
                    <button name="action_confirm" string="確認" type="object"
                            invisible="state != 'draft'" class="btn-primary"/>
                    <button name="action_approve_overtime" string="核准加班" type="object"
                            invisible="not show_overtime_details or overtime_status != 'to_approve'"
                            class="btn-success"/>
                    <button name="action_refuse_overtime" string="拒絕加班" type="object"
                            invisible="not show_overtime_details or overtime_status != 'to_approve'"
                            class="btn-danger"/>
                    <button name="action_sync_from_hr_attendance" string="同步" type="object"
                            groups="l10n_tw_hr_payroll.group_hr_manager"
                            class="btn-secondary"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,confirmed,validated"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="display_name" readonly="1"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group string="員工資訊">
                            <field name="employee_id" required="1"/>
                            <field name="department_id"/>
                        </group>
                        <group string="考勤時間">
                            <field name="check_in" required="1"/>
                            <field name="check_out_real" groups="l10n_tw_hr_payroll.group_hr_manager,l10n_tw_hr_payroll.group_system_admin"/>
                            <field name="check_out"/>
                            <field name="worked_hours"/>
                        </group>
                    </group>
                    
                    <group string="加班資訊" groups="l10n_tw_hr_payroll.group_hr_officer,l10n_tw_hr_payroll.group_hr_manager,l10n_tw_hr_payroll.group_system_admin">
                        <group>
                            <field name="displayed_overtime_hours"/>
                            <field name="overtime_status"/>
                        </group>
                        <group>
                            <field name="validated_overtime_hours"/>
                        </group>
                    </group>
                    
                    <group string="完整加班資訊" groups="l10n_tw_hr_payroll.group_hr_manager,l10n_tw_hr_payroll.group_system_admin">
                        <group>
                            <field name="overtime_hours" string="實際加班時數"/>
                            <field name="hidden_overtime_hours" string="隱藏加班時數"/>
                        </group>
                        <group>
                            <field name="displayed_overtime_hours" readonly="1" string="顯示加班時數"/>
                        </group>
                    </group>
                    
                    <group string="系統資訊">
                        <field name="hr_attendance_id"/>
                    </group>
                    
                    <notebook>
                        <page string="備註">
                            <field name="notes" nolabel="1"/>
                        </page>
                    </notebook>
                    
                    <!-- 隱藏欄位 -->
                    <field name="show_overtime_details" invisible="1"/>
                    <field name="show_hidden_overtime" invisible="1"/>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- 台灣考勤記錄 - 搜尋視圖 -->
    <record id="view_tw_hr_attendance_search" model="ir.ui.view">
        <field name="name">tw.hr.attendance.search</field>
        <field name="model">tw.hr.attendance</field>
        <field name="arch" type="xml">
            <search string="搜尋台灣考勤記錄">
                <field name="employee_id"/>
                <field name="department_id"/>
                <field name="check_in"/>
                <field name="check_out"/>
                <separator/>
                <filter name="my_attendances" string="我的考勤" 
                        domain="[('employee_id.user_id', '=', uid)]"/>
                <filter name="today" string="今天" date="check_in"/>
                <filter name="this_week" string="本週" date="check_in"/>
                <filter name="this_month" string="本月" date="check_in"/>
                <separator/>
                <filter name="has_overtime" string="有加班" 
                        domain="[('overtime_hours', '>', 0)]"/>
                <filter name="pending_approval" string="待核准加班" 
                        domain="[('overtime_status', '=', 'to_approve')]"/>
                <separator/>
                <filter name="draft" string="草稿" 
                        domain="[('state', '=', 'draft')]"/>
                <filter name="confirmed" string="已確認" 
                        domain="[('state', '=', 'confirmed')]"/>
                <filter name="validated" string="已驗證" 
                        domain="[('state', '=', 'validated')]"/>
                <group expand="0" string="分組">
                    <filter name="group_employee" string="員工" context="{'group_by': 'employee_id'}"/>
                    <filter name="group_department" string="部門" context="{'group_by': 'department_id'}"/>
                    <filter name="group_date" string="日期" context="{'group_by': 'check_in:day'}"/>
                    <filter name="group_state" string="狀態" context="{'group_by': 'state'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- 台灣考勤記錄 - 行動作 -->
    <record id="action_tw_hr_attendance" model="ir.actions.act_window">
        <field name="name">台灣考勤記錄</field>
        <field name="res_model">tw.hr.attendance</field>
        <field name="view_mode">list,gantt,form</field>
        <field name="search_view_id" ref="view_tw_hr_attendance_search"/>
        <field name="context">{
            'search_default_my_attendances': 1,
            'search_default_this_month': 1
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                建立新的台灣考勤記錄
            </p>
            <p>
                台灣考勤系統提供完整的考勤管理功能，包含加班時數計算和權限控制。
            </p>
        </field>
    </record>

    <!-- 台灣考勤甘特圖 - 行動作 -->
    <record id="action_tw_hr_attendance_gantt" model="ir.actions.act_window">
        <field name="name">台灣考勤甘特圖</field>
        <field name="res_model">tw.hr.attendance</field>
        <field name="view_mode">gantt,list,form</field>
        <field name="view_id" ref="view_tw_hr_attendance_gantt"/>
        <field name="search_view_id" ref="view_tw_hr_attendance_search"/>
        <field name="context">{
            'search_default_this_week': 1
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                查看台灣考勤甘特圖
            </p>
            <p>
                甘特圖視圖提供直觀的考勤時間軸顯示，方便管理者查看員工考勤狀況。
            </p>
        </field>
    </record>

    <!-- 我的考勤記錄 - 行動作 -->
    <record id="action_my_tw_hr_attendance" model="ir.actions.act_window">
        <field name="name">我的考勤記錄</field>
        <field name="res_model">tw.hr.attendance</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_tw_hr_attendance_search"/>
        <field name="domain">[('employee_id.user_id', '=', uid)]</field>
        <field name="context">{
            'search_default_this_month': 1
        }</field>
    </record>

    <!-- 同步管理視圖 -->
    <record id="view_sync_management" model="ir.ui.view">
        <field name="name">sync.management.form</field>
        <field name="model">tw.sync.management</field>
        <field name="arch" type="xml">
            <form string="同步管理">
                <header>
                    <button name="manual_sync_from_hr_attendance"
                            string="全部手動同步"
                            type="object"
                            class="btn-primary"
                            groups="l10n_tw_hr_payroll.group_hr_manager"/>
                    <button name="check_sync_status"
                            string="檢查同步狀態"
                            type="object"
                            class="btn-info"
                            groups="l10n_tw_hr_payroll.group_hr_manager"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>考勤記錄同步管理</h1>
                    </div>
                    <group>
                        <group string="同步功能">
                            <p>此頁面提供考勤記錄同步管理功能：</p>
                            <ul>
                                <li>全部手動同步：同步所有未同步的 hr.attendance 記錄</li>
                                <li>檢查同步狀態：檢查同步一致性並顯示統計</li>
                                <li>修復同步問題：自動修復缺失和孤立記錄</li>
                            </ul>
                        </group>
                        <group string="使用說明">
                            <p>建議操作順序：</p>
                            <ol>
                                <li>先執行「檢查同步狀態」了解目前狀況</li>
                                <li>如有缺失記錄，執行「全部手動同步」</li>
                                <li>定期檢查確保同步一致性</li>
                            </ol>
                        </group>
                    </group>
                    <field name="name" invisible="1"/>
                </sheet>
            </form>
        </field>
    </record>

    <!-- 同步管理動作 -->
    <record id="action_sync_management" model="ir.actions.act_window">
        <field name="name">同步管理</field>
        <field name="res_model">tw.sync.management</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="view_sync_management"/>
        <field name="target">new</field>
        <field name="context">{}</field>
    </record>

</odoo>