<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- 假日匯入視圖 -->
    <record id="view_tw_holiday_import_form" model="ir.ui.view">
        <field name="name">tw.holiday.import.form</field>
        <field name="model">tw.holiday.import</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_import_holidays" string="開始匯入" type="object"
                            class="btn-primary" invisible="state != 'draft'"/>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="calendar_id"/>
                            <field name="year"/>
                        </group>
                        <group>
                            <field name="import_file" filename="filename"/>
                            <field name="filename" invisible="1"/>
                            <field name="override_existing"/>
                        </group>
                    </group>
                    
                    <group string="補班日設定">
                        <field name="has_makeup_days"/>
                        <field name="makeup_day_column" invisible="has_makeup_days == False"/>
                    </group>
                    
                    <group string="匯入結果" invisible="state in ['draft', 'processing']">
                        <group>
                            <field name="total_records"/>
                            <field name="success_records"/>
                            <field name="error_records"/>
                        </group>
                    </group>
                    
                    <field name="error_log" invisible="error_log ==  False" 
                           widget="text" readonly="1"/>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_tw_holiday_import_tree" model="ir.ui.view">
        <field name="name">tw.holiday.import.tree</field>
        <field name="model">tw.holiday.import</field>
        <field name="arch" type="xml">
            <list>
                <field name="name"/>
                <field name="calendar_id"/>
                <field name="year"/>
                <field name="total_records"/>
                <field name="success_records"/>
                <field name="error_records"/>
                <field name="state" decoration-info="state=='processing'" 
                       decoration-success="state=='done'" decoration-danger="state=='error'"/>
                <field name="create_date"/>
            </list>
        </field>
    </record>

    <!-- 工作行事曆覆蓋設定視圖 -->
    <record id="view_tw_working_calendar_override_form" model="ir.ui.view">
        <field name="name">tw.working.calendar.override.form</field>
        <field name="model">tw.working.calendar.override</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_confirm" string="確認" type="object"
                            class="btn-primary" invisible="state != 'draft'"/>
                    <button name="action_cancel" string="取消" type="object" invisible="state != 'confirmed'"/>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="calendar_id"/>
                            <field name="date"/>
                            <field name="is_working_day"/>
                            <field name="reason"/>
                        </group>
                        <group>
                            <field name="working_hours" invisible="is_working_day == False"/>
                            <field name="apply_to_all"/>
                        </group>
                    </group>
                    
                    <group string="工作時間設定" invisible="is_working_day == False">
                        <group>
                            <field name="start_time" widget="float_time"/>
                            <field name="lunch_start" widget="float_time"/>
                        </group>
                        <group>
                            <field name="lunch_end" widget="float_time"/>
                            <field name="end_time" widget="float_time"/>
                        </group>
                    </group>
                    
                    <field name="employee_ids" invisible="apply_to_all == True"/>
                </sheet>
            </form>
        </field>
    </record>

    <!-- 加班記錄視圖 - 已移除，功能整合到 tw.hr.attendance -->

    <!-- 薪資單擴展視圖 -->
    <record id="view_hr_payslip_form_tw" model="ir.ui.view">
        <field name="name">hr.payslip.form.tw</field>
        <field name="model">hr.payslip</field>
        <field name="inherit_id" ref="hr_payroll.view_hr_payslip_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='contract_id']" position="after">
                <field name="payslip_type"/>
                <field name="paired_payslip_id" readonly="1" 
                       invisible="paired_payslip_id == False"/>
            </xpath>
            
            <xpath expr="//sheet" position="inside">
                <div class="oe_button_box" name="button_box">
                    <button name="action_view_paired_payslip" type="object" 
                            class="oe_stat_button" icon="fa-files-o"
                            invisible="paired_payslip_id == False">
                        <div class="o_stat_info">
                            <span class="o_stat_text">配對薪資單</span>
                        </div>
                    </button>
                    
                    <button name="action_generate_dual_payslips" type="object" 
                            class="oe_stat_button" icon="fa-copy"
                            invisible="paired_payslip_id != False or state != 'draft'"
                            groups="l10n_tw_hr_payroll.group_hr_manager">
                        <div class="o_stat_info">
                            <span class="o_stat_text">生成雙薪資單</span>
                        </div>
                    </button>
                </div>
                
                <group string="加班時數資訊" name="overtime_info">
                    <group>
                        <field name="displayed_overtime_hours"/>
                        <!-- 移除加班費欄位 - 交由 salary rule 處理 -->
                    </group>
                    <group groups="l10n_tw_hr_payroll.group_hr_manager">
                        <field name="total_overtime_hours"/>
                        <field name="hidden_overtime_hours"/>
                        <!-- 移除加班費欄位 - 交由 salary rule 處理 -->
                    </group>
                </group>
            </xpath>
        </field>
    </record>

    <!-- 薪資單批次擴展視圖 -->
    <record id="view_hr_payslip_run_form_tw" model="ir.ui.view">
        <field name="name">hr.payslip.run.form.tw</field>
        <field name="model">hr.payslip.run</field>
        <field name="inherit_id" ref="hr_payroll.hr_payslip_run_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='date_end']" position="after">
                <field name="batch_type"/>
            </xpath>
            
            <xpath expr="//button[@name='action_draft']" position="after">
                <button name="action_generate_dual_payslips" string="生成雙薪資單" type="object"
                        class="btn-primary" invisible="state != 'draft'"
                        groups="l10n_tw_hr_payroll.group_hr_manager"/>
            </xpath>
            
            <xpath expr="//field[@name='date_end']" position="after">
                <group>
                    <group>
                        <field name="standard_payslip_count"/>
                    </group>
                    <group>
                        <field name="full_payslip_count"/>
                    </group>
                </group>
            </xpath>
        </field>
    </record>

    <!-- 假日管理 Actions -->
    <record id="action_tw_holiday_import" model="ir.actions.act_window">
        <field name="name">假日匯入</field>
        <field name="res_model">tw.holiday.import</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                點擊創建新的假日匯入
            </p>
            <p>
                您可以透過 Excel 或 CSV 檔案批量匯入假日資料到系統中。
            </p>
        </field>
    </record>
              
    <record id="action_tw_working_calendar_override" model="ir.actions.act_window">
        <field name="name">工作日覆蓋設定</field>
        <field name="res_model">tw.working.calendar.override</field>
        <field name="view_mode">list,form</field>
    </record>

    <!-- 雙薪資單生成嚮導 -->
    <record id="action_tw_dual_payslip_wizard" model="ir.actions.act_window">
        <field name="name">生成雙薪資單</field>
        <field name="res_model">tw.dual.payslip.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
              
    <record id="action_tw_payslip_report" model="ir.actions.act_window">
        <field name="name">薪資單分析報表</field>
        <field name="res_model">tw.payslip.report</field>
        <field name="view_mode">graph,pivot,list</field>
    </record>

    <!-- 搜尋視圖 - 已移除，功能整合到 tw.hr.attendance -->

</odoo>