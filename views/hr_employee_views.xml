<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr_employee_view_form" model="ir.ui.view">
        <field name="name">hr.employee.view.form.inherit.l10n.tw.hr.payroll</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr_payroll.payroll_hr_employee_view_form"/>
        <field name="arch" type="xml">
            <group name="payroll_group" position="after">
                <group name="tw_payroll_info" string="台灣薪資資訊" invisible="company_country_code != 'TW'">
                    <field name="national_id"/>
                    <field name="tw_employee_id"/>
                    <field name="labor_insurance_no"/>
                    <field name="health_insurance_no"/>
                    <field name="hourly_rate" readonly="1" invisible="not contract_id"/>
                </group>
            </group>
        </field>
    </record>

    <!-- 員工表單視圖 - 加班統計擴展 -->
    <record id="hr_employee_view_form_overtime_stats" model="ir.ui.view">
        <field name="name">hr.employee.view.form.overtime.stats</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_form"/>
        <field name="arch" type="xml">
            <!-- 在按鈕區域添加加班統計按鈕 -->
            <xpath expr="//div[@name='button_box']" position="inside">
                <button name="action_view_overtime_records" type="object" class="oe_stat_button" icon="fa-clock-o">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_value">
                            <field name="current_month_overtime" widget="float_time"/>
                        </span>
                        <span class="o_stat_text">本月加班</span>
                    </div>
                </button>
                
                <button name="action_view_overtime_records" type="object" class="oe_stat_button" icon="fa-eye"
                        groups="l10n_tw_hr_payroll.group_hr_manager">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_value text-warning">
                            <field name="current_month_hidden_overtime" widget="float_time"/>
                        </span>
                        <span class="o_stat_text">隱藏加班</span>
                    </div>
                </button>

                <button name="action_create_pending_overtime_records" type="object" class="oe_stat_button"
                        icon="fa-plus-circle" invisible="pending_attendance_overtime == 0">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_value text-info">
                            <field name="pending_attendance_overtime"/>
                        </span>
                        <span class="o_stat_text">待處理加班</span>
                    </div>
                </button>

                <button name="action_view_overtime_attendance_integration" type="object" class="oe_stat_button"
                        icon="fa-link">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_text">加班整合</span>
                    </div>
                </button>
            </xpath>

            <!-- 添加加班統計頁面 -->
            <xpath expr="//notebook" position="inside">
                <page string="加班統計" name="overtime_stats">
                    <group>
                        <group string="本月統計">
                            <field name="current_month_overtime" widget="float_time"/>
                            <field name="current_month_displayed_overtime" widget="float_time"/>
                            <field name="current_month_hidden_overtime" widget="float_time"
                                   groups="l10n_tw_hr_payroll.group_hr_manager"/>
                        </group>
                        <group string="整合統計">
                            <field name="auto_overtime_count"/>
                            <field name="manual_overtime_count"/>
                            <field name="pending_attendance_overtime"/>
                        </group>
                    </group>

                    <group string="快速操作">
                        <button name="action_create_pending_overtime_records" string="創建待處理加班記錄"
                                type="object" class="btn-primary" invisible="pending_attendance_overtime == 0"/>
                        <button name="action_view_overtime_attendance_integration" string="查看加班出勤整合"
                                type="object" class="btn-secondary"/>
                    </group>

                    <separator string="考勤記錄"/>
                    <p class="text-muted">
                        加班記錄已整合至台灣考勤記錄中，請前往「出勤管理」→「台灣考勤記錄」查看詳細資訊。
                    </p>
                </page>
            </xpath>
        </field>
    </record>

    <!-- 員工列表視圖 - 加班統計欄位 -->
    <record id="hr_employee_view_tree_overtime" model="ir.ui.view">
        <field name="name">hr.employee.view.tree.overtime</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_tree"/>
        <field name="arch" type="xml">
            <field name="department_id" position="after">
                <field name="current_month_overtime" widget="float_time" optional="hide"/>
                <field name="auto_overtime_count" optional="hide"/>
                <field name="manual_overtime_count" optional="hide"/>
                <field name="pending_attendance_overtime" optional="hide"/>
            </field>
        </field>
    </record>

    <!-- 員工搜索視圖 - 加班相關過濾器 -->
    <record id="hr_employee_view_search_overtime" model="ir.ui.view">
        <field name="name">hr.employee.view.search.overtime</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_filter"/>
        <field name="arch" type="xml">
            <filter name="inactive" position="after">
                <separator/>
                <filter name="has_overtime" string="有加班記錄"
                        domain="[('current_month_overtime', '>', 0)]"/>
                <filter name="has_pending_overtime" string="有待處理加班"
                        domain="[('pending_attendance_overtime', '>', 0)]"/>
                <filter name="has_hidden_overtime" string="有隱藏加班"
                        domain="[('current_month_hidden_overtime', '>', 0)]"
                        groups="l10n_tw_hr_payroll.group_hr_manager"/>
            </filter>
        </field>
    </record>

</odoo>
