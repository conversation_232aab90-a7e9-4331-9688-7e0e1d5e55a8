<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- report/payslip_report_templates.xml -->
    
    <!-- 台灣薪資單報表動作 -->
    <record id="action_report_tw_payslip" model="ir.actions.report">
        <field name="name">台灣薪資單</field>
        <field name="model">hr.payslip</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">l10n_tw_hr_payroll.report_tw_payslip_template</field>
        <field name="report_file">l10n_tw_hr_payroll.report_tw_payslip_template</field>
        <field name="binding_model_id" ref="hr_payroll.model_hr_payslip"/>
        <field name="binding_type">report</field>
        <field name="paperformat_id" ref="base.paperformat_euro"/>
    </record>

    <!-- 台灣薪資單模板 -->
    <template id="report_tw_payslip_template">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="payslip">
                <t t-call="web.external_layout">
                    <div class="page">
                        <!-- 薪資單標題 -->
                        <div class="row">
                            <div class="col-12 text-center">
                                <h2>
                                    <strong>薪資單</strong>
                                    <t t-if="payslip.payslip_type == 'full'" groups="l10n_tw_hr_payroll.group_hr_manager">
                                        <span class="badge badge-warning">完整版</span>
                                    </t>
                                    <t t-elif="payslip.payslip_type == 'standard'">
                                        <span class="badge badge-info">標準版</span>
                                    </t>
                                </h2>
                            </div>
                        </div>
                        
                        <!-- 員工資訊 -->
                        <div class="row mt-3">
                            <div class="col-6">
                                <table class="table table-sm table-borderless">
                                    <tr>
                                        <td><strong>員工姓名：</strong></td>
                                        <td><span t-field="payslip.employee_id.name"/></td>
                                    </tr>
                                    <tr>
                                        <td><strong>員工編號：</strong></td>
                                        <td><span t-field="payslip.employee_id.tw_employee_id"/></td>
                                    </tr>
                                    <tr>
                                        <td><strong>部門：</strong></td>
                                        <td><span t-field="payslip.employee_id.department_id.name"/></td>
                                    </tr>
                                    <tr>
                                        <td><strong>職位：</strong></td>
                                        <td><span t-field="payslip.employee_id.job_id.name"/></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-6">
                                <table class="table table-sm table-borderless">
                                    <tr>
                                        <td><strong>薪資期間：</strong></td>
                                        <td>
                                            <span t-field="payslip.date_from" t-options="{'widget': 'date'}"/> 
                                            至 
                                            <span t-field="payslip.date_to" t-options="{'widget': 'date'}"/>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>計算日期：</strong></td>
                                        <td><span t-field="payslip.date_generated" t-options="{'widget': 'date'}"/></td>
                                    </tr>
                                    <tr>
                                        <td><strong>薪資單編號：</strong></td>
                                        <td><span t-field="payslip.number"/></td>
                                    </tr>
                                    <tr t-if="payslip.payslip_type == 'full'" groups="l10n_tw_hr_payroll.group_hr_manager">
                                        <td><strong>配對薪資單：</strong></td>
                                        <td><span t-field="payslip.paired_payslip_id.number"/></td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <!-- 薪資明細 -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h4>薪資明細</h4>
                                <table class="table table-sm">
                                    <thead>
                                        <tr class="table-primary">
                                            <th>項目</th>
                                            <th class="text-right">數量</th>
                                            <th class="text-right">單價</th>
                                            <th class="text-right">金額</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- 基本薪資類 -->
                                        <t t-foreach="payslip.line_ids.filtered(lambda line: line.category_id.code == 'BASIC')" t-as="line">
                                            <tr>
                                                <td><span t-field="line.name"/></td>
                                                <td class="text-right">
                                                    <span t-field="line.quantity" t-options="{'widget': 'float', 'precision': 2}"/>
                                                </td>
                                                <td class="text-right">
                                                    <span t-field="line.rate" t-options="{'widget': 'monetary', 'display_currency': payslip.company_id.currency_id}"/>
                                                </td>
                                                <td class="text-right">
                                                    <span t-field="line.amount" t-options="{'widget': 'monetary', 'display_currency': payslip.company_id.currency_id}"/>
                                                </td>
                                            </tr>
                                        </t>
                                        
                                        <!-- 津貼類 -->
                                        <t t-foreach="payslip.line_ids.filtered(lambda line: line.category_id.code == 'ALW')" t-as="line">
                                            <tr>
                                                <td><span t-field="line.name"/></td>
                                                <td class="text-right">
                                                    <span t-field="line.quantity" t-options="{'widget': 'float', 'precision': 2}"/>
                                                </td>
                                                <td class="text-right">
                                                    <span t-field="line.rate" t-options="{'widget': 'monetary', 'display_currency': payslip.company_id.currency_id}"/>
                                                </td>
                                                <td class="text-right">
                                                    <span t-field="line.amount" t-options="{'widget': 'monetary', 'display_currency': payslip.company_id.currency_id}"/>
                                                </td>
                                            </tr>
                                        </t>
                                        
                                        <!-- 加班費類 -->
                                        <t t-foreach="payslip.line_ids.filtered(lambda line: line.category_id.code == 'OT')" t-as="line">
                                            <tr>
                                                <td>
                                                    <span t-field="line.name"/>
                                                    <t t-if="payslip.payslip_type == 'standard' and payslip.hidden_overtime_hours > 0">
                                                        <small class="text-muted">(部分時數未顯示)</small>
                                                    </t>
                                                </td>
                                                <td class="text-right">
                                                    <span t-field="line.quantity" t-options="{'widget': 'float_time'}"/>
                                                </td>
                                                <td class="text-right">
                                                    <span t-field="line.rate" t-options="{'widget': 'monetary', 'display_currency': payslip.company_id.currency_id}"/>
                                                </td>
                                                <td class="text-right">
                                                    <span t-field="line.amount" t-options="{'widget': 'monetary', 'display_currency': payslip.company_id.currency_id}"/>
                                                </td>
                                            </tr>
                                        </t>
                                        
                                        <!-- 應發小計 -->
                                        <tr class="table-secondary">
                                            <td colspan="3"><strong>應發小計</strong></td>
                                            <td class="text-right">
                                                <strong>
                                                    <t t-set="gross_amount" t-value="sum(payslip.line_ids.filtered(lambda l: l.category_id.code in ['BASIC', 'ALW', 'OT']).mapped('amount'))"/>
                                                    <span t-esc="gross_amount" t-options="{'widget': 'monetary', 'display_currency': payslip.company_id.currency_id}"/>
                                                </strong>
                                            </td>
                                        </tr>
                                        
                                        <!-- 扣除項目 -->
                                        <t t-foreach="payslip.line_ids.filtered(lambda line: line.category_id.code == 'DED')" t-as="line">
                                            <tr>
                                                <td><span t-field="line.name"/></td>
                                                <td class="text-right">
                                                    <span t-field="line.quantity" t-options="{'widget': 'float', 'precision': 2}"/>
                                                </td>
                                                <td class="text-right">
                                                    <span t-field="line.rate" t-options="{'widget': 'monetary', 'display_currency': payslip.company_id.currency_id}"/>
                                                </td>
                                                <td class="text-right">
                                                    <span t-field="line.amount" t-options="{'widget': 'monetary', 'display_currency': payslip.company_id.currency_id}"/>
                                                </td>
                                            </tr>
                                        </t>
                                        
                                        <!-- 實發總額 -->
                                        <tr class="table-success">
                                            <td colspan="3"><strong>實發總額</strong></td>
                                            <td class="text-right">
                                                <strong>
                                                    <span t-field="payslip.net_wage" t-options="{'widget': 'monetary', 'display_currency': payslip.company_id.currency_id}"/>
                                                </strong>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 加班時數統計 (僅限有權限用戶) -->
                        <div class="row mt-4" t-if="payslip.displayed_overtime_hours > 0">
                            <div class="col-12">
                                <h5>加班時數統計</h5>
                                <table class="table table-sm table-bordered">
                                    <tr>
                                        <td><strong>顯示加班時數</strong></td>
                                        <td class="text-right">
                                            <span t-field="payslip.displayed_overtime_hours" t-options="{'widget': 'float_time'}"/>
                                        </td>
                                    </tr>
                                    <tr t-if="payslip.show_full_overtime" groups="l10n_tw_hr_payroll.group_hr_manager">
                                        <td><strong>實際加班時數</strong></td>
                                        <td class="text-right">
                                            <span t-field="payslip.total_overtime_hours" t-options="{'widget': 'float_time'}"/>
                                        </td>
                                    </tr>
                                    <tr t-if="payslip.show_full_overtime and payslip.hidden_overtime_hours > 0" groups="l10n_tw_hr_payroll.group_hr_manager">
                                        <td><strong>隱藏加班時數</strong></td>
                                        <td class="text-right text-warning">
                                            <span t-field="payslip.hidden_overtime_hours" t-options="{'widget': 'float_time'}"/>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <!-- 備註說明 -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5>備註說明</h5>
                                <ul class="list-unstyled small text-muted">
                                    <li>• 本薪資單依照中華民國勞動基準法計算</li>
                                    <li>• 勞保費、健保費、勞退提繳依照相關法規辦理</li>
                                    <li>• 加班費依據勞基法第24條計算：平日前2小時1.34倍，超過2小時1.67倍</li>
                                    <li t-if="payslip.payslip_type == 'standard'">• 本薪資單為標準版，部分加班時數可能未完全顯示</li>
                                    <li t-if="payslip.payslip_type == 'full'" groups="l10n_tw_hr_payroll.group_hr_manager">• 本薪資單為完整版，包含所有實際加班時數</li>
                                    <li>• 如有疑問請洽人事部門</li>
                                </ul>
                            </div>
                        </div>

                        <!-- 簽名區 -->
                        <div class="row mt-5">
                            <div class="col-4 text-center">
                                <div style="border-top: 1px solid #000; padding-top: 5px;">
                                    員工簽名
                                </div>
                            </div>
                            <div class="col-4 text-center">
                                <div style="border-top: 1px solid #000; padding-top: 5px;">
                                    人事主管
                                </div>
                            </div>
                            <div class="col-4 text-center">
                                <div style="border-top: 1px solid #000; padding-top: 5px;">
                                    會計主管
                                </div>
                            </div>
                        </div>

                        <!-- 頁尾 -->
                        <div class="row mt-3">
                            <div class="col-12 text-center small text-muted">
                                <p>此薪資單由系統自動產生，列印日期：<span t-esc="context_timestamp(datetime.datetime.now()).strftime('%Y-%m-%d %H:%M:%S')"/></p>
                                <p t-if="payslip.payslip_type == 'full'" groups="l10n_tw_hr_payroll.group_hr_manager">
                                    <strong class="text-warning">機密文件 - 僅授權人員可查閱</strong>
                                </p>
                            </div>
                        </div>
                    </div>
                </t>
            </t>
        </t>
    </template>

    <!-- 薪資單比較報表 -->
    <record id="action_report_tw_payslip_comparison" model="ir.actions.report">
        <field name="name">薪資單比較分析</field>
        <field name="model">hr.payslip</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">l10n_tw_hr_payroll.report_tw_payslip_comparison_template</field>
        <field name="report_file">l10n_tw_hr_payroll.report_tw_payslip_comparison_template</field>
        <field name="groups_id" eval="[(4, ref('l10n_tw_hr_payroll.group_hr_manager'))]"/>
        <field name="paperformat_id" ref="base.paperformat_euro"/>
    </record>

    <!-- 薪資單比較模板 -->
    <template id="report_tw_payslip_comparison_template">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="standard_payslip">
                <t t-set="full_payslip" t-value="standard_payslip.paired_payslip_id"/>
                <t t-if="full_payslip">
                    <t t-call="web.external_layout">
                        <div class="page">
                            <!-- 報表標題 -->
                            <div class="row">
                                <div class="col-12 text-center">
                                    <h2><strong>薪資單比較分析</strong></h2>
                                    <h4 class="text-muted">機密文件</h4>
                                </div>
                            </div>
                            
                            <!-- 員工資訊 -->
                            <div class="row mt-3">
                                <div class="col-12">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>員工姓名：</strong><span t-field="standard_payslip.employee_id.name"/></td>
                                            <td><strong>部門：</strong><span t-field="standard_payslip.employee_id.department_id.name"/></td>
                                            <td><strong>薪資期間：</strong>
                                                <span t-field="standard_payslip.date_from" t-options="{'widget': 'date'}"/> 
                                                至 
                                                <span t-field="standard_payslip.date_to" t-options="{'widget': 'date'}"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <!-- 比較表格 -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <table class="table table-bordered">
                                        <thead class="table-primary">
                                            <tr>
                                                <th>項目</th>
                                                <th class="text-center">標準版薪資單</th>
                                                <th class="text-center">完整版薪資單</th>
                                                <th class="text-center">差異</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- 加班時數比較 -->
                                            <tr>
                                                <td><strong>加班時數</strong></td>
                                                <td class="text-right">
                                                    <span t-field="standard_payslip.displayed_overtime_hours" t-options="{'widget': 'float_time'}"/>
                                                </td>
                                                <td class="text-right">
                                                    <span t-field="full_payslip.total_overtime_hours" t-options="{'widget': 'float_time'}"/>
                                                </td>
                                                <td class="text-right text-warning">
                                                    <span t-field="standard_payslip.hidden_overtime_hours" t-options="{'widget': 'float_time'}"/>
                                                </td>
                                            </tr>
                                            
                                            <!-- 加班費比較 -->
                                            <tr>
                                                <td><strong>加班費</strong></td>
                                                <td class="text-right">
                                                    <span t-field="standard_payslip.standard_overtime_amount" t-options="{'widget': 'monetary', 'display_currency': standard_payslip.company_id.currency_id}"/>
                                                </td>
                                                <td class="text-right">
                                                    <span t-field="full_payslip.full_overtime_amount" t-options="{'widget': 'monetary', 'display_currency': full_payslip.company_id.currency_id}"/>
                                                </td>
                                                <td class="text-right text-warning">
                                                    <span t-field="standard_payslip.hidden_overtime_amount" t-options="{'widget': 'monetary', 'display_currency': standard_payslip.company_id.currency_id}"/>
                                                </td>
                                            </tr>
                                            
                                            <!-- 總薪資比較 -->
                                            <tr class="table-info">
                                                <td><strong>總薪資</strong></td>
                                                <td class="text-right">
                                                    <strong>
                                                        <span t-field="standard_payslip.net_wage" t-options="{'widget': 'monetary', 'display_currency': standard_payslip.company_id.currency_id}"/>
                                                    </strong>
                                                </td>
                                                <td class="text-right">
                                                    <strong>
                                                        <span t-field="full_payslip.net_wage" t-options="{'widget': 'monetary', 'display_currency': full_payslip.company_id.currency_id}"/>
                                                    </strong>
                                                </td>
                                                <td class="text-right">
                                                    <strong class="text-warning">
                                                        <t t-set="wage_diff" t-value="full_payslip.net_wage - standard_payslip.net_wage"/>
                                                        <span t-esc="wage_diff" t-options="{'widget': 'monetary', 'display_currency': standard_payslip.company_id.currency_id}"/>
                                                    </strong>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- 統計分析 -->
                            <div class="row mt-4">
                                <div class="col-6">
                                    <h5>隱藏比例分析</h5>
                                    <table class="table table-sm">
                                        <tr>
                                            <td>隱藏加班時數比例：</td>
                                            <td class="text-right">
                                                <t t-set="hide_ratio" t-value="round((standard_payslip.hidden_overtime_hours / full_payslip.total_overtime_hours * 100) if full_payslip.total_overtime_hours else 0, 1)"/>
                                                <span t-esc="hide_ratio"/>%
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>隱藏加班費比例：</td>
                                            <td class="text-right">
                                                <t t-set="amount_hide_ratio" t-value="round((standard_payslip.hidden_overtime_amount / full_payslip.full_overtime_amount * 100) if full_payslip.full_overtime_amount else 0, 1)"/>
                                                <span t-esc="amount_hide_ratio"/>%
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-6">
                                    <h5>月度限制資訊</h5>
                                    <table class="table table-sm">
                                        <tr>
                                            <td>本月累計加班：</td>
                                            <td class="text-right">
                                                <span t-field="standard_payslip.employee_id.current_month_overtime" t-options="{'widget': 'float_time'}"/>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>本月隱藏加班：</td>
                                            <td class="text-right">
                                                <span t-field="standard_payslip.employee_id.current_month_hidden_overtime" t-options="{'widget': 'float_time'}"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <!-- 明細比較 -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <h5>明細項目比較</h5>
                                    <table class="table table-sm table-bordered">
                                        <thead>
                                            <tr class="table-secondary">
                                                <th>項目</th>
                                                <th class="text-right">標準版</th>
                                                <th class="text-right">完整版</th>
                                                <th class="text-right">差異</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <t t-foreach="standard_payslip.line_ids" t-as="std_line">
                                                <t t-set="full_line" t-value="full_payslip.line_ids.filtered(lambda l: l.code == std_line.code)"/>
                                                <t t-if="full_line and (std_line.amount != full_line.amount)">
                                                    <tr>
                                                        <td><span t-field="std_line.name"/></td>
                                                        <td class="text-right">
                                                            <span t-field="std_line.amount" t-options="{'widget': 'monetary', 'display_currency': standard_payslip.company_id.currency_id}"/>
                                                        </td>
                                                        <td class="text-right">
                                                            <span t-field="full_line.amount" t-options="{'widget': 'monetary', 'display_currency': full_payslip.company_id.currency_id}"/>
                                                        </td>
                                                        <td class="text-right">
                                                            <t t-set="line_diff" t-value="full_line.amount - std_line.amount"/>
                                                            <span t-esc="line_diff" t-options="{'widget': 'monetary', 'display_currency': standard_payslip.company_id.currency_id}"/>
                                                        </td>
                                                    </tr>
                                                </t>
                                            </t>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- 頁尾 -->
                            <div class="row mt-5">
                                <div class="col-12 text-center">
                                    <p class="small text-muted">
                                        本報表為機密文件，僅供授權人員查閱<br/>
                                        產生時間：<span t-esc="context_timestamp(datetime.datetime.now()).strftime('%Y-%m-%d %H:%M:%S')"/>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </t>
                </t>
            </t>
        </t>
    </template>

    <!-- 月度加班統計報表 - 已移除，功能整合到 tw.hr.attendance -->

</odoo>