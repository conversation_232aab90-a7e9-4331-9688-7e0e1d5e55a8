# wizards/makeup_day_wizard.py

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from datetime import datetime, date

class MakeupDayWizard(models.TransientModel):
    """補班日批量設定嚮導"""
    _name = 'makeup.day.wizard'
    _description = '補班日批量設定嚮導'

    # 基本設定
    calendar_id = fields.Many2one('resource.calendar', '工作行事曆', required=True)
    year = fields.Integer('年度', required=True, default=lambda self: datetime.now().year)
    auto_create_from_holidays = fields.Boolean('從假日自動創建補班日', default=True,
                                               help='根據現有假日資料自動建議補班日期')
    
    # 補班日期明細
    makeup_date_ids = fields.One2many('makeup.day.line', 'wizard_id', '補班日期')

    @api.model
    def default_get(self, fields):
        """設定預設值"""
        result = super().default_get(fields)
        
        # 尋找預設的工作行事曆
        calendar = self.env['resource.calendar'].search([
            ('company_id', '=', self.env.company.id)
        ], limit=1)
        if calendar:
            result['calendar_id'] = calendar.id
            
        return result

    @api.onchange('calendar_id', 'year', 'auto_create_from_holidays')
    def _onchange_auto_create_settings(self):
        """當設定變更時自動建議補班日"""
        if self.auto_create_from_holidays and self.calendar_id and self.year:
            self._suggest_makeup_dates()

    def _suggest_makeup_dates(self):
        """根據假日自動建議補班日"""
        if not self.calendar_id or not self.year:
            return
        
        # 查找該年度的假日
        holidays = self.env['tw.holiday.import'].search([
            ('calendar_id', '=', self.calendar_id.id),
            ('year', '=', self.year),
            ('state', '=', 'done')
        ])
        
        suggested_lines = []
        for holiday in holidays:
            # 這裡可以根據業務邏輯建議補班日
            # 例如：連假前後的週六可能需要補班
            if holiday.makeup_day_column and holiday.has_makeup_days:
                # 如果假日記錄中有補班日資訊，使用該資訊
                pass
        
        # 清空現有的補班日期並設定建議的日期
        self.makeup_date_ids = [(5, 0, 0)]  # 清空
        if suggested_lines:
            self.makeup_date_ids = [(0, 0, line) for line in suggested_lines]

    def action_create_makeup_days(self):
        """創建補班日"""
        self.ensure_one()
        
        if not self.makeup_date_ids:
            raise UserError(_('請至少添加一個補班日期'))
        
        # 驗證日期
        for line in self.makeup_date_ids:
            if not line.date:
                raise UserError(_('所有補班日期都必須填寫'))
            if line.date.year != self.year:
                raise UserError(_('補班日期必須在指定年度內'))
        
        # 創建或更新工作行事曆的補班日設定
        created_records = []
        
        for line in self.makeup_date_ids:
            # 檢查是否已存在該日期的設定
            existing = self.env['tw.working.calendar.override'].search([
                ('calendar_id', '=', self.calendar_id.id),
                ('date', '=', line.date),
                ('override_type', '=', 'makeup')
            ])
            
            if existing:
                # 更新現有記錄
                existing.write({
                    'reason': line.reason,
                    'working_hours': line.working_hours,
                })
                created_records.append(existing.id)
            else:
                # 創建新記錄
                override = self.env['tw.working.calendar.override'].create({
                    'calendar_id': self.calendar_id.id,
                    'date': line.date,
                    'override_type': 'makeup',
                    'reason': line.reason,
                    'working_hours': line.working_hours,
                    'is_working_day': True,
                })
                created_records.append(override.id)
        
        # 返回創建的記錄列表
        return {
            'type': 'ir.actions.act_window',
            'name': _('補班日設定'),
            'res_model': 'tw.working.calendar.override',
            'view_mode': 'list,form',
            'domain': [('id', 'in', created_records)],
            'target': 'current',
            'context': {
                'default_calendar_id': self.calendar_id.id,
                'default_override_type': 'makeup',
            }
        }


class MakeupDayLine(models.TransientModel):
    """補班日期明細"""
    _name = 'makeup.day.line'
    _description = '補班日期明細'
    _order = 'date'

    wizard_id = fields.Many2one('makeup.day.wizard', '嚮導', required=True, ondelete='cascade')
    date = fields.Date('補班日期', required=True)
    reason = fields.Char('補班原因', required=True, default='補班日')
    working_hours = fields.Float('工作時數', default=8.0, required=True,
                                help='該補班日的工作時數')

    @api.constrains('working_hours')
    def _check_working_hours(self):
        """檢查工作時數"""
        for record in self:
            if record.working_hours <= 0:
                raise ValidationError(_('工作時數必須大於0'))
            if record.working_hours > 24:
                raise ValidationError(_('工作時數不能超過24小時'))

    @api.constrains('date')
    def _check_date(self):
        """檢查日期"""
        for record in self:
            if record.date and record.date < date.today():
                # 允許設定過去的日期，但給出警告
                _logger.warning(
                    _('補班日期 %s 已是過去日期') % record.date
                )
