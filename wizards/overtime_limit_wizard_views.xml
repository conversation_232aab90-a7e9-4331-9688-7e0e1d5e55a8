<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- wizard/overtime_limit_wizard_views.xml -->
    
    <record id="view_overtime_limit_wizard_form" model="ir.ui.view">
        <field name="name">overtime.limit.wizard.form</field>
        <field name="model">overtime.limit.wizard</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="設定名稱..." required="1"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group string="時數限制設定">
                            <field name="monthly_limit" widget="float_time"/>
                            <field name="display_limit" widget="float_time"/>
                            <field name="priority"/>
                        </group>
                        <group string="生效時間">
                            <field name="date_from"/>
                            <field name="date_to"/>
                        </group>
                    </group>
                    
                    <group string="適用範圍">
                        <field name="apply_scope" widget="radio"/>
                        <field name="department_ids" widget="many2many_tags"
                               invisible="apply_scope != 'departments'" required="apply_scope == 'departments'"/>
                        <field name="employee_ids" widget="many2many_tags"
                               invisible="apply_scope != 'employees'" required="apply_scope == 'employees'"/>
                    </group>
                    
                    <div class="alert alert-info" role="alert">
                        <strong>說明：</strong>
                        <ul>
                            <li>每月加班時數上限：員工每月最多可申請的加班時數</li>
                            <li>顯示時數上限：在標準薪資單中顯示的最大加班時數，超過部分將被隱藏</li>
                            <li>優先級：數字越高優先級越高，特定設定會覆蓋一般設定</li>
                        </ul>
                    </div>
                </sheet>
                
                <footer>
                    <button name="action_create_limit" string="創建設定" type="object" class="btn-primary"/>
                    <button string="取消" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="action_overtime_limit_wizard" model="ir.actions.act_window">
        <field name="name">加班時數限制設定</field>
        <field name="res_model">overtime.limit.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

</odoo>
