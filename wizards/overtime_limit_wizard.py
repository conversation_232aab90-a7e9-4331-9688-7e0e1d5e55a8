# wizard/overtime_limit_wizard.py

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from datetime import datetime

class OvertimeLimitWizard(models.TransientModel):
    """加班時數限制設定嚮導"""
    _name = 'overtime.limit.wizard'
    _description = '加班時數限制設定嚮導'

    name = fields.Char('設定名稱', required=True)
    
    # 限制設定
    monthly_limit = fields.Float('每月加班時數上限', default=46.0, required=True)
    display_limit = fields.Float('顯示時數上限', default=30.0, required=True)
    
    # 適用範圍
    apply_scope = fields.Selection([
        ('all', '全公司'),
        ('departments', '指定部門'),
        ('employees', '指定員工')
    ], string='適用範圍', default='all', required=True)
    
    department_ids = fields.Many2many('hr.department', string='適用部門')
    employee_ids = fields.Many2many('hr.employee', string='適用員工')
    
    # 生效時間
    date_from = fields.Date('開始日期', default=fields.Date.today)
    date_to = fields.Date('結束日期')
    
    # 優先級
    priority = fields.Integer('優先級', default=10)

    @api.onchange('apply_scope')
    def _onchange_apply_scope(self):
        """切換適用範圍時清空相關欄位"""
        if self.apply_scope != 'departments':
            self.department_ids = False
        if self.apply_scope != 'employees':
            self.employee_ids = False

    @api.constrains('monthly_limit', 'display_limit')
    def _check_limits(self):
        for record in self:
            if record.monthly_limit <= 0:
                raise ValidationError(_('每月加班時數上限必須大於0'))
            if record.display_limit < 0:
                raise ValidationError(_('顯示時數上限不能為負數'))
            if record.display_limit > record.monthly_limit:
                raise ValidationError(_('顯示時數上限不能超過每月加班時數上限'))

    def action_create_limit(self):
        """創建加班限制設定"""
        self.ensure_one()
        
        vals = {
            'name': self.name,
            'monthly_limit': self.monthly_limit,
            'display_limit': self.display_limit,
            'priority': self.priority,
            'date_from': self.date_from,
            'date_to': self.date_to,
            'apply_to_all': self.apply_scope == 'all',
        }
        
        if self.apply_scope == 'departments':
            vals['department_ids'] = [(6, 0, self.department_ids.ids)]
        elif self.apply_scope == 'employees':
            vals['employee_ids'] = [(6, 0, self.employee_ids.ids)]
        
        limit_record = self.env['tw.overtime.limit'].create(vals)
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('加班時數限制'),
            'res_model': 'tw.overtime.limit',
            'res_id': limit_record.id,
            'view_mode': 'form',
            'target': 'current',
        }
