# wizard/holiday_import_wizard.py

from odoo import models, fields, api, _
from odoo.exceptions import UserError
import base64
import io
import pandas as pd
from datetime import datetime

class HolidayImportWizard(models.TransientModel):
    """假日匯入嚮導"""
    _name = 'holiday.import.wizard'
    _description = '假日匯入嚮導'

    name = fields.Char('匯入名稱', required=True, default=lambda self: f"假日匯入 - {datetime.now().strftime('%Y-%m-%d')}")
    import_file = fields.Binary('匯入檔案', required=True)
    filename = fields.Char('檔案名稱')
    
    # 行事曆設定
    calendar_id = fields.Many2one('resource.calendar', '工作行事曆', required=True)
    year = fields.Integer('年度', required=True, default=lambda self: datetime.now().year)
    
    # 匯入選項
    override_existing = fields.Boolean('覆蓋現有假日', default=False)
    create_makeup_days = fields.Bo<PERSON>an('自動創建補班日', default=True)
    
    # 檔案格式設定
    date_column = fields.Char('日期欄位名稱', default='date', required=True)
    name_column = fields.Char('假日名稱欄位', default='name', required=True)
    makeup_column = fields.Char('補班日欄位名稱', default='makeup_date')
    
    # 預覽資料
    preview_data = fields.Text('預覽資料', readonly=True)
    show_preview = fields.Boolean('顯示預覽', default=False)

    @api.model
    def default_get(self, fields):
        """設定預設值"""
        result = super().default_get(fields)
        
        # 尋找預設的工作行事曆
        calendar = self.env['resource.calendar'].search([
            ('company_id', '=', self.env.company.id)
        ], limit=1)
        if calendar:
            result['calendar_id'] = calendar.id
            
        return result

    def action_preview_data(self):
        """預覽匯入資料"""
        self.ensure_one()
        
        if not self.import_file:
            raise UserError(_('請上傳匯入檔案'))
        
        try:
            # 讀取檔案
            file_content = base64.b64decode(self.import_file)
            
            if self.filename.endswith('.csv'):
                df = pd.read_csv(io.BytesIO(file_content), encoding='utf-8')
            elif self.filename.endswith(('.xlsx', '.xls')):
                df = pd.read_excel(io.BytesIO(file_content))
            else:
                raise UserError(_('不支援的檔案格式'))
            
            # 檢查必要欄位
            required_columns = [self.date_column, self.name_column]
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise UserError(_('缺少必要欄位: %s') % ', '.join(missing_columns))
            
            # 生成預覽資料
            preview_rows = []
            for i, row in df.head(10).iterrows():
                date_val = row[self.date_column]
                name_val = row[self.name_column]
                makeup_val = row.get(self.makeup_column, '') if self.makeup_column in df.columns else ''
                
                preview_rows.append(f"第{i+1}行: {date_val} - {name_val}" + (f" (補班: {makeup_val})" if makeup_val else ""))
            
            self.preview_data = '\n'.join(preview_rows)
            self.show_preview = True
            
            return {
                'type': 'ir.actions.act_window',
                'res_model': 'holiday.import.wizard',
                'res_id': self.id,
                'view_mode': 'form',
                'target': 'new',
                'context': self.env.context,
            }
            
        except Exception as e:
            raise UserError(_('檔案預覽失敗: %s') % str(e))

    def action_import_holidays(self):
        """執行假日匯入"""
        self.ensure_one()
        
        # 創建匯入記錄
        import_record = self.env['tw.holiday.import'].create({
            'name': self.name,
            'import_file': self.import_file,
            'filename': self.filename,
            'calendar_id': self.calendar_id.id,
            'year': self.year,
            'override_existing': self.override_existing,
            'has_makeup_days': self.create_makeup_days,
            'makeup_day_column': self.makeup_column,
        })
        
        # 執行匯入
        import_record.action_import_holidays()
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('匯入結果'),
            'res_model': 'tw.holiday.import',
            'res_id': import_record.id,
            'view_mode': 'form',
            'target': 'current',
        }


# wizard/dual_payslip_wizard.py

class DualPayslipWizard(models.TransientModel):
    """雙薪資單生成嚮導"""
    _name = 'tw.dual.payslip.wizard'
    _description = '雙薪資單生成嚮導'

    name = fields.Char('批次名稱', compute='_compute_name', store=True)
    date_from = fields.Date('開始日期', required=True)
    date_to = fields.Date('結束日期', required=True)
    
    # 員工選擇
    employee_selection = fields.Selection([
        ('all', '所有員工'),
        ('departments', '指定部門'),
        ('employees', '指定員工')
    ], string='員工選擇', default='all', required=True)
    
    department_ids = fields.Many2many('hr.department', string='部門')
    employee_ids = fields.Many2many('hr.employee', string='員工')
    
    # 生成選項
    auto_confirm = fields.Boolean('自動確認薪資單', default=False)
    include_draft_overtime = fields.Boolean('包含草稿狀態的加班記錄', default=False)
    
    # 統計資訊
    estimated_count = fields.Integer('預估生成數量', compute='_compute_estimated_count')

    @api.depends('date_from', 'date_to')
    def _compute_name(self):
        for record in self:
            if record.date_from and record.date_to:
                record.name = f"雙薪資單批次 - {record.date_from} 至 {record.date_to}"
            else:
                record.name = "雙薪資單批次"

    @api.depends('employee_selection', 'department_ids', 'employee_ids')
    def _compute_estimated_count(self):
        for record in self:
            employees = record._get_target_employees()
            record.estimated_count = len(employees) * 2  # 每個員工生成兩份薪資單

    def _get_target_employees(self):
        """取得目標員工"""
        if self.employee_selection == 'all':
            return self.env['hr.employee'].search([
                ('company_id', '=', self.env.company.id),
                ('contract_ids', '!=', False)
            ])
        elif self.employee_selection == 'departments':
            return self.env['hr.employee'].search([
                ('department_id', 'in', self.department_ids.ids),
                ('contract_ids', '!=', False)
            ])
        else:
            return self.employee_ids.filtered(lambda e: e.contract_ids)

    @api.onchange('employee_selection')
    def _onchange_employee_selection(self):
        """切換員工選擇時清空相關欄位"""
        if self.employee_selection != 'departments':
            self.department_ids = False
        if self.employee_selection != 'employees':
            self.employee_ids = False

    def action_generate_payslips(self):
        """生成雙薪資單"""
        self.ensure_one()
        
        employees = self._get_target_employees()
        
        if not employees:
            raise UserError(_('沒有找到符合條件的員工'))
        
        # 創建薪資單批次
        payslip_run = self.env['hr.payslip.run'].create({
            'name': self.name,
            'date_start': self.date_from,
            'date_end': self.date_to,
            'batch_type': 'dual',
        })
        
        # 生成雙薪資單
        created_payslips = []
        
        for employee in employees:
            # 檢查是否已有該期間的薪資單
            existing = self.env['hr.payslip'].search([
                ('employee_id', '=', employee.id),
                ('date_from', '=', self.date_from),
                ('date_to', '=', self.date_to),
                ('state', '!=', 'cancel')
            ])
            
            if existing:
                continue
            
            # 創建標準薪資單
            standard_vals = {
                'name': f"{employee.name} - {self.date_from} 至 {self.date_to} (標準版)",
                'employee_id': employee.id,
                'date_from': self.date_from,
                'date_to': self.date_to,
                'contract_id': employee.contract_id.id,
                'payslip_type': 'standard',
                'payslip_run_id': payslip_run.id,
            }
            
            standard_payslip = self.env['hr.payslip'].create(standard_vals)
            
            # 創建完整版薪資單
            full_vals = standard_vals.copy()
            full_vals.update({
                'name': f"{employee.name} - {self.date_from} 至 {self.date_to} (完整版)",
                'payslip_type': 'full',
                'paired_payslip_id': standard_payslip.id,
                'is_primary_payslip': False,
            })
            
            full_payslip = self.env['hr.payslip'].create(full_vals)
            
            # 更新標準薪資單的配對關係
            standard_payslip.paired_payslip_id = full_payslip.id
            
            created_payslips.extend([standard_payslip.id, full_payslip.id])
            
            # 自動確認
            if self.auto_confirm:
                standard_payslip.compute_sheet()
                full_payslip.compute_sheet()
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('薪資單批次'),
            'res_model': 'hr.payslip.run',
            'res_id': payslip_run.id,
            'view_mode': 'form',
            'target': 'current',
        }
