id,name,model_id/id,group_id/id,perm_read,perm_write,perm_create,perm_unlink
access_tw_holiday_import_employee,tw.holiday.import.employee,model_tw_holiday_import,base.group_user,1,0,0,0
access_tw_holiday_import_hr_officer,tw.holiday.import.hr.officer,model_tw_holiday_import,group_hr_officer,1,1,1,1
access_tw_holiday_import_hr_manager,tw.holiday.import.hr.manager,model_tw_holiday_import,group_hr_manager,1,1,1,1
access_tw_working_calendar_override_employee,tw.working.calendar.override.employee,model_tw_working_calendar_override,base.group_user,1,0,0,0
access_tw_working_calendar_override_hr_officer,tw.working.calendar.override.hr.officer,model_tw_working_calendar_override,group_hr_officer,1,1,1,1
access_tw_working_calendar_override_hr_manager,tw.working.calendar.override.hr.manager,model_tw_working_calendar_override,group_hr_manager,1,1,1,1
access_tw_overtime_limit_employee,tw.overtime.limit.employee,model_tw_overtime_limit,base.group_user,1,0,0,0
access_tw_overtime_limit_hr_officer,tw.overtime.limit.hr.officer,model_tw_overtime_limit,group_hr_officer,1,0,0,0
access_tw_overtime_limit_hr_manager,tw.overtime.limit.hr.manager,model_tw_overtime_limit,group_hr_manager,1,1,1,1
access_tw_overtime_limit_system_admin,tw.overtime.limit.system.admin,model_tw_overtime_limit,group_system_admin,1,1,1,1
access_tw_payslip_report_employee,tw.payslip.report.employee,model_tw_payslip_report,base.group_user,1,0,0,0
access_tw_payslip_report_hr_officer,tw.payslip.report.hr.officer,model_tw_payslip_report,group_hr_officer,1,0,0,0
access_tw_payslip_report_hr_manager,tw.payslip.report.hr.manager,model_tw_payslip_report,group_hr_manager,1,0,0,0
access_tw_payslip_report_system_admin,tw.payslip.report.system.admin,model_tw_payslip_report,group_system_admin,1,1,1,1
access_tw_dual_payslip_wizard_hr_manager,tw.dual.payslip.wizard.hr.manager,model_tw_dual_payslip_wizard,group_hr_manager,1,1,1,1
access_tw_dual_payslip_wizard_system_admin,tw.dual.payslip.wizard.system.admin,model_tw_dual_payslip_wizard,group_system_admin,1,1,1,1
access_makeup_day_wizard_hr_officer,makeup.day.wizard.hr.officer,model_makeup_day_wizard,group_hr_officer,1,1,1,1
access_makeup_day_wizard_hr_manager,makeup.day.wizard.hr.manager,model_makeup_day_wizard,group_hr_manager,1,1,1,1
access_makeup_day_line_hr_officer,makeup.day.line.hr.officer,model_makeup_day_line,group_hr_officer,1,1,1,1
access_makeup_day_line_hr_manager,makeup.day.line.hr.manager,model_makeup_day_line,group_hr_manager,1,1,1,1
access_holiday_import_wizard_hr_officer,holiday.import.wizard.hr.officer,model_holiday_import_wizard,group_hr_officer,1,1,1,1
access_holiday_import_wizard_hr_manager,holiday.import.wizard.hr.manager,model_holiday_import_wizard,group_hr_manager,1,1,1,1
access_overtime_limit_wizard_hr_manager,overtime.limit.wizard.hr.manager,model_overtime_limit_wizard,group_hr_manager,1,1,1,1
access_overtime_limit_wizard_system_admin,overtime.limit.wizard.system.admin,model_overtime_limit_wizard,group_system_admin,1,1,1,1
access_hr_attendance_employee,hr.attendance.employee,hr_attendance.model_hr_attendance,base.group_user,1,0,0,0
access_hr_attendance_hr_officer,hr.attendance.hr.officer,hr_attendance.model_hr_attendance,group_hr_officer,1,1,1,1
access_hr_attendance_hr_manager,hr.attendance.hr.manager,hr_attendance.model_hr_attendance,group_hr_manager,1,1,1,1
access_hr_attendance_system_admin,hr.attendance.system.admin,hr_attendance.model_hr_attendance,group_system_admin,1,1,1,1
access_tw_hr_attendance_employee,tw.hr.attendance.employee,model_tw_hr_attendance,base.group_user,1,0,0,0
access_tw_hr_attendance_hr_officer,tw.hr.attendance.hr.officer,model_tw_hr_attendance,group_hr_officer,1,1,1,1
access_tw_hr_attendance_hr_manager,tw.hr.attendance.hr.manager,model_tw_hr_attendance,group_hr_manager,1,1,1,1
access_tw_hr_attendance_system_admin,tw.hr.attendance.system.admin,model_tw_hr_attendance,group_system_admin,1,1,1,1
access_tw_sync_management_hr_manager,tw.sync.management.hr.manager,model_tw_sync_management,group_hr_manager,1,1,1,1
access_tw_sync_management_system_admin,tw.sync.management.system.admin,model_tw_sync_management,group_system_admin,1,1,1,1