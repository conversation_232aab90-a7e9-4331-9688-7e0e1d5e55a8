<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- security/tw_payroll_groups.xml -->
    
    <!-- 台灣薪酬管理安全組 -->
    <record id="category_tw_payroll" model="ir.module.category">
        <field name="name">台灣薪酬管理</field>
        <field name="description">台灣薪酬本地化功能的權限管理</field>
        <field name="sequence">16</field>
    </record>

    <!-- HR 人事專員 -->
    <record id="group_hr_officer" model="res.groups">
        <field name="name">HR 人事專員</field>
        <field name="category_id" ref="category_tw_payroll"/>
        <field name="implied_ids" eval="[(4, ref('base.group_user')), (4, ref('hr_payroll.group_hr_payroll_user'))]"/>
        <field name="comment">HR 人事專員：可查看所有考勤記錄、管理假日補班日、查看顯示加班時數、查看標準薪資單</field>
    </record>

    <!-- HR 主管 -->
    <record id="group_hr_manager" model="res.groups">
        <field name="name">HR 主管</field>
        <field name="category_id" ref="category_tw_payroll"/>
        <field name="implied_ids" eval="[(4, ref('group_hr_officer')), (4, ref('hr_payroll.group_hr_payroll_manager'))]"/>
        <field name="comment">HR 主管：完整權限，可查看隱藏加班時數、管理所有薪資單、管理加班限制</field>
    </record>

    <!-- 系統管理員 -->
    <record id="group_system_admin" model="res.groups">
        <field name="name">系統管理員</field>
        <field name="category_id" ref="category_tw_payroll"/>
        <field name="implied_ids" eval="[(4, ref('group_hr_manager')), (4, ref('base.group_system'))]"/>
        <field name="users" eval="[(4, ref('base.user_admin'))]"/>
        <field name="comment">系統管理員：最高權限，可管理所有功能和設定</field>
    </record>

</odoo>