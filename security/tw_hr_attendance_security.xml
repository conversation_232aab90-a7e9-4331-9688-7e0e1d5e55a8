<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- 台灣考勤記錄的權限控制規則 - 參考官方 hr_attendance 實現 -->
        
        <!-- 多公司規則 -->
        <record id="tw_hr_attendance_rule_employee_company" model="ir.rule">
            <field name="name">台灣考勤記錄：多公司規則</field>
            <field name="model_id" ref="model_tw_hr_attendance"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|',('employee_id.company_id','=',False),('employee_id.company_id', 'in', company_ids)]</field>
        </record>

        <!-- 暫時註解掉 ir.rule，改用模型層面的權限控制 -->
        <!--
        <record id="tw_hr_attendance_rule_admin_manager" model="ir.rule">
            <field name="name">台灣考勤記錄：管理員完整權限</field>
            <field name="model_id" ref="model_tw_hr_attendance"/>
            <field name="domain_force">[(1,'=',1)]</field>
            <field name="groups" eval="[(4, ref('group_hr_manager')), (4, ref('group_system_admin'))]"/>
        </record>

        <record id="tw_hr_attendance_rule_hr_officer" model="ir.rule">
            <field name="name">台灣考勤記錄：HR專員權限</field>
            <field name="model_id" ref="model_tw_hr_attendance"/>
            <field name="domain_force">[(1,'=',1)]</field>
            <field name="groups" eval="[(4, ref('group_hr_officer'))]"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_unlink" eval="1"/>
            <field name="perm_read" eval="1"/>
        </record>

        <record id="tw_hr_attendance_rule_employee_own" model="ir.rule">
            <field name="name">台灣考勤記錄：員工只能看到自己的記錄</field>
            <field name="model_id" ref="model_tw_hr_attendance"/>
            <field name="domain_force">[('employee_id.user_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_unlink" eval="0"/>
            <field name="perm_read" eval="1"/>
        </record>
        -->

    </data>
</odoo>