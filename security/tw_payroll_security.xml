<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- security/tw_payroll_security.xml -->
    
    <!-- 考勤記錄權限控制 -->
    <record id="hr_attendance_employee_own_rule" model="ir.rule">
        <field name="name">考勤記錄: 一般用戶只能查看自己的記錄</field>
        <field name="model_id" ref="hr_attendance.model_hr_attendance"/>
        <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
        <field name="domain_force">[('employee_id.user_id', '=', user.id)]</field>
    </record>

    <record id="hr_attendance_hr_all_rule" model="ir.rule">
        <field name="name">考勤記錄: HR 可查看所有記錄</field>
        <field name="model_id" ref="hr_attendance.model_hr_attendance"/>
        <field name="groups" eval="[(4, ref('group_hr_officer'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
        <field name="domain_force">[(1, '=', 1)]</field>
    </record>

    <!-- 加班記錄權限控制 - 已移除，功能整合到 tw.hr.attendance -->

    <!-- 薪資單權限控制 -->
    <record id="hr_payslip_employee_rule" model="ir.rule">
        <field name="name">薪資單: 一般用戶只能查看自己的薪資單</field>
        <field name="model_id" ref="hr_payroll.model_hr_payslip"/>
        <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
        <field name="domain_force">[('employee_id.user_id', '=', user.id)]</field>
    </record>

    <!-- HR 專員只能查看標準薪資單 -->
    <record id="hr_payslip_standard_only_rule" model="ir.rule">
        <field name="name">薪資單: HR 專員只能查看標準版</field>
        <field name="model_id" ref="hr_payroll.model_hr_payslip"/>
        <field name="groups" eval="[(4, ref('group_hr_officer'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
        <field name="domain_force">[('payslip_type', 'in', ['standard', False])]</field>
    </record>

    <!-- HR 主管可查看所有薪資單 -->
    <record id="hr_payslip_full_access_rule" model="ir.rule">
        <field name="name">薪資單: HR 主管可查看所有薪資單</field>
        <field name="model_id" ref="hr_payroll.model_hr_payslip"/>
        <field name="groups" eval="[(4, ref('group_hr_manager'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
        <field name="domain_force">[(1, '=', 1)]</field>
    </record>

    <!-- 隱藏加班時數控制 - 已移除，功能整合到 tw.hr.attendance -->

    <record id="tw_working_calendar_override_company_rule" model="ir.rule">
        <field name="name">工作行事曆覆蓋: 公司範圍</field>
        <field name="model_id" ref="model_tw_working_calendar_override"/>
        <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        <field name="domain_force">[('calendar_id.company_id', 'in', company_ids)]</field>
    </record>

    <record id="tw_holiday_import_company_rule" model="ir.rule">
        <field name="name">假日匯入: 公司範圍</field>
        <field name="model_id" ref="model_tw_holiday_import"/>
        <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        <field name="domain_force">[('calendar_id.company_id', 'in', company_ids)]</field>
    </record>

    <record id="tw_overtime_limit_company_rule" model="ir.rule">
        <field name="name">加班時數限制: 公司範圍</field>
        <field name="model_id" ref="model_tw_overtime_limit"/>
        <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        <field name="domain_force">[('company_id', 'in', company_ids)]</field>
    </record>

    <!-- 台灣考勤記錄權限控制 -->
    <record id="tw_hr_attendance_employee_own_rule" model="ir.rule">
        <field name="name">台灣考勤記錄: 一般用戶只能查看自己的記錄</field>
        <field name="model_id" ref="model_tw_hr_attendance"/>
        <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
        <field name="domain_force">[('employee_id.user_id', '=', user.id)]</field>
    </record>

    <record id="tw_hr_attendance_hr_officer_rule" model="ir.rule">
        <field name="name">台灣考勤記錄: HR 專員可查看所有記錄</field>
        <field name="model_id" ref="model_tw_hr_attendance"/>
        <field name="groups" eval="[(4, ref('group_hr_officer'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
        <field name="domain_force">[(1, '=', 1)]</field>
    </record>

    <record id="tw_hr_attendance_hr_manager_rule" model="ir.rule">
        <field name="name">台灣考勤記錄: HR 主管完整權限</field>
        <field name="model_id" ref="model_tw_hr_attendance"/>
        <field name="groups" eval="[(4, ref('group_hr_manager'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
        <field name="domain_force">[(1, '=', 1)]</field>
    </record>

    <record id="tw_hr_attendance_system_admin_rule" model="ir.rule">
        <field name="name">台灣考勤記錄: 系統管理員完整權限</field>
        <field name="model_id" ref="model_tw_hr_attendance"/>
        <field name="groups" eval="[(4, ref('group_system_admin'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
        <field name="domain_force">[(1, '=', 1)]</field>
    </record>

    <!-- 台灣考勤記錄公司範圍限制 -->
    <record id="tw_hr_attendance_company_rule" model="ir.rule">
        <field name="name">台灣考勤記錄: 公司範圍</field>
        <field name="model_id" ref="model_tw_hr_attendance"/>
        <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        <field name="global" eval="True"/>
        <field name="domain_force">[('employee_id.company_id', 'in', company_ids)]</field>
    </record>

</odoo>