# models/working_calendar.py

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from datetime import datetime, date, timedelta
import logging

_logger = logging.getLogger(__name__)

class TwWorkingCalendarOverride(models.Model):
    """台灣工作行事曆覆蓋設定"""
    _name = 'tw.working.calendar.override'
    _description = '工作行事曆覆蓋設定'
    _order = 'date desc'

    name = fields.Char('名稱', compute='_compute_name', store=True)
    calendar_id = fields.Many2one('resource.calendar', '工作行事曆', required=True)
    date = fields.Date('日期', required=True)
    is_working_day = fields.Bo<PERSON>an('是否為工作日', default=True)
    reason = fields.Char('原因', required=True)
    
    # 覆蓋類型
    override_type = fields.Selection([
        ('holiday', '假日'),
        ('makeup', '補班'),
        ('special', '特殊')
    ], string='覆蓋類型', default='special', required=True)
    
    # 工作時間設定
    working_hours = fields.Float('工作時數', default=8.0)
    start_time = fields.Float('開始時間', default=9.0)  # 9:00
    lunch_start = fields.Float('午餐開始', default=12.0)  # 12:00
    lunch_end = fields.Float('午餐結束', default=13.0)   # 13:00
    end_time = fields.Float('結束時間', default=18.0)    # 18:00
    
    # 狀態
    state = fields.Selection([
        ('draft', '草稿'),
        ('confirmed', '確認'),
        ('cancelled', '取消')
    ], default='draft', string='狀態')
    
    # 影響的員工
    employee_ids = fields.Many2many('hr.employee', string='影響員工')
    apply_to_all = fields.Boolean('套用至全體員工', default=True)

    @api.depends('date', 'reason', 'is_working_day')
    def _compute_name(self):
        for record in self:
            working_status = '工作日' if record.is_working_day else '休息日'
            record.name = f"{record.date} - {working_status} - {record.reason}"

    @api.constrains('date', 'calendar_id')
    def _check_duplicate_date(self):
        """檢查日期重複"""
        for record in self:
            existing = self.search([
                ('calendar_id', '=', record.calendar_id.id),
                ('date', '=', record.date),
                ('id', '!=', record.id)
            ])
            if existing:
                raise ValidationError(_('該日期已存在覆蓋設定'))

    @api.constrains('working_hours')
    def _check_working_hours(self):
        """檢查工作時數"""
        for record in self:
            if record.is_working_day and not (0 < record.working_hours <= 12):
                raise ValidationError(_('工作時數必須在 0 到 12 小時之間'))

    def action_confirm(self):
        """確認覆蓋設定"""
        for record in self:
            if record.state != 'draft':
                continue
            
            # 創建實際的工作時間覆蓋
            record._create_calendar_attendances()
            record.state = 'confirmed'

    def action_cancel(self):
        """取消覆蓋設定"""
        for record in self:
            record._remove_calendar_attendances()
            record.state = 'cancelled'

    def _create_calendar_attendances(self):
        """創建行事曆出勤記錄"""
        self.ensure_one()
        
        if not self.is_working_day:
            # 如果設定為非工作日，創建假日記錄
            self.env['resource.calendar.leaves'].create({
                'name': self.reason,
                'calendar_id': self.calendar_id.id,
                'date_from': self.date,
                'date_to': self.date,
                'resource_id': False,
                'time_type': 'leave'
            })
            return

        # 為工作日創建出勤時間
        weekday = str(self.date.weekday())  # 0=Monday, 6=Sunday
        
        # 上午時段
        morning_attendance = {
            'name': f"補班 - {self.reason} (上午)",
            'calendar_id': self.calendar_id.id,
            'dayofweek': weekday,
            'hour_from': self.start_time,
            'hour_to': self.lunch_start,
            'date_from': self.date,
            'date_to': self.date,
            'display_type': 'line_section',
            'tw_override_id': self.id,
        }
        
        # 下午時段
        afternoon_attendance = {
            'name': f"補班 - {self.reason} (下午)",
            'calendar_id': self.calendar_id.id,
            'dayofweek': weekday,
            'hour_from': self.lunch_end,
            'hour_to': self.end_time,
            'date_from': self.date,
            'date_to': self.date,
            'display_type': 'line_section',
            'tw_override_id': self.id,
        }
        
        # 如果是週六補班，需要特別處理
        if self.date.weekday() == 5:  # Saturday
            self._create_saturday_special_attendance()
        else:
            self.env['resource.calendar.attendance'].create([morning_attendance, afternoon_attendance])

    def _create_saturday_special_attendance(self):
        """為週六補班創建特殊出勤記錄"""
        self.ensure_one()
        
        # 創建週六的工作時間記錄
        attendance_vals = {
            'name': f"週六補班 - {self.reason}",
            'calendar_id': self.calendar_id.id,
            'dayofweek': '5',  # Saturday
            'hour_from': self.start_time,
            'hour_to': self.end_time,
            'date_from': self.date,
            'date_to': self.date,
            'tw_override_id': self.id,
        }
        
        self.env['resource.calendar.attendance'].create(attendance_vals)

    def _remove_calendar_attendances(self):
        """移除行事曆出勤記錄"""
        self.ensure_one()
        
        # 移除相關的出勤記錄
        attendances = self.env['resource.calendar.attendance'].search([
            ('tw_override_id', '=', self.id)
        ])
        attendances.unlink()
        
        # 移除相關的假日記錄
        leaves = self.env['resource.calendar.leaves'].search([
            ('calendar_id', '=', self.calendar_id.id),
            ('date_from', '=', self.date),
            ('name', 'ilike', self.reason)
        ])
        leaves.unlink()

    @api.model
    def get_working_day_overrides(self, date_from, date_to, calendar_id=None):
        """取得指定期間的工作日覆蓋"""
        domain = [
            ('date', '>=', date_from),
            ('date', '<=', date_to),
            ('state', '=', 'confirmed')
        ]
        
        if calendar_id:
            domain.append(('calendar_id', '=', calendar_id))
        
        return self.search(domain)


class ResourceCalendarAttendance(models.Model):
    """擴展資源行事曆出勤"""
    _inherit = 'resource.calendar.attendance'
    
    # 台灣特有欄位
    tw_override_id = fields.Many2one('tw.working.calendar.override', '覆蓋設定')
    is_makeup_day = fields.Boolean('補班日', compute='_compute_is_makeup_day', store=True)

    @api.depends('tw_override_id')
    def _compute_is_makeup_day(self):
        for record in self:
            record.is_makeup_day = bool(record.tw_override_id)


class ResourceCalendar(models.Model):
    """擴展資源行事曆"""
    _inherit = 'resource.calendar'
    
    # 台灣特有設定
    is_tw_calendar = fields.Boolean('台灣行事曆', default=False)
    tw_override_ids = fields.One2many('tw.working.calendar.override', 'calendar_id', '覆蓋設定')
    
    # 預設工作時間設定
    default_daily_hours = fields.Float('預設每日工作時數', default=8.0)
    default_weekly_hours = fields.Float('預設每週工作時數', default=40.0)
    
    # 移除加班費率設定欄位 - 交由 salary rule 處理
    
    def get_effective_working_hours(self, date):
        """取得指定日期的有效工作時數"""
        self.ensure_one()
        
        # 檢查是否有覆蓋設定
        override = self.env['tw.working.calendar.override'].search([
            ('calendar_id', '=', self.id),
            ('date', '=', date),
            ('state', '=', 'confirmed')
        ], limit=1)
        
        if override:
            return override.working_hours if override.is_working_day else 0.0
        
        # 使用標準行事曆邏輯
        weekday = date.weekday()
        attendances = self.attendance_ids.filtered(lambda a: int(a.dayofweek) == weekday)
        
        if not attendances:
            return 0.0
        
        total_hours = 0.0
        for attendance in attendances:
            total_hours += attendance.hour_to - attendance.hour_from
        
        return total_hours

    def is_working_day(self, date):
        """檢查指定日期是否為工作日"""
        self.ensure_one()
        
        # 檢查覆蓋設定
        override = self.env['tw.working.calendar.override'].search([
            ('calendar_id', '=', self.id),
            ('date', '=', date),
            ('state', '=', 'confirmed')
        ], limit=1)
        
        if override:
            return override.is_working_day
        
        # 檢查是否為假日
        holiday = self.env['resource.calendar.leaves'].search([
            ('calendar_id', '=', self.id),
            ('date_from', '<=', date),
            ('date_to', '>=', date)
        ], limit=1)
        
        if holiday:
            return False
        
        # 檢查是否為正常工作日
        weekday = date.weekday()
        attendances = self.attendance_ids.filtered(lambda a: int(a.dayofweek) == weekday)
        
        return bool(attendances)

    @api.model
    def create_saturday_makeup_schedule(self, year, makeup_dates):
        """為指定年度創建週六補班行程表"""
        calendar = self.search([('is_tw_calendar', '=', True)], limit=1)
        if not calendar:
            raise UserError(_('找不到台灣行事曆設定'))
        
        created_overrides = []
        
        for makeup_info in makeup_dates:
            if isinstance(makeup_info, dict):
                makeup_date = makeup_info.get('date')
                reason = makeup_info.get('reason', '補班日')
            else:
                makeup_date = makeup_info
                reason = '補班日'
            
            # 檢查是否為週六
            if makeup_date.weekday() != 5:
                continue
            
            # 創建覆蓋設定
            override = self.env['tw.working.calendar.override'].create({
                'calendar_id': calendar.id,
                'date': makeup_date,
                'is_working_day': True,
                'reason': reason,
                'working_hours': 8.0,
            })
            
            override.action_confirm()
            created_overrides.append(override)
        
        return created_overrides


# HrEmployee 擴展已移至 hr_employee_extension.py 以避免重複繼承