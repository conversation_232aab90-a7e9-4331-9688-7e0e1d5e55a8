# models/payslip_extension.py

from odoo import models, fields, api, tools, _
from odoo.exceptions import UserError, ValidationError
import logging

_logger = logging.getLogger(__name__)

class HrPayslip(models.Model):
    """擴展薪資單模型 - 雙薪資單功能"""
    _inherit = 'hr.payslip'
    
    # 薪資單類型
    payslip_type = fields.Selection([
        ('standard', '標準薪資單'),
        ('full', '完整薪資單'),
        ('hidden', '隱藏版薪資單')
    ], string='薪資單類型', default='standard')
    
    # 關聯薪資單
    paired_payslip_id = fields.Many2one('hr.payslip', '配對薪資單')
    is_primary_payslip = fields.Boolean('主要薪資單', default=True)
    
    # 顯示控制
    show_hidden_overtime = fields.Bo<PERSON>an('顯示隱藏加班', compute='_compute_display_settings')
    can_access_full_version = fields.<PERSON><PERSON>an('可存取完整版本', compute='_compute_display_settings')
    
    # 基礎時數欄位 - 儲存在資料庫中
    displayed_overtime_hours = fields.Float('顯示加班時數', default=0.0)
    hidden_overtime_hours = fields.Float('隱藏加班時數', default=0.0)
    total_overtime_hours = fields.Float('總加班時數', compute='_compute_total_overtime', store=True)
    
    # 基本薪資
    basic_wage = fields.Monetary('基本薪資', related='contract_id.wage', store=True)
    
    # 移除加班費計算欄位 - 交由 salary rule 處理

    @api.depends('displayed_overtime_hours', 'hidden_overtime_hours')
    def _compute_total_overtime(self):
        """計算總加班時數 - 簡化邏輯避免循環依賴"""
        for payslip in self:
            # 直接從欄位計算，避免循環查詢
            payslip.total_overtime_hours = (payslip.displayed_overtime_hours or 0.0) + (payslip.hidden_overtime_hours or 0.0)

    @api.depends('employee_id')
    def _compute_display_settings(self):
        for payslip in self:
            # 檢查使用者是否有完整查看權限
            payslip.can_access_full_version = (
                self.env.user.has_group('l10n_tw_hr_payroll.group_hr_manager')
            )
            
            # 根據薪資單類型和使用者權限決定是否顯示隱藏加班
            if payslip.payslip_type == 'full' and payslip.can_access_full_version:
                payslip.show_hidden_overtime = True
            else:
                payslip.show_hidden_overtime = False

    # 移除加班費率計算方法 - 交由 salary rule 處理

    def action_generate_dual_payslips(self):
        """生成雙薪資單"""
        self.ensure_one()
        
        if self.state != 'draft':
            raise UserError(_('只能在草稿狀態生成雙薪資單'))
        
        if self.paired_payslip_id:
            raise UserError(_('該薪資單已有配對薪資單'))
        
        # 創建完整版薪資單
        full_payslip_vals = self._prepare_paired_payslip_vals('full')
        full_payslip = self.create(full_payslip_vals)
        
        # 更新當前薪資單為標準版
        self.write({
            'payslip_type': 'standard',
            'paired_payslip_id': full_payslip.id,
            'is_primary_payslip': True,
        })
        
        # 更新完整版薪資單的配對關係
        full_payslip.write({
            'paired_payslip_id': self.id,
            'is_primary_payslip': False,
        })
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('雙薪資單'),
            'view_mode': 'list,form',
            'res_model': 'hr.payslip',
            'domain': [('id', 'in', [self.id, full_payslip.id])],
            'context': {'create': False},
        }

    def _prepare_paired_payslip_vals(self, payslip_type):
        """準備配對薪資單的值"""
        self.ensure_one()
        
        vals = {
            'name': f"{self.name} ({'完整版' if payslip_type == 'full' else '標準版'})",
            'employee_id': self.employee_id.id,
            'date_from': self.date_from,
            'date_to': self.date_to,
            'contract_id': self.contract_id.id,
            'struct_id': self.struct_id.id,
            'payslip_type': payslip_type,
            'company_id': self.company_id.id,
            # 複製時數資料
            'displayed_overtime_hours': self.displayed_overtime_hours,
            'hidden_overtime_hours': self.hidden_overtime_hours,
        }
        
        return vals

    def compute_sheet(self):
        """重寫計算薪資單方法，根據類型使用不同的加班時數"""
        # 先更新加班時數
        for payslip in self:
            if payslip.employee_id and payslip.date_from and payslip.date_to:
                _logger.info(f"開始計算薪資單 {payslip.id} - 員工: {payslip.employee_id.name}")
                payslip._update_overtime_from_records()
        
        # 調用原生計算方法，讓 salary rule 處理加班費計算
        result = super().compute_sheet()
        
        return result

    # 移除加班費調整方法 - 交由 salary rule 處理

    def _update_overtime_from_records(self):
        """從考勤記錄更新薪資單的加班時數 - 使用新的 tw.hr.attendance 模型"""
        self.ensure_one()
        
        if not self.employee_id or not self.date_from or not self.date_to:
            return
        
        # 取得期間內的考勤記錄 - 包含已提交和已核准的記錄
        attendance_records = self.env['tw.hr.attendance'].search([
            ('employee_id', '=', self.employee_id.id),
            ('check_in_date', '>=', self.date_from),
            ('check_in_date', '<=', self.date_to),
            ('state', 'in', ['confirmed', 'validated']),
            ('overtime_hours', '>', 0)
        ])
        
        _logger.info(f"薪資單 {self.id} 找到 {len(attendance_records)} 筆有加班的考勤記錄")
        
        if attendance_records:
            # 重新計算顯示和隱藏時數，考慮薪資單類型和用戶權限
            total_actual = sum(attendance_records.mapped('overtime_hours'))
            
            # 檢查用戶權限和薪資單類型
            has_full_access = (
                self.env.user.has_group('l10n_tw_hr_payroll.group_hr_manager')
            )
            
            if self.payslip_type == 'full' and has_full_access:
                # 完整版薪資單且有權限：顯示全部時數
                total_displayed = total_actual
                total_hidden = 0.0
            elif self.payslip_type == 'standard' or not has_full_access:
                # 標準版薪資單或無完整權限：應用顯示限制
                total_displayed = sum(attendance_records.mapped('displayed_overtime_hours'))
                total_hidden = sum(attendance_records.mapped('hidden_overtime_hours'))
            else:
                # 其他情況：使用考勤記錄的原始計算
                total_displayed = sum(attendance_records.mapped('displayed_overtime_hours'))
                total_hidden = sum(attendance_records.mapped('hidden_overtime_hours'))
            
            # 記錄詳細資訊
            for record in attendance_records:
                _logger.info(f"考勤記錄 {record.id}: 實際={record.overtime_hours}, 顯示={record.displayed_overtime_hours}, 隱藏={record.hidden_overtime_hours}")
            
            # 直接更新欄位值，避免觸發 compute 方法
            self.env.cr.execute("""
                UPDATE hr_payslip
                SET displayed_overtime_hours = %s, hidden_overtime_hours = %s
                WHERE id = %s
            """, (total_displayed, total_hidden, self.id))
            
            # 清除快取以確保下次讀取時獲得正確值
            self.invalidate_model(['displayed_overtime_hours', 'hidden_overtime_hours', 'total_overtime_hours'])
            
            _logger.info(f"薪資單 {self.id} ({self.payslip_type}) 加班時數已更新: 實際={total_actual}, 顯示={total_displayed}, 隱藏={total_hidden}")
        else:
            _logger.warning(f"薪資單 {self.id} 在期間 {self.date_from} 至 {self.date_to} 沒有找到加班記錄")

    # 移除加班費計算方法 - 完全交由 salary rule 處理

    @api.model
    def create_batch_dual_payslips(self, employee_ids, date_from, date_to):
        """批量創建雙薪資單"""
        created_payslips = []
        
        for employee_id in employee_ids:
            employee = self.env['hr.employee'].browse(employee_id)
            
            # 檢查是否已有該期間的薪資單
            existing_payslip = self.search([
                ('employee_id', '=', employee_id),
                ('date_from', '=', date_from),
                ('date_to', '=', date_to),
                ('state', '!=', 'cancel')
            ], limit=1)
            
            if existing_payslip:
                continue
            
            # 創建標準薪資單
            standard_payslip_vals = {
                'name': f"{employee.name} - {date_from} 至 {date_to} (標準版)",
                'employee_id': employee_id,
                'date_from': date_from,
                'date_to': date_to,
                'contract_id': employee.contract_id.id if employee.contract_id else False,
                'payslip_type': 'standard',
            }
            
            standard_payslip = self.create(standard_payslip_vals)
            
            # 自動生成配對的完整薪資單
            standard_payslip.action_generate_dual_payslips()
            
            created_payslips.extend([standard_payslip.id, standard_payslip.paired_payslip_id.id])
        
        return created_payslips

    def action_view_paired_payslip(self):
        """查看配對薪資單"""
        self.ensure_one()
        
        if not self.paired_payslip_id:
            raise UserError(_('該薪資單沒有配對薪資單'))
        
        # 檢查權限
        if (self.paired_payslip_id.payslip_type == 'full' and
            not self.env.user.has_group('l10n_tw_hr_payroll.group_hr_manager')):
            raise UserError(_('您沒有權限查看完整版薪資單'))
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('配對薪資單'),
            'view_mode': 'form',
            'res_model': 'hr.payslip',
            'res_id': self.paired_payslip_id.id,
            'target': 'current',
        }

    def unlink(self):
        """刪除薪資單時同時處理配對薪資單"""
        paired_payslips = self.mapped('paired_payslip_id').filtered(lambda p: p)
        
        result = super().unlink()
        
        # 同時刪除配對薪資單
        if paired_payslips:
            paired_payslips.unlink()
        
        return result


class HrPayslipRun(models.Model):
    """擴展薪資單批次"""
    _inherit = 'hr.payslip.run'
    
    # 批次類型
    batch_type = fields.Selection([
        ('standard', '標準批次'),
        ('dual', '雙薪資單批次')
    ], string='批次類型', default='standard')
    
    # 統計資訊
    standard_payslip_count = fields.Integer('標準薪資單數量', compute='_compute_payslip_counts')
    full_payslip_count = fields.Integer('完整薪資單數量', compute='_compute_payslip_counts')
    
    @api.depends('slip_ids.payslip_type')
    def _compute_payslip_counts(self):
        for batch in self:
            batch.standard_payslip_count = len(batch.slip_ids.filtered(lambda s: s.payslip_type == 'standard'))
            batch.full_payslip_count = len(batch.slip_ids.filtered(lambda s: s.payslip_type == 'full'))

    def action_generate_dual_payslips(self):
        """為整個批次生成雙薪資單"""
        self.ensure_one()
        
        if self.state != 'draft':
            raise UserError(_('只能在草稿狀態生成雙薪資單'))
        
        employees_without_payslips = self.env['hr.employee'].search([
            ('company_id', '=', self.company_id.id),
            ('contract_ids', '!=', False)
        ]) - self.slip_ids.mapped('employee_id')
        
        created_payslips = self.env['hr.payslip'].create_batch_dual_payslips(
            employees_without_payslips.ids,
            self.date_start,
            self.date_end
        )
        
        # 將新創建的薪資單加入批次
        self.env['hr.payslip'].browse(created_payslips).write({'payslip_run_id': self.id})
        
        self.batch_type = 'dual'
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'type': 'success',
                'message': _('成功生成 %d 份雙薪資單') % (len(created_payslips) // 2),
                'sticky': False,
            }
        }


class HrPayslipLine(models.Model):
    """擴展薪資單明細"""
    _inherit = 'hr.payslip.line'
    
    # 是否為隱藏項目
    is_hidden_item = fields.Boolean('隱藏項目', default=False)
    visible_to_employee = fields.Boolean('員工可見', default=True)
    
    # 原始數值 (用於完整版薪資單)
    original_quantity = fields.Float('原始數量')
    original_amount = fields.Monetary('原始金額')


class TwPayslipReport(models.Model):
    """台灣薪資單報表 - 使用 Odoo 18 最佳實踐"""
    _name = 'tw.payslip.report'
    _description = '台灣薪資單報表'
    _auto = False
    _rec_name = 'employee_id'
    
    employee_id = fields.Many2one('hr.employee', '員工')
    department_id = fields.Many2one('hr.department', '部門')
    date_from = fields.Date('開始日期')
    date_to = fields.Date('結束日期')
    company_id = fields.Many2one('res.company', '公司')
    currency_id = fields.Many2one('res.currency', '貨幣', related='company_id.currency_id', readonly=True)
    
    # 薪資資訊
    basic_wage = fields.Monetary('基本薪資', currency_field='currency_id')
    # 移除加班費相關欄位 - 交由 salary rule 處理
    
    # 時數資訊
    displayed_overtime_hours = fields.Float('顯示加班時數')
    hidden_overtime_hours = fields.Float('隱藏加班時數')
    total_overtime_hours = fields.Float('總加班時數')
    
    # 分析欄位 - 只保留時數比例分析
    overtime_hide_ratio = fields.Float('加班隱藏比例', aggregator='avg')

    @property
    def _table_query(self):
        """使用 _table_query 屬性動態生成 SQL 視圖 - Odoo 18 最佳實踐
        
        這個方法會在每次查詢時動態生成 SQL，確保所有欄位都已存在
        解決了模型初始化時機衝突的問題
        """
        return self._build_dynamic_query()
    
    def _build_dynamic_query(self):
        """構建動態 SQL 查詢，根據實際存在的欄位調整"""
        try:
            # 檢查必要的自定義欄位是否存在
            existing_columns = self._check_payslip_columns()
            
            # 根據存在的欄位動態構建查詢表達式
            field_expressions = self._build_field_expressions(existing_columns)
            
            # 構建完整的 SQL 查詢 - 移除加班費計算
            query = f"""
                SELECT
                    row_number() OVER (ORDER BY p.id) AS id,
                    p.employee_id,
                    e.department_id,
                    p.date_from,
                    p.date_to,
                    p.company_id,
                    {field_expressions['basic_wage']} AS basic_wage,
                    {field_expressions['displayed_hours']} AS displayed_overtime_hours,
                    {field_expressions['hidden_hours']} AS hidden_overtime_hours,
                    {field_expressions['total_hours']} AS total_overtime_hours,
                    CASE
                        WHEN {field_expressions['total_hours']} > 0
                        THEN ROUND(CAST(({field_expressions['hidden_hours']} / NULLIF({field_expressions['total_hours']}, 0) * 100) AS NUMERIC), 2)
                        ELSE 0
                    END AS overtime_hide_ratio
                FROM hr_payslip p
                LEFT JOIN hr_employee e ON p.employee_id = e.id
                WHERE {field_expressions['payslip_condition']} AND p.state = 'done'
                ORDER BY p.date_from DESC, e.name
            """
            
            _logger.debug("動態 SQL 查詢構建成功")
            return query
            
        except Exception as e:
            _logger.warning(f"構建動態查詢失敗，使用基本查詢: {e}")
            return self._get_fallback_query()
    
    def _check_payslip_columns(self):
        """檢查 hr_payslip 表中存在的自定義欄位"""
        try:
            self.env.cr.execute("""
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'hr_payslip'
                AND column_name IN (
                    'basic_wage', 'displayed_overtime_hours', 'hidden_overtime_hours',
                    'total_overtime_hours', 'payslip_type'
                )
            """)
            return {row[0] for row in self.env.cr.fetchall()}
        except Exception as e:
            _logger.warning(f"檢查欄位存在性失敗: {e}")
            return set()
    
    def _build_field_expressions(self, existing_columns):
        """根據存在的欄位構建 SQL 表達式"""
        return {
            'basic_wage': "COALESCE(p.basic_wage, 0)" if 'basic_wage' in existing_columns else "0",
            'displayed_hours': "COALESCE(p.displayed_overtime_hours, 0)" if 'displayed_overtime_hours' in existing_columns else "0",
            'hidden_hours': "COALESCE(p.hidden_overtime_hours, 0)" if 'hidden_overtime_hours' in existing_columns else "0",
            'total_hours': "COALESCE(p.total_overtime_hours, 0)" if 'total_overtime_hours' in existing_columns else "0",
            'payslip_condition': "p.payslip_type IN ('standard', 'full')" if 'payslip_type' in existing_columns else "TRUE"
        }
    
    def _get_fallback_query(self):
        """當欄位檢查失敗時使用的基本查詢"""
        return """
            SELECT
                row_number() OVER (ORDER BY p.id) AS id,
                p.employee_id,
                e.department_id,
                p.date_from,
                p.date_to,
                p.company_id,
                0 AS basic_wage,
                0 AS displayed_overtime_hours,
                0 AS hidden_overtime_hours,
                0 AS total_overtime_hours,
                0 AS overtime_hide_ratio
            FROM hr_payslip p
            LEFT JOIN hr_employee e ON p.employee_id = e.id
            WHERE p.state = 'done'
            ORDER BY p.date_from DESC, e.name
        """



# Wizard for generating dual payslips
class TwDualPayslipWizard(models.TransientModel):
    """雙薪資單生成嚮導"""
    _name = 'tw.dual.payslip.wizard'
    _description = '雙薪資單生成嚮導'
    
    date_from = fields.Date('開始日期', required=True)
    date_to = fields.Date('結束日期', required=True)
    employee_ids = fields.Many2many('hr.employee', string='員工')
    department_ids = fields.Many2many('hr.department', string='部門')
    all_employees = fields.Boolean('所有員工', default=True)
    
    def action_generate_payslips(self):
        """生成薪資單"""
        employee_ids = []
        
        if self.all_employees:
            employees = self.env['hr.employee'].search([
                ('company_id', '=', self.env.company.id),
                ('contract_ids', '!=', False)
            ])
            employee_ids = employees.ids
        else:
            if self.employee_ids:
                employee_ids.extend(self.employee_ids.ids)
            
            if self.department_ids:
                dept_employees = self.env['hr.employee'].search([
                    ('department_id', 'in', self.department_ids.ids),
                    ('contract_ids', '!=', False)
                ])
                employee_ids.extend(dept_employees.ids)
        
        if not employee_ids:
            raise UserError(_('沒有找到符合條件的員工'))
        
        # 創建薪資單批次
        payslip_run = self.env['hr.payslip.run'].create({
            'name': f"雙薪資單批次 - {self.date_from} 至 {self.date_to}",
            'date_start': self.date_from,
            'date_end': self.date_to,
            'batch_type': 'dual',
        })
        
        # 生成雙薪資單
        created_payslips = self.env['hr.payslip'].create_batch_dual_payslips(
            list(set(employee_ids)),
            self.date_from,
            self.date_to
        )
        
        # 將薪資單加入批次
        self.env['hr.payslip'].browse(created_payslips).write({'payslip_run_id': payslip_run.id})
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('薪資單批次'),
            'view_mode': 'form',
            'res_model': 'hr.payslip.run',
            'res_id': payslip_run.id,
            'target': 'current',
        }