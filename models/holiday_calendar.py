from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
import pandas as pd
import io
import base64
from datetime import datetime, date
import logging

_logger = logging.getLogger(__name__)

class TwHolidayImport(models.Model):
    """台灣假日匯入管理"""
    _name = 'tw.holiday.import'
    _description = '台灣假日匯入'
    _order = 'create_date desc'

    name = fields.Char('匯入名稱', required=True)
    import_file = fields.Binary('匯入檔案', required=True)
    filename = fields.Char('檔案名稱')
    state = fields.Selection([
        ('draft', '草稿'),
        ('processing', '處理中'),
        ('done', '完成'),
        ('error', '錯誤')
    ], default='draft', string='狀態')
    
    # 匯入結果
    total_records = fields.Integer('總記錄數', readonly=True)
    success_records = fields.Integer('成功記錄數', readonly=True)
    error_records = fields.Integer('錯誤記錄數', readonly=True)
    error_log = fields.Text('錯誤日誌', readonly=True)
    
    # 匯入設定
    calendar_id = fields.Many2one('resource.calendar', '工作行事曆', required=True)
    year = fields.Integer('年度', required=True, default=lambda self: datetime.now().year)
    override_existing = fields.Boolean('覆蓋現有假日', default=False)
    
    # 補班設定
    has_makeup_days = fields.Boolean('包含補班日', default=False)
    makeup_day_column = fields.Char('補班日欄位名稱', default='makeup_date')

    @api.model
    def default_get(self, fields):
        """設定預設值"""
        result = super().default_get(fields)
        # 尋找預設的工作行事曆
        calendar = self.env['resource.calendar'].search([('company_id', '=', self.env.company.id)], limit=1)
        if calendar:
            result['calendar_id'] = calendar.id
        return result

    def action_import_holidays(self):
        """執行假日匯入"""
        self.ensure_one()
        if not self.import_file:
            raise UserError(_('請上傳匯入檔案'))
        
        self.state = 'processing'
        
        try:
            # 解析檔案
            file_content = base64.b64decode(self.import_file)
            
            if self.filename.endswith('.csv'):
                df = pd.read_csv(io.BytesIO(file_content), encoding='utf-8')
            elif self.filename.endswith(('.xlsx', '.xls')):
                df = pd.read_excel(io.BytesIO(file_content))
            else:
                raise UserError(_('不支援的檔案格式，請使用 CSV 或 Excel 檔案'))
            
            self.total_records = len(df)
            
            # 處理資料
            self._process_holiday_data(df)
            
            self.state = 'done'
            
        except Exception as e:
            self.state = 'error'
            self.error_log = str(e)
            _logger.error(f"假日匯入錯誤: {e}")
            raise UserError(_('匯入失敗: %s') % str(e))

    def _process_holiday_data(self, df):
        """處理假日資料"""
        success_count = 0
        error_count = 0
        error_messages = []
        
        # 必要欄位檢查
        required_columns = ['date', 'name']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise UserError(_('缺少必要欄位: %s') % ', '.join(missing_columns))
        
        # 如果有補班日，檢查相關欄位
        if self.has_makeup_days and self.makeup_day_column not in df.columns:
            raise UserError(_('缺少補班日欄位: %s') % self.makeup_day_column)
        
        for index, row in df.iterrows():
            try:
                # 處理假日
                holiday_date = self._parse_date(row['date'])
                holiday_name = str(row['name'])
                
                # 檢查是否已存在
                existing_holiday = self.env['resource.calendar.leaves'].search([
                    ('calendar_id', '=', self.calendar_id.id),
                    ('date_from', '=', holiday_date),
                    ('date_to', '=', holiday_date)
                ])
                
                if existing_holiday and not self.override_existing:
                    error_messages.append(f"第 {index+1} 行: 假日 {holiday_name} ({holiday_date}) 已存在")
                    error_count += 1
                    continue
                
                # 創建或更新假日
                holiday_vals = {
                    'name': holiday_name,
                    'calendar_id': self.calendar_id.id,
                    'date_from': holiday_date,
                    'date_to': holiday_date,
                    'resource_id': False,  # 全公司假日
                    'time_type': 'leave'
                }
                
                if existing_holiday:
                    existing_holiday.write(holiday_vals)
                else:
                    self.env['resource.calendar.leaves'].create(holiday_vals)
                
                # 處理補班日
                if self.has_makeup_days and self.makeup_day_column in row and pd.notna(row[self.makeup_day_column]):
                    makeup_date = self._parse_date(row[self.makeup_day_column])
                    self._process_makeup_day(makeup_date, holiday_name)
                
                success_count += 1
                
            except Exception as e:
                error_messages.append(f"第 {index+1} 行: {str(e)}")
                error_count += 1
        
        self.success_records = success_count
        self.error_records = error_count
        
        if error_messages:
            self.error_log = '\n'.join(error_messages)

    def _parse_date(self, date_value):
        """解析日期"""
        if pd.isna(date_value):
            raise ValueError("日期不能為空")
        
        if isinstance(date_value, (datetime, date)):
            return date_value.date() if isinstance(date_value, datetime) else date_value
        
        # 嘗試多種日期格式
        date_formats = ['%Y-%m-%d', '%Y/%m/%d', '%m/%d/%Y', '%d/%m/%Y']
        
        for fmt in date_formats:
            try:
                return datetime.strptime(str(date_value), fmt).date()
            except ValueError:
                continue
        
        raise ValueError(f"無法解析日期格式: {date_value}")

    def _process_makeup_day(self, makeup_date, holiday_name):
        """處理補班日"""
        # 創建補班日記錄 (移除該日期的假日狀態，如果是週末的話要加入工作日)
        
        # 檢查是否為週末
        weekday = makeup_date.weekday()  # 0=Monday, 6=Sunday
        
        if weekday == 5:  # Saturday
            # 為週六補班日創建特殊的工作時間
            self._create_saturday_working_day(makeup_date, holiday_name)

    def _create_saturday_working_day(self, saturday_date, reason):
        """為週六創建工作日"""
        # 這將在 working_calendar.py 中實現
        self.env['tw.working.calendar.override'].create({
            'calendar_id': self.calendar_id.id,
            'date': saturday_date,
            'is_working_day': True,
            'reason': f"因 {reason} 補班",
            'working_hours': 8.0,  # 預設8小時
        })


class ResourceCalendarLeaves(models.Model):
    """擴展資源行事曆假日"""
    _inherit = 'resource.calendar.leaves'
    
    # 增加台灣特有欄位
    is_tw_holiday = fields.Boolean('台灣假日', default=False)
    holiday_type = fields.Selection([
        ('national', '國定假日'),
        ('traditional', '民俗節日'),
        ('special', '特殊假日'),
        ('makeup', '補假')
    ], string='假日類型')
    makeup_date = fields.Date('補班日期')
    
    @api.model
    def get_tw_holidays(self, year, calendar_id=None):
        """取得指定年度的台灣假日"""
        domain = [
            ('is_tw_holiday', '=', True),
            ('date_from', '>=', f'{year}-01-01'),
            ('date_from', '<=', f'{year}-12-31')
        ]
        
        if calendar_id:
            domain.append(('calendar_id', '=', calendar_id))
        
        return self.search(domain)