# models/hr_employee_extension.py

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from datetime import datetime, date, timedelta
import logging

_logger = logging.getLogger(__name__)

class HrEmployee(models.Model):
    """擴展員工模型"""
    _inherit = 'hr.employee'
    
    # l10n_tw_id = fields.Char(
    #     string='Tax ID',
    #     groups='hr.group_hr_user',
    #     tracking=True,
    #     help='Taiwan National ID',
    # )
    # l10n_tw_health_ins_type = fields.Selection(
    #     selection=[
    #         ('1', 'First Class'),
    #         ('2', 'Second Class'),
    #         ('3', 'Third Class'),
    #         ('4', 'Fourth Class'),
    #         ('5', 'Fifth Class'),
    #         ('6', 'Sixth Class'),
    #     ],
    #     groups='hr.group_hr_user',
    #     string='Health Insurance Type',
    #     tracking=True,
    #     default='1',
    #     help='The type of the insured person affects the contribution ratio shared by the government, the employer, and the insured.',
    # )
    # l10n_tw_health_ins_family_count = fields.Integer(
    #     string='Family Count',
    #     groups='hr.group_hr_user',
    #     tracking=True,
    #     help='When the number of dependents covered under the insurance exceeds three, the premium will be calculated for a maximum of four individuals, including the insured person.',
    # )



    # 台灣特有欄位
    tw_employee_id = fields.Char('員工編號', help='台灣員工編號')
    national_id = fields.Char('身分證字號', help='台灣身分證字號')
    labor_insurance_no = fields.Char('勞保證號')
    health_insurance_no = fields.Char('健保證號')
    
    # 薪資相關 - 時薪從合約計算
    hourly_rate = fields.Monetary('時薪', currency_field='currency_id', compute='_compute_hourly_rate', store=True)
    
    # 工作時間相關
    personal_calendar_id = fields.Many2one('resource.calendar', '個人工作行事曆')
    standard_working_hours = fields.Float('標準工作時數', default=8.0)
    weekly_working_hours = fields.Float('每週工作時數', default=40.0)
    
    # 加班相關統計
    current_month_overtime = fields.Float('本月加班時數', compute='_compute_overtime_stats', search='_search_current_month_overtime')
    current_month_displayed_overtime = fields.Float('本月顯示加班時數', compute='_compute_overtime_stats')
    current_month_hidden_overtime = fields.Float('本月隱藏加班時數', compute='_compute_overtime_stats', search='_search_current_month_hidden_overtime')
    
    # 假勤相關
    annual_leave_days = fields.Float('年假天數', default=7.0)
    sick_leave_days = fields.Float('病假天數', default=30.0)
    personal_leave_days = fields.Float('事假天數', default=14.0)
    
    # 權限相關
    can_view_full_overtime = fields.Boolean('可查看完整加班時數', compute='_compute_permissions')
    can_approve_overtime = fields.Boolean('可核准加班', compute='_compute_permissions')
    
    # 關聯記錄
    payslip_ids = fields.One2many('hr.payslip', 'employee_id', '薪資單')
    
    # 出勤整合統計
    auto_overtime_count = fields.Integer(
        string='自動加班記錄數',
        compute='_compute_overtime_integration_stats'
    )
    manual_overtime_count = fields.Integer(
        string='手動加班記錄數',
        compute='_compute_overtime_integration_stats'
    )
    pending_attendance_overtime = fields.Integer(
        string='待處理出勤加班',
        compute='_compute_overtime_integration_stats',
        search='_search_pending_attendance_overtime',
        help='有加班時數但未創建加班記錄的出勤次數'
    )
    
    # 出勤相關統計
    current_month_attendance_count = fields.Integer(
        string='本月出勤次數',
        compute='_compute_attendance_stats'
    )
    
    current_month_overtime_hours = fields.Float(
        string='本月出勤加班時數',
        compute='_compute_attendance_stats'
    )
    
    last_attendance_overtime = fields.Float(
        string='最後一次加班時數',
        compute='_compute_attendance_stats'
    )

    @api.depends('contract_id.wage', 'standard_working_hours')
    def _compute_hourly_rate(self):
        """從合約計算時薪"""
        for employee in self:
            if employee.contract_id and employee.contract_id.wage and employee.standard_working_hours:
                # 從合約月薪計算：月薪 / 30天 / 每日工作時數
                employee.hourly_rate = employee.contract_id.wage / 30 / employee.standard_working_hours
            else:
                employee.hourly_rate = 0.0

    def _compute_overtime_stats(self):
        """計算加班統計 - 使用 tw.hr.attendance"""
        _logger.info("開始計算員工加班統計")
        for employee in self:
            try:
                today = fields.Date.today()
                month_start = today.replace(day=1)
                
                # 查詢本月考勤記錄中的加班時數
                attendance_records = self.env['tw.hr.attendance'].search([
                    ('employee_id', '=', employee.id),
                    ('check_in_date', '>=', month_start),
                    ('check_in_date', '<=', today),
                    ('state', 'in', ['confirmed', 'validated']),
                    ('overtime_hours', '>', 0)
                ])
                
                # 確保為所有員工的所有計算欄位賦值
                actual_hours = sum(attendance_records.mapped('overtime_hours')) if attendance_records else 0.0
                displayed_hours = sum(attendance_records.mapped('displayed_overtime_hours')) if attendance_records else 0.0
                hidden_hours = sum(attendance_records.mapped('hidden_overtime_hours')) if attendance_records else 0.0
                
                employee.current_month_overtime = actual_hours
                employee.current_month_displayed_overtime = displayed_hours
                employee.current_month_hidden_overtime = hidden_hours
                
                _logger.debug(f"員工 {employee.name} (ID:{employee.id}) 加班統計: 實際={actual_hours}, 顯示={displayed_hours}, 隱藏={hidden_hours}")
                
            except Exception as e:
                _logger.error(f"計算員工 {employee.id} 加班統計時發生錯誤: {e}")
                # 發生錯誤時也要賦值，避免 ValueError
                employee.current_month_overtime = 0.0
                employee.current_month_displayed_overtime = 0.0
                employee.current_month_hidden_overtime = 0.0

    def _search_current_month_overtime(self, operator, value):
        """搜索本月加班時數 - 使用 tw.hr.attendance"""
        today = fields.Date.today()
        month_start = today.replace(day=1)
        
        # 查詢有加班記錄的員工
        attendance_records = self.env['tw.hr.attendance'].search([
            ('check_in_date', '>=', month_start),
            ('check_in_date', '<=', today),
            ('state', 'in', ['confirmed', 'validated']),
            ('overtime_hours', '>', 0)
        ])
        
        # 按員工分組計算加班時數
        employee_overtime = {}
        for record in attendance_records:
            if record.employee_id.id not in employee_overtime:
                employee_overtime[record.employee_id.id] = 0
            employee_overtime[record.employee_id.id] += record.overtime_hours
        
        # 根據操作符篩選員工
        employee_ids = []
        for emp_id, overtime_hours in employee_overtime.items():
            if operator == '>' and overtime_hours > value:
                employee_ids.append(emp_id)
            elif operator == '>=' and overtime_hours >= value:
                employee_ids.append(emp_id)
            elif operator == '<' and overtime_hours < value:
                employee_ids.append(emp_id)
            elif operator == '<=' and overtime_hours <= value:
                employee_ids.append(emp_id)
            elif operator == '=' and overtime_hours == value:
                employee_ids.append(emp_id)
            elif operator == '!=' and overtime_hours != value:
                employee_ids.append(emp_id)
        
        return [('id', 'in', employee_ids)]

    def _search_current_month_hidden_overtime(self, operator, value):
        """搜索本月隱藏加班時數 - 使用 tw.hr.attendance"""
        today = fields.Date.today()
        month_start = today.replace(day=1)
        
        # 查詢有隱藏加班記錄的員工
        attendance_records = self.env['tw.hr.attendance'].search([
            ('check_in_date', '>=', month_start),
            ('check_in_date', '<=', today),
            ('state', 'in', ['confirmed', 'validated']),
            ('hidden_overtime_hours', '>', 0)
        ])
        
        # 按員工分組計算隱藏加班時數
        employee_hidden_overtime = {}
        for record in attendance_records:
            if record.employee_id.id not in employee_hidden_overtime:
                employee_hidden_overtime[record.employee_id.id] = 0
            employee_hidden_overtime[record.employee_id.id] += record.hidden_overtime_hours
        
        # 根據操作符篩選員工
        employee_ids = []
        for emp_id, hidden_hours in employee_hidden_overtime.items():
            if operator == '>' and hidden_hours > value:
                employee_ids.append(emp_id)
            elif operator == '>=' and hidden_hours >= value:
                employee_ids.append(emp_id)
            elif operator == '<' and hidden_hours < value:
                employee_ids.append(emp_id)
            elif operator == '<=' and hidden_hours <= value:
                employee_ids.append(emp_id)
            elif operator == '=' and hidden_hours == value:
                employee_ids.append(emp_id)
            elif operator == '!=' and hidden_hours != value:
                employee_ids.append(emp_id)
        
        return [('id', 'in', employee_ids)]

    def _compute_permissions(self):
        """計算權限"""
        for employee in self:
            user = self.env.user
            
            # 檢查是否可查看完整加班時數
            employee.can_view_full_overtime = (
                user.has_group('l10n_tw_hr_payroll.group_hr_manager')
            )
            
            # 檢查是否可核准加班
            employee.can_approve_overtime = (
                user.has_group('l10n_tw_hr_payroll.group_hr_manager') or
                user.has_group('l10n_tw_hr_payroll.group_attendance_integration_manager')
            )

    def get_working_calendar(self):
        """取得員工的工作行事曆"""
        self.ensure_one()
        return (self.personal_calendar_id or 
                self.resource_calendar_id or 
                self.company_id.resource_calendar_id)

    def is_working_day(self, date):
        """檢查指定日期對該員工是否為工作日"""
        self.ensure_one()
        calendar = self.get_working_calendar()
        if calendar:
            return calendar.is_working_day(date)
        return False

    def get_working_hours_for_date(self, date):
        """取得指定日期的工作時數"""
        self.ensure_one()
        calendar = self.get_working_calendar()
        if calendar:
            return calendar.get_effective_working_hours(date)
        return 0.0

    # 移除加班費率計算方法 - 交由 salary rule 處理

    def create_overtime_record(self, date, hours, overtime_type='weekday', description=''):
        """創建加班記錄 - 使用 tw.hr.attendance"""
        self.ensure_one()
        
        vals = {
            'employee_id': self.id,
            'check_in_date': date,
            'overtime_hours': hours,
            'overtime_type': overtime_type,
            'source_type': 'manual',
            'state': 'draft',
        }
        
        return self.env['tw.hr.attendance'].create(vals)

    def get_monthly_overtime_summary(self, year=None, month=None):
        """取得月度加班彙總 - 使用 tw.hr.attendance"""
        self.ensure_one()
        
        if not year or not month:
            today = fields.Date.today()
            year = today.year
            month = today.month
        
        date_from = date(year, month, 1)
        if month == 12:
            date_to = date(year + 1, 1, 1) - timedelta(days=1)
        else:
            date_to = date(year, month + 1, 1) - timedelta(days=1)
        
        records = self.env['tw.hr.attendance'].search([
            ('employee_id', '=', self.id),
            ('check_in_date', '>=', date_from),
            ('check_in_date', '<=', date_to),
            ('state', 'in', ['confirmed', 'validated']),
            ('overtime_hours', '>', 0)
        ])
        
        return {
            'total_actual_hours': sum(records.mapped('overtime_hours')),
            'total_displayed_hours': sum(records.mapped('displayed_overtime_hours')),
            'total_hidden_hours': sum(records.mapped('hidden_overtime_hours')),
            'record_count': len(records),
            'records': records,
            'auto_created_count': len(records.filtered(lambda r: r.source_type == 'auto')),
            'manual_created_count': len(records.filtered(lambda r: r.source_type == 'manual')),
        }

    def get_annual_leave_balance(self):
        """取得年假餘額"""
        self.ensure_one()
        
        # 計算已用年假
        current_year = fields.Date.today().year
        used_leave = self.env['hr.leave'].search([
            ('employee_id', '=', self.id),
            ('holiday_status_id.code', '=', 'ANNUAL'),
            ('date_from', '>=', f'{current_year}-01-01'),
            ('date_to', '<=', f'{current_year}-12-31'),
            ('state', '=', 'validate')
        ])
        
        used_days = sum(used_leave.mapped('number_of_days'))
        return self.annual_leave_days - used_days

    def action_view_overtime_records(self):
        """查看加班記錄 - 重定向到台灣考勤記錄"""
        self.ensure_one()
        
        return {
            'type': 'ir.actions.act_window',
            'name': f'{self.name} - 加班記錄',
            'res_model': 'tw.hr.attendance',
            'view_mode': 'list,form',
            'domain': [('employee_id', '=', self.id), ('overtime_hours', '>', 0)],
            'context': {
                'default_employee_id': self.id,
                'search_default_has_overtime': 1,
            }
        }

    def action_view_payslips(self):
        """查看薪資單"""
        self.ensure_one()
        
        return {
            'type': 'ir.actions.act_window',
            'name': f'{self.name} - 薪資單',
            'res_model': 'hr.payslip',
            'view_mode': 'list,form',
            'domain': [('employee_id', '=', self.id)],
            'context': {'default_employee_id': self.id}
        }

    def get_payslip_for_period(self, date_from, date_to, payslip_type='standard'):
        """取得指定期間的薪資單"""
        self.ensure_one()
        
        return self.env['hr.payslip'].search([
            ('employee_id', '=', self.id),
            ('date_from', '=', date_from),
            ('date_to', '=', date_to),
            ('payslip_type', '=', payslip_type),
            ('state', '!=', 'cancel')
        ], limit=1)

    @api.model
    def get_employees_for_payroll(self, date_from, date_to, department_ids=None):
        """取得指定期間需要計薪的員工"""
        domain = [
            ('contract_ids', '!=', False),
            ('contract_ids.state', '=', 'open'),
            ('contract_ids.date_start', '<=', date_to),
            '|', 
            ('contract_ids.date_end', '=', False),
            ('contract_ids.date_end', '>=', date_from)
        ]
        
        if department_ids:
            domain.append(('department_id', 'in', department_ids))
        
        return self.search(domain)

    def _get_work_days_data(self, date_from, date_to):
        """取得工作日資料"""
        self.ensure_one()
        
        calendar = self.get_working_calendar()
        if not calendar:
            return {}
        
        # 使用 Odoo 標準方法計算工作日
        work_data = calendar._get_work_days_data_batch(
            date_from, date_to, resources=self.resource_id
        )
        
        return work_data.get(self.resource_id.id, {})

    @api.depends('attendance_ids')
    def _compute_overtime_integration_stats(self):
        """計算加班整合統計 - 使用 tw.hr.attendance"""
        for employee in self:
            # 查詢台灣考勤記錄中的加班統計
            tw_attendances = self.env['tw.hr.attendance'].search([
                ('employee_id', '=', employee.id),
                ('overtime_hours', '>', 0)
            ])
            
            # 自動和手動加班記錄統計（基於來源類型）
            # 注意：tw.hr.attendance 模型中沒有 source_type 欄位，暫時使用狀態區分
            employee.auto_overtime_count = len(tw_attendances.filtered(lambda r: r.state == 'validated'))
            employee.manual_overtime_count = len(tw_attendances.filtered(lambda r: r.state == 'confirmed'))
            
            # 待處理的出勤加班（原生 hr.attendance 中有加班但未同步到 tw.hr.attendance）
            # 注意：原生 hr.attendance 沒有 tw_overtime_hours 欄位，使用 worked_hours 判斷
            pending_attendances = employee.attendance_ids.filtered(
                lambda a: a.check_out and a.worked_hours > 8.0  # 超過8小時視為有加班
            )
            # 檢查這些記錄是否已同步到 tw.hr.attendance
            synced_dates = tw_attendances.mapped('check_in_date')
            unsynced_attendances = pending_attendances.filtered(
                lambda a: a.check_in.date() not in synced_dates
            )
            employee.pending_attendance_overtime = len(unsynced_attendances)

    def _search_pending_attendance_overtime(self, operator, value):
        """搜索待處理出勤加班 - 使用同步狀態判斷"""
        # 查找有加班時數的原生出勤記錄
        overtime_attendances = self.env['hr.attendance'].search([
            ('check_out', '!=', False),
            ('worked_hours', '>', 8.0)  # 超過8小時視為有加班
        ])
        
        # 查找已同步的台灣考勤記錄
        synced_tw_attendances = self.env['tw.hr.attendance'].search([
            ('overtime_hours', '>', 0)
        ])
        synced_dates_by_employee = {}
        for tw_att in synced_tw_attendances:
            emp_id = tw_att.employee_id.id
            if emp_id not in synced_dates_by_employee:
                synced_dates_by_employee[emp_id] = set()
            synced_dates_by_employee[emp_id].add(tw_att.check_in_date)
        
        # 按員工分組計算待處理數量
        employee_pending_count = {}
        for attendance in overtime_attendances:
            emp_id = attendance.employee_id.id
            att_date = attendance.check_in.date()
            
            # 檢查是否已同步
            if emp_id in synced_dates_by_employee and att_date in synced_dates_by_employee[emp_id]:
                continue  # 已同步，跳過
            
            if emp_id not in employee_pending_count:
                employee_pending_count[emp_id] = 0
            employee_pending_count[emp_id] += 1
        
        # 根據操作符篩選員工
        employee_ids = []
        for emp_id, pending_count in employee_pending_count.items():
            if operator == '>' and pending_count > value:
                employee_ids.append(emp_id)
            elif operator == '>=' and pending_count >= value:
                employee_ids.append(emp_id)
            elif operator == '<' and pending_count < value:
                employee_ids.append(emp_id)
            elif operator == '<=' and pending_count <= value:
                employee_ids.append(emp_id)
            elif operator == '=' and pending_count == value:
                employee_ids.append(emp_id)
            elif operator == '!=' and pending_count != value:
                employee_ids.append(emp_id)
        
        return [('id', 'in', employee_ids)]

    @api.depends('attendance_ids')
    def _compute_attendance_stats(self):
        """計算出勤統計 - 使用 tw.hr.attendance"""
        for employee in self:
            today = fields.Date.today()
            month_start = today.replace(day=1)
            
            # 本月出勤次數
            month_attendances = employee.attendance_ids.filtered(
                lambda a: a.check_in and a.check_in.date() >= month_start
            )
            employee.current_month_attendance_count = len(month_attendances)
            
            # 本月加班時數（從 tw.hr.attendance 查詢）
            month_tw_attendances = self.env['tw.hr.attendance'].search([
                ('employee_id', '=', employee.id),
                ('check_in_date', '>=', month_start),
                ('check_in_date', '<=', today),
                ('state', 'in', ['confirmed', 'validated']),
                ('overtime_hours', '>', 0)
            ])
            employee.current_month_overtime_hours = sum(month_tw_attendances.mapped('overtime_hours'))
            
            # 最後一次加班時數
            last_attendance = employee.attendance_ids.filtered(
                lambda a: a.check_out and a.worked_hours > 8.0
            )
            if last_attendance:
                # 計算加班時數：工作時數 - 8小時
                employee.last_attendance_overtime = max(0, last_attendance[0].worked_hours - 8.0)
            else:
                employee.last_attendance_overtime = 0.0

    def get_current_month_overtime(self):
        """取得本月加班統計"""
        self.ensure_one()
        today = fields.Date.today()
        return self.get_monthly_overtime_summary(today.year, today.month)

    def can_view_full_overtime(self):
        """檢查是否可以查看完整加班時數"""
        self.ensure_one()
        return self.env.user.has_group('l10n_tw_hr_payroll.group_overtime_full_access')

    def action_create_pending_overtime_records(self):
        """為員工創建待處理的加班記錄"""
        self.ensure_one()
        
        # 查找有加班時數但沒有加班記錄的出勤記錄
        pending_attendances = self.attendance_ids.filtered(
            lambda a: a.check_out and a.worked_hours > 8.0
        )
        
        if not pending_attendances:
            raise UserError(_('沒有找到待處理的出勤加班記錄'))
        
        created_records = []
        error_messages = []
        
        for attendance in pending_attendances:
            try:
                # 直接從原生考勤記錄同步到台灣考勤記錄
                tw_attendance = self.env['tw.hr.attendance'].create_from_hr_attendance(attendance.id)
                created_records.append(tw_attendance)
            except Exception as e:
                error_messages.append(f"出勤記錄 {attendance.id}: {str(e)}")
        
        message = f"為員工 {self.name} 成功創建 {len(created_records)} 筆加班記錄"
        if error_messages:
            message += f"\n錯誤 {len(error_messages)} 筆:\n" + "\n".join(error_messages[:3])
            if len(error_messages) > 3:
                message += f"\n... 還有 {len(error_messages) - 3} 筆錯誤"
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('處理完成'),
                'message': message,
                'type': 'success' if not error_messages else 'warning',
            }
        }

    def action_view_overtime_attendance_integration(self):
        """查看加班與出勤整合狀況"""
        self.ensure_one()
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('加班出勤整合狀況 - %s') % self.name,
            'res_model': 'tw.hr.attendance',
            'view_mode': 'list,form',
            'domain': [('employee_id', '=', self.id)],
            'context': {
                'default_employee_id': self.id,
                'search_default_group_by_source_type': 1,
                'search_default_current_month': 1,
            },
            'target': 'current',
        }

    @api.constrains('national_id')
    def _check_national_id(self):
        """驗證身分證字號格式"""
        for employee in self:
            if employee.national_id:
                # 台灣身分證字號基本格式檢查 (1英文字母 + 9數字)
                import re
                if not re.match(r'^[A-Z][0-9]{9}$', employee.national_id):
                    raise ValidationError(_('身分證字號格式不正確，應為1個英文字母加9個數字'))
                
                # 檢查是否重複
                existing = self.search([
                    ('national_id', '=', employee.national_id),
                    ('id', '!=', employee.id)
                ])
                if existing:
                    raise ValidationError(_('身分證字號不能重複'))


class HrContract(models.Model):
    """擴展合約模型"""
    _inherit = 'hr.contract'
    
    # 台灣特有欄位
    tw_contract_type = fields.Selection([
        ('indefinite', '不定期契約'),
        ('fixed', '定期契約'),
        ('dispatched', '派遣契約'),
        ('part_time', '部分工時契約')
    ], string='契約類型', default='indefinite')
    
    # 薪資結構
    basic_salary = fields.Monetary('基本薪資', currency_field='currency_id')
    position_allowance = fields.Monetary('職務加給', currency_field='currency_id')
    transportation_allowance = fields.Monetary('交通津貼', currency_field='currency_id')
    meal_allowance = fields.Monetary('伙食津貼', currency_field='currency_id')
    
    # 保險相關
    labor_insurance = fields.Boolean('勞保', default=True)
    health_insurance = fields.Boolean('健保', default=True)
    pension_fund = fields.Boolean('勞退', default=True)
    
    # 工作時間
    working_hours_per_day = fields.Float('每日工作時數', default=8.0)
    working_days_per_week = fields.Float('每週工作天數', default=5.0)
    
    @api.depends('basic_salary', 'position_allowance', 'transportation_allowance', 'meal_allowance')
    def _compute_wage(self):
        """計算總薪資"""
        for contract in self:
            contract.wage = (
                (contract.basic_salary or 0) +
                (contract.position_allowance or 0) +
                (contract.transportation_allowance or 0) +
                (contract.meal_allowance or 0)
            )