# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)

class SyncManagement(models.TransientModel):
    """同步管理工具"""
    _name = 'tw.sync.management'
    _description = '同步管理'

    name = fields.Char(string='名稱', default='同步管理', readonly=True)
    
    @api.model
    def check_sync_status(self, *args, **kwargs):
        """檢查同步狀態"""
        _logger.info("=== 同步狀態檢查診斷開始 ===")
        
        try:
            # 統計 hr.attendance 記錄
            _logger.info("統計 hr.attendance 記錄...")
            hr_attendance_count = self.env['hr.attendance'].search_count([
                ('check_in', '!=', False),
                ('employee_id', '!=', False)
            ])
            _logger.info(f"hr.attendance 總記錄數: {hr_attendance_count}")
            
            # 統計 tw.hr.attendance 記錄
            _logger.info("統計 tw.hr.attendance 記錄...")
            tw_attendance_count = self.env['tw.hr.attendance'].search_count([
                ('hr_attendance_id', '!=', False)
            ])
            _logger.info(f"tw.hr.attendance 已同步記錄數: {tw_attendance_count}")
            
            # 查找孤立的 tw.hr.attendance 記錄
            _logger.info("查找孤立的 tw.hr.attendance 記錄...")
            orphaned_tw = self.env['tw.hr.attendance'].search([
                ('hr_attendance_id', '!=', False)
            ])
            _logger.info(f"找到 {len(orphaned_tw)} 筆有 hr_attendance_id 的記錄")
            
            # 檢查這些記錄的 hr.attendance 是否還存在
            orphaned_records = []
            for tw_record in orphaned_tw:
                if not tw_record.hr_attendance_id.exists():
                    orphaned_records.append(tw_record)
                    _logger.warning(f"孤立記錄: tw.hr.attendance ID {tw_record.id} 關聯到不存在的 hr.attendance ID {tw_record.hr_attendance_id.id}")
            
            _logger.info(f"實際孤立記錄數: {len(orphaned_records)}")
            
            # 進一步驗證：查找實際缺失的記錄
            _logger.info("驗證實際缺失的記錄...")
            hr_attendances = self.env['hr.attendance'].search([
                ('check_in', '!=', False),
                ('employee_id', '!=', False)
            ])
            
            actual_missing = 0
            for hr_att in hr_attendances:
                tw_exists = self.env['tw.hr.attendance'].search([('hr_attendance_id', '=', hr_att.id)], limit=1)
                if not tw_exists:
                    actual_missing += 1
                    if actual_missing <= 5:  # 只記錄前5筆作為範例
                        _logger.info(f"缺失同步: hr.attendance ID {hr_att.id} (員工: {hr_att.employee_id.name})")
            
            _logger.info(f"實際驗證缺失記錄數: {actual_missing}")
            
            result = {
                'hr_attendance_total': hr_attendance_count,
                'tw_attendance_synced': tw_attendance_count,
                'missing_sync': max(0, actual_missing),
                'orphaned_records': len(orphaned_records),
            }
            
            _logger.info(f"同步狀態結果: {result}")
            _logger.info("=== 同步狀態檢查診斷結束 ===")
            
            # 返回用戶友好的通知
            message = f"同步狀態檢查完成：\n"
            message += f"• hr.attendance 總記錄數：{result['hr_attendance_total']}\n"
            message += f"• tw.hr.attendance 已同步：{result['tw_attendance_synced']}\n"
            message += f"• 缺失同步記錄：{result['missing_sync']}\n"
            message += f"• 孤立記錄：{result['orphaned_records']}"
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('同步狀態檢查'),
                    'message': message,
                    'type': 'success' if result['missing_sync'] == 0 and result['orphaned_records'] == 0 else 'warning',
                    'sticky': True,
                }
            }
            
        except Exception as e:
            _logger.error(f"=== 同步狀態檢查失敗 ===")
            _logger.error(f"錯誤詳情: {str(e)}")
            _logger.error(f"錯誤類型: {type(e).__name__}")
            import traceback
            _logger.error(f"錯誤堆疊: {traceback.format_exc()}")
            raise

    @api.model
    def manual_sync_from_hr_attendance(self, *args, **kwargs):
        """手動同步所有未同步的 hr.attendance 記錄"""
        if not self.env.user.has_group('l10n_tw_hr_payroll.group_hr_manager'):
            raise UserError(_('您沒有權限執行手動同步'))
        
        # 查找所有沒有對應 tw.hr.attendance 的 hr.attendance 記錄
        hr_attendances = self.env['hr.attendance'].search([
            ('check_in', '!=', False),
            ('employee_id', '!=', False)
        ])
        
        synced_count = 0
        error_count = 0
        error_messages = []
        
        for hr_attendance in hr_attendances:
            try:
                # 檢查是否已存在對應記錄
                existing_tw = self.env['tw.hr.attendance'].search([
                    ('hr_attendance_id', '=', hr_attendance.id)
                ], limit=1)
                
                if not existing_tw:
                    # 創建新的 tw.hr.attendance 記錄
                    self.env['tw.hr.attendance'].create({
                        'employee_id': hr_attendance.employee_id.id,
                        'check_in': hr_attendance.check_in,
                        'check_out_real': hr_attendance.check_out,  # 使用 check_out_real 儲存實際時間
                        'hr_attendance_id': hr_attendance.id,
                        'state': 'confirmed',
                    })
                    synced_count += 1
                    
            except Exception as e:
                error_count += 1
                error_messages.append(f"HR記錄 {hr_attendance.id}: {str(e)}")
        
        # 返回同步結果
        message = f"同步完成：成功 {synced_count} 筆"
        if error_count > 0:
            message += f"，錯誤 {error_count} 筆"
            
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('手動同步完成'),
                'message': message,
                'type': 'success' if error_count == 0 else 'warning',
                'sticky': True,
            }
        }