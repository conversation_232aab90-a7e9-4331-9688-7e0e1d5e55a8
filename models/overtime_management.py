# models/overtime_management.py

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from datetime import datetime, date, timedelta
import logging

_logger = logging.getLogger(__name__)

class TwOvertimeLimit(models.Model):
    """台灣加班時數限制設定"""
    _name = 'tw.overtime.limit'
    _description = '加班時數限制設定'
    _order = 'priority desc, id desc'

    name = fields.Char('設定名稱', required=True)
    company_id = fields.Many2one('res.company', '公司', default=lambda self: self.env.company)
    
    # 限制設定
    monthly_limit = fields.Float('每月加班時數上限', default=46.0, help='依台灣勞基法，每月加班不得超過46小時')
    display_limit = fields.Float('顯示時數上限', default=30.0, help='超過此時數的部分將被隱藏')
    
    # 審核設定
    auto_approve = fields.Boolean('自動審核', default=False, help='啟用後，符合條件的加班記錄將自動審核通過')
    auto_approve_limit = fields.Float('自動審核時數上限', default=2.0, help='單日加班時數在此限制內將自動審核通過')
    
    # 亂數顯示設定
    use_random_display = fields.Boolean('啟用亂數顯示', default=True, help='啟用後，加班顯示將使用亂數機制，避免所有員工同時顯示或隱藏加班')
    random_hide_probability = fields.Float('完全隱藏機率', default=0.3, help='完全不顯示加班的機率 (0.0-1.0)')
    random_partial_probability = fields.Float('部分顯示機率', default=0.4, help='部分顯示加班的機率 (0.0-1.0)')
    
    # 適用範圍
    department_ids = fields.Many2many('hr.department', string='適用部門')
    employee_ids = fields.Many2many('hr.employee', string='適用員工')
    apply_to_all = fields.Boolean('套用至全公司', default=True)
    
    # 狀態和優先級
    active = fields.Boolean('啟用', default=True)
    priority = fields.Integer('優先級', default=10, help='數字越高優先級越高')
    
    # 日期範圍
    date_from = fields.Date('開始日期')
    date_to = fields.Date('結束日期')

    @api.constrains('monthly_limit', 'display_limit', 'random_hide_probability', 'random_partial_probability')
    def _check_limits(self):
        for record in self:
            if record.monthly_limit <= 0:
                raise ValidationError(_('每月加班時數上限必須大於0'))
            if record.display_limit < 0:
                raise ValidationError(_('顯示時數上限不能為負數'))
            if record.display_limit > record.monthly_limit:
                raise ValidationError(_('顯示時數上限不能超過每月加班時數上限'))
            
            # 驗證亂數機率設定
            if record.random_hide_probability < 0 or record.random_hide_probability > 1:
                raise ValidationError(_('完全隱藏機率必須在 0.0 到 1.0 之間'))
            if record.random_partial_probability < 0 or record.random_partial_probability > 1:
                raise ValidationError(_('部分顯示機率必須在 0.0 到 1.0 之間'))
            if (record.random_hide_probability + record.random_partial_probability) > 1:
                raise ValidationError(_('完全隱藏機率和部分顯示機率的總和不能超過 1.0'))

    def get_applicable_limit_for_employee(self, employee_id, date=None):
        """取得適用於指定員工的加班限制"""
        if date is None:
            date = fields.Date.today()
        
        domain = [
            ('active', '=', True),
            '|', ('date_from', '<=', date), ('date_from', '=', False),
            '|', ('date_to', '>=', date), ('date_to', '=', False),
        ]
        
        # 優先查找特定員工設定
        employee_limits = self.search(domain + [('employee_ids', 'in', [employee_id])])
        if employee_limits:
            return employee_limits[0]
        
        # 查找部門設定
        employee = self.env['hr.employee'].browse(employee_id)
        if employee.department_id:
            dept_limits = self.search(domain + [('department_ids', 'in', [employee.department_id.id])])
            if dept_limits:
                return dept_limits[0]
        
        # 查找全公司設定
        company_limits = self.search(domain + [('apply_to_all', '=', True)])
        if company_limits:
            return company_limits[0]
        
        return False