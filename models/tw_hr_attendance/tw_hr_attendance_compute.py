# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
from datetime import timedelta


class TwHrAttendanceCompute(models.Model):
    _inherit = 'tw.hr.attendance'

    # 計算欄位方法
    @api.depends('check_in')
    def _compute_check_in_date(self):
        """計算打卡日期"""
        for record in self:
            if record.check_in:
                record.check_in_date = record.check_in.date()
            else:
                record.check_in_date = False

    @api.depends('check_in', 'check_out_real')
    def _compute_worked_hours(self):
        """計算實際工作時數"""
        for record in self:
            if record.check_in and record.check_out_real:
                delta = record.check_out_real - record.check_in
                record.worked_hours = delta.total_seconds() / 3600.0
            else:
                record.worked_hours = 0.0

    @api.depends('check_out_real', 'displayed_overtime_hours', 'check_in', 'employee_id')
    def _compute_check_out_display(self):
        """基於權限動態計算顯示的下班時間"""
        for record in self:
            # 如果沒有實際下班時間，不顯示
            if not record.check_out_real:
                record.check_out = False
                continue
                
            # 管理員和HR主管看到真實時間
            if record._is_user_manager_or_admin():
                record.check_out = record.check_out_real
                continue
            
            # HR專員看到真實時間
            if record._is_user_hr_officer_or_above():
                record.check_out = record.check_out_real
                continue
            
            # 一般用戶的顯示邏輯
            if record._can_see_own_record(record):
                # 檢查是否為加班時段
                if record._is_overtime_period():
                    # 如果是加班時段且有顯示加班時數
                    if record.displayed_overtime_hours and record.displayed_overtime_hours > 0:
                        # 計算調整後的下班時間：check_in + 正常工時 + 顯示加班時數
                        normal_hours = record._get_normal_work_hours()
                        total_display_hours = normal_hours + record.displayed_overtime_hours
                        
                        # 直接使用時間差計算，避免時區問題
                        if record.check_in:
                            # 計算實際工作時數
                            actual_work_duration = (record.check_out_real - record.check_in).total_seconds() / 3600.0
                            
                            # 如果顯示時數小於實際時數，調整下班時間
                            if total_display_hours < actual_work_duration:
                                record.check_out = record.check_in + timedelta(hours=total_display_hours)
                            else:
                                record.check_out = record.check_out_real
                        else:
                            record.check_out = record.check_out_real
                    else:
                        # 加班時段但沒有顯示加班時數，顯示正常下班時間
                        normal_hours = record._get_normal_work_hours()
                        if record.check_in:
                            # 只顯示正常工作時間
                            record.check_out = record.check_in + timedelta(hours=normal_hours)
                        else:
                            record.check_out = record.check_out_real
                else:
                    # 正常工作時間，顯示真實時間
                    record.check_out = record.check_out_real
            else:
                # 無權限查看，不顯示
                record.check_out = False

    @api.depends('check_in', 'check_out_real')
    def _compute_worked_hours_real(self):
        """計算實際工作時數"""
        for record in self:
            if record.check_in and record.check_out_real:
                delta = record.check_out_real - record.check_in
                record.worked_hours_real = delta.total_seconds() / 3600.0
            else:
                record.worked_hours_real = 0.0

    @api.depends('check_in', 'check_out_real', 'employee_id')
    def _compute_overtime_hours(self):
        """計算加班時數 - 參考官方 hr.attendance 邏輯"""
        for record in self:
            if not record.check_out_real or not record.employee_id or not record.check_in:
                record.overtime_hours = 0.0
                continue
            
            # 取得員工的工作行事曆
            calendar = record.employee_id.resource_calendar_id or record.employee_id.company_id.resource_calendar_id
            if not calendar:
                # 沒有行事曆設定，全部算加班
                record.overtime_hours = record.worked_hours
                continue
            
            # 取得當地時區
            import pytz
            tz = pytz.timezone(calendar.tz) if calendar.tz else pytz.UTC
            
            # 轉換為當地時間
            local_check_in = record.check_in.astimezone(tz)
            local_check_out = record.check_out_real.astimezone(tz)
            attendance_date = local_check_in.date()
            
            # 取得該日的預期工作時間
            start = local_check_in.replace(hour=0, minute=0, second=0)
            stop = start + timedelta(days=1)
            
            try:
                # 使用官方方法取得預期工作時間
                expected_attendances = record.employee_id._employee_attendance_intervals(start, stop)
                
                if not expected_attendances:
                    # 非工作日，全部算加班
                    record.overtime_hours = record.worked_hours
                    continue
                
                # 計算預期工作時數
                planned_work_duration = 0
                planned_start_dt = None
                planned_end_dt = None
                
                for expected_attendance in expected_attendances:
                    planned_start_dt = min(planned_start_dt, expected_attendance[0]) if planned_start_dt else expected_attendance[0]
                    planned_end_dt = max(planned_end_dt, expected_attendance[1]) if planned_end_dt else expected_attendance[1]
                    planned_work_duration += (expected_attendance[1] - expected_attendance[0]).total_seconds() / 3600.0
                
                if planned_work_duration == 0:
                    # 沒有預期工作時間，全部算加班
                    record.overtime_hours = record.worked_hours
                    continue
                
                # 計算加班時數
                overtime_hours = 0.0
                
                # 早上提前上班的加班時數
                if local_check_in < planned_start_dt:
                    early_overtime = (min(planned_start_dt, local_check_out) - local_check_in).total_seconds() / 3600.0
                    overtime_hours += early_overtime
                
                # 晚上延後下班的加班時數
                if local_check_out > planned_end_dt:
                    late_overtime = (local_check_out - max(planned_end_dt, local_check_in)).total_seconds() / 3600.0
                    overtime_hours += late_overtime
                
                # 如果整個時段都在非工作時間，全部算加班
                if local_check_out <= planned_start_dt or local_check_in >= planned_end_dt:
                    overtime_hours = record.worked_hours
                
                record.overtime_hours = max(0.0, overtime_hours)
                
            except Exception:
                # 如果計算失敗，使用簡單邏輯：超過8小時算加班
                standard_hours = 8.0
                record.overtime_hours = max(0.0, record.worked_hours - standard_hours)

    @api.depends('overtime_hours', 'employee_id', 'check_in')
    def _compute_displayed_overtime_hours(self):
        """計算顯示加班時數 - 根據員工限制設定和亂數機制計算"""
        for record in self:
            if record.overtime_hours <= 0 or not record.employee_id or not record.check_in:
                record.displayed_overtime_hours = record.overtime_hours
                continue
                
            check_date = record.check_in.date()
            
            # 檢查是否有加班時數限制設定
            try:
                limit_setting = self.env['tw.overtime.limit'].get_applicable_limit_for_employee(
                    record.employee_id.id, check_date
                )
            except:
                # 如果查詢失敗，使用亂數決定是否顯示
                record.displayed_overtime_hours = record._apply_random_display_logic(record.overtime_hours)
                continue
            
            if not limit_setting:
                # 沒有限制設定，使用亂數決定是否顯示
                record.displayed_overtime_hours = record._apply_random_display_logic(record.overtime_hours)
                continue
            
            # 計算該月已累計的顯示時數
            month_start = check_date.replace(day=1)
            if check_date.month == 12:
                next_month_start = check_date.replace(year=check_date.year + 1, month=1, day=1)
            else:
                next_month_start = check_date.replace(month=check_date.month + 1, day=1)
            
            # 查找該月該員工其他已處理的考勤記錄的顯示時數
            existing_records = self.search([
                ('employee_id', '=', record.employee_id.id),
                ('check_in', '>=', month_start),
                ('check_in', '<', next_month_start),
                ('id', '!=', record.id),  # 排除當前記錄
                ('overtime_hours', '>', 0)  # 只計算有加班的記錄
            ])
            
            existing_displayed_hours = sum(existing_records.mapped('displayed_overtime_hours'))
            
            # 計算可顯示的時數
            remaining_display_quota = max(0, limit_setting.display_limit - existing_displayed_hours)
            max_displayable_hours = min(record.overtime_hours, remaining_display_quota)
            
            # 應用亂數邏輯到可顯示的時數
            record.displayed_overtime_hours = record._apply_random_display_logic(max_displayable_hours)

    def _apply_random_display_logic(self, max_hours):
        """應用亂數邏輯決定顯示的加班時數"""
        if max_hours <= 0:
            return 0.0
            
        # 檢查是否有加班限制設定，並確認是否啟用亂數顯示
        check_date = self.check_in.date()
        try:
            limit_setting = self.env['tw.overtime.limit'].get_applicable_limit_for_employee(
                self.employee_id.id, check_date
            )
            
            # 如果有設定且未啟用亂數顯示，直接返回最大時數
            if limit_setting and not limit_setting.use_random_display:
                return max_hours
                
        except:
            # 查詢失敗時使用預設亂數邏輯
            pass
            
        # 使用更複雜的種子值來增加隨機性，避免集中在特定日期
        import hashlib
        import random
        
        # 結合員工ID、日期和一些變化因子來增加隨機性
        day_of_year = check_date.timetuple().tm_yday
        week_of_year = check_date.isocalendar()[1]
        
        # 使用多個因子組合來產生更分散的隨機性
        seed_string = f"{self.employee_id.id}_{check_date.strftime('%Y%m%d')}_{day_of_year}_{week_of_year}"
        seed_hash = hashlib.sha256(seed_string.encode()).hexdigest()
        
        # 使用多個不同位置的hash值來產生不同的隨機數
        seed_value1 = int(seed_hash[:8], 16)
        seed_value2 = int(seed_hash[8:16], 16)
        seed_value3 = int(seed_hash[16:24], 16)
        
        # 設定隨機種子，使用組合值
        combined_seed = (seed_value1 ^ seed_value2 ^ seed_value3) % (2**32)
        random.seed(combined_seed)
        
        # 生成多個隨機數來增加變化性
        random_factor1 = random.random()
        random_factor2 = random.random()
        random_factor3 = random.random()
        
        # 使用加權平均來決定最終的隨機因子
        final_random_factor = (random_factor1 * 0.5 + random_factor2 * 0.3 + random_factor3 * 0.2)
        
        # 使用設定中的機率值，如果沒有設定則使用預設值
        hide_prob = limit_setting.random_hide_probability if limit_setting else 0.3
        partial_prob = limit_setting.random_partial_probability if limit_setting else 0.4
        
        # 根據隨機因子決定顯示策略
        if final_random_factor < hide_prob:
            # 完全不顯示加班
            return 0.0
        elif final_random_factor < (hide_prob + partial_prob):
            # 顯示部分加班時數，但四捨五入到整數小時
            partial_factor = (final_random_factor - hide_prob) / partial_prob  # 0.0 到 1.0
            
            # 產生1到max_hours-1之間的整數小時數
            max_int_hours = int(max_hours)
            if max_int_hours <= 1:
                return 1.0 if max_hours >= 1.0 else 0.0
            
            # 隨機選擇1到max_int_hours-1之間的整數
            display_hours = 1 + int(partial_factor * (max_int_hours - 1))
            return float(min(display_hours, max_int_hours))
        else:
            # 顯示完整加班時數，四捨五入到整數小時
            return float(round(max_hours))

    @api.depends('overtime_hours', 'displayed_overtime_hours')
    def _compute_hidden_overtime_hours(self):
        """計算隱藏加班時數 - 只有主管可見部分"""
        for record in self:
            record.hidden_overtime_hours = record.overtime_hours - record.displayed_overtime_hours

    @api.depends('overtime_hours', 'overtime_status', 'employee_id', 'check_in')
    def _compute_validated_overtime_hours(self):
        """計算核准的加班時數 - 包含自動審核邏輯"""
        for record in self:
            if record.overtime_status == 'approved':
                record.validated_overtime_hours = record.overtime_hours
            elif record.overtime_status == 'pending' and record.overtime_hours > 0:
                # 檢查是否符合自動審核條件
                if record._should_auto_approve():
                    # 自動審核通過
                    record.overtime_status = 'approved'
                    record.validated_overtime_hours = record.overtime_hours
                else:
                    record.validated_overtime_hours = 0.0
            else:
                record.validated_overtime_hours = 0.0

    def _should_auto_approve(self):
        """檢查是否應該自動審核通過"""
        if not self.employee_id or not self.check_in or self.overtime_hours <= 0:
            return False
            
        check_date = self.check_in.date()
        
        try:
            # 取得適用的加班限制設定
            limit_setting = self.env['tw.overtime.limit'].get_applicable_limit_for_employee(
                self.employee_id.id, check_date
            )
            
            if not limit_setting or not limit_setting.auto_approve:
                return False
                
            # 檢查是否在自動審核時數限制內
            if self.overtime_hours <= limit_setting.auto_approve_limit:
                return True
                
        except Exception as e:
            # 如果查詢失敗，不進行自動審核
            import logging
            _logger = logging.getLogger(__name__)
            _logger.warning(f"自動審核檢查失敗: {e}")
            
        return False

    def _compute_show_overtime_details(self):
        """計算是否顯示加班詳情"""
        for record in self:
            record.show_overtime_details = record._is_user_hr_officer_or_above()

    def _compute_show_hidden_overtime(self):
        """計算是否顯示隱藏加班"""
        for record in self:
            record.show_hidden_overtime = record._is_user_manager_or_admin()

    @api.depends('employee_id', 'check_in')
    def _compute_display_name(self):
        """計算顯示名稱"""
        for record in self:
            if record.employee_id and record.check_in:
                record.display_name = f"{record.employee_id.name} - {record.check_in.strftime('%Y-%m-%d %H:%M')}"
            elif record.employee_id:
                record.display_name = record.employee_id.name
            else:
                record.display_name = _('考勤記錄')