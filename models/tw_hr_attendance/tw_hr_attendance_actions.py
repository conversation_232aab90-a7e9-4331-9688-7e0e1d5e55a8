# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
from datetime import datetime, timedelta


class TwHrAttendanceActions(models.Model):
    _inherit = 'tw.hr.attendance'

    def action_confirm(self):
        """確認考勤記錄"""
        for record in self:
            if record.state != 'draft':
                raise UserError(_('只有草稿狀態的記錄可以確認。'))
            record.state = 'confirmed'
        return True

    def action_validate(self):
        """驗證考勤記錄"""
        # 檢查權限
        if not self._is_user_hr_officer_or_above():
            raise UserError(_('您沒有權限驗證考勤記錄。'))
        
        for record in self:
            if record.state != 'confirmed':
                raise UserError(_('只有已確認的記錄可以驗證。'))
            record.state = 'validated'
        return True

    def action_reset_to_draft(self):
        """重設為草稿"""
        # 檢查權限
        if not self._is_user_hr_officer_or_above():
            raise UserError(_('您沒有權限重設考勤記錄。'))
        
        for record in self:
            record.state = 'draft'
        return True

    def action_approve_overtime(self):
        """核准加班"""
        # 檢查權限
        if not self._is_user_manager_or_admin():
            raise UserError(_('您沒有權限核准加班。'))
        
        for record in self:
            if record.overtime_hours <= 0:
                raise UserError(_('沒有加班時數可以核准。'))
            record.overtime_status = 'approved'
        return True

    def action_refuse_overtime(self):
        """拒絕加班"""
        # 檢查權限
        if not self._is_user_manager_or_admin():
            raise UserError(_('您沒有權限拒絕加班。'))
        
        for record in self:
            record.overtime_status = 'refused'
        return True

    def action_reset_overtime_approval(self):
        """重設加班核准狀態"""
        # 檢查權限
        if not self._is_user_manager_or_admin():
            raise UserError(_('您沒有權限重設加班核准狀態。'))
        
        for record in self:
            record.overtime_status = 'to_approve'
        return True

    def action_sync_from_hr_attendance(self):
        """從原生考勤同步資料"""
        if not self._is_user_hr_officer_or_above():
            raise UserError(_('您沒有權限執行同步操作。'))
        
        synced_count = 0
        for record in self:
            if record.hr_attendance_id:
                hr_record = record.hr_attendance_id
                # 同步基本資料
                record.write({
                    'employee_id': hr_record.employee_id.id,
                    'check_in': hr_record.check_in,
                    'check_out_real': hr_record.check_out,
                })
                synced_count += 1
        
        if synced_count > 0:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('同步完成'),
                    'message': _('已同步 %d 筆記錄') % synced_count,
                    'type': 'success',
                }
            }
        else:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('同步結果'),
                    'message': _('沒有需要同步的記錄'),
                    'type': 'info',
                }
            }

    def action_create_from_hr_attendance(self):
        """從原生考勤創建台灣考勤記錄"""
        if not self._is_user_hr_officer_or_above():
            raise UserError(_('您沒有權限執行此操作。'))
        
        # 取得選定的日期範圍（可以通過context傳入）
        context = self.env.context
        date_from = context.get('date_from')
        date_to = context.get('date_to')
        
        if not date_from or not date_to:
            raise UserError(_('請指定日期範圍。'))
        
        # 查找原生考勤記錄
        hr_attendances = self.env['hr.attendance'].search([
            ('check_in', '>=', date_from),
            ('check_in', '<=', date_to),
            ('check_out', '!=', False),  # 只處理已下班的記錄
        ])
        
        created_count = 0
        for hr_record in hr_attendances:
            # 檢查是否已經存在對應的台灣考勤記錄
            existing = self.search([
                ('hr_attendance_id', '=', hr_record.id)
            ])
            
            if not existing:
                # 創建新的台灣考勤記錄
                self.create({
                    'employee_id': hr_record.employee_id.id,
                    'check_in': hr_record.check_in,
                    'check_out_real': hr_record.check_out,
                    'hr_attendance_id': hr_record.id,
                    'state': 'draft',
                })
                created_count += 1
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('創建完成'),
                'message': _('已創建 %d 筆台灣考勤記錄') % created_count,
                'type': 'success',
            }
        }

    def action_open_employee_attendances(self):
        """開啟員工考勤記錄"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': _('員工考勤記錄'),
            'res_model': 'tw.hr.attendance',
            'view_mode': 'list,form',
            'domain': [('employee_id', '=', self.employee_id.id)],
            'context': {'default_employee_id': self.employee_id.id},
        }

    def action_open_overtime_details(self):
        """開啟加班詳情"""
        self.ensure_one()
        if not self._is_user_hr_officer_or_above():
            raise UserError(_('您沒有權限查看加班詳情。'))
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('加班詳情'),
            'res_model': 'tw.hr.attendance',
            'view_mode': 'form',
            'res_id': self.id,
            'target': 'new',
        }

    @api.model
    def action_batch_approve_overtime(self):
        """批量核准加班"""
        if not self._is_user_manager_or_admin():
            raise UserError(_('您沒有權限批量核准加班。'))
        
        active_ids = self.env.context.get('active_ids', [])
        if not active_ids:
            raise UserError(_('請選擇要核准的記錄。'))
        
        records = self.browse(active_ids)
        approved_count = 0
        
        for record in records:
            if record.overtime_hours > 0 and record.overtime_status == 'to_approve':
                record.overtime_status = 'approved'
                approved_count += 1
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('批量核准完成'),
                'message': _('已核准 %d 筆加班記錄') % approved_count,
                'type': 'success',
            }
        }

    @api.model
    def get_attendance_summary(self, employee_id, date_from, date_to):
        """取得考勤摘要"""
        domain = [
            ('employee_id', '=', employee_id),
            ('check_in_date', '>=', date_from),
            ('check_in_date', '<=', date_to),
        ]
        
        records = self.search(domain)
        
        summary = {
            'total_days': len(records),
            'total_work_hours': sum(records.mapped('worked_hours_real')),
            'total_overtime_hours': sum(records.mapped('overtime_hours')),
            'approved_overtime_hours': sum(records.filtered(lambda r: r.overtime_status == 'approved').mapped('overtime_hours')),
            'pending_overtime_hours': sum(records.filtered(lambda r: r.overtime_status == 'to_approve').mapped('overtime_hours')),
        }
        
        return summary

    def action_delete_with_sync(self):
        """同步刪除台灣考勤記錄和對應的系統記錄"""
        if not (self.env.user.has_group('l10n_tw_hr_payroll.group_hr_manager') or
                self.env.user.has_group('l10n_tw_hr_payroll.group_system_admin')):
            raise UserError(_('您沒有權限執行同步刪除操作'))
        
        # 收集要刪除的 hr.attendance 記錄
        hr_attendance_to_delete = self.filtered('hr_attendance_id').mapped('hr_attendance_id')
        record_count = len(self)
        hr_count = len(hr_attendance_to_delete)
        
        # 先刪除 tw.hr.attendance 記錄
        self.unlink()
        
        # 刪除對應的 hr.attendance 記錄
        deleted_hr_count = 0
        if hr_attendance_to_delete:
            try:
                hr_attendance_to_delete.unlink()
                deleted_hr_count = hr_count
            except Exception as e:
                import logging
                _logger = logging.getLogger('tw.attendance.delete')
                _logger.warning(f"刪除 hr.attendance 記錄時發生錯誤: {e}")
        
        # 返回重新載入動作以重新整理列表
        message = _('已刪除 %d 筆台灣考勤記錄') % record_count
        if deleted_hr_count > 0:
            message += _('，同時刪除 %d 筆系統考勤記錄') % deleted_hr_count
        
        return {
            'type': 'ir.actions.client',
            'tag': 'reload',
            'params': {
                'message': message
            }
        }

    def sync_to_hr_attendance(self):
        """同步到原生 hr.attendance"""
        self.ensure_one()
        if not self.hr_attendance_id:
            # 創建新的 hr.attendance 記錄
            hr_attendance = self.env['hr.attendance'].create({
                'employee_id': self.employee_id.id,
                'check_in': self.check_in,
                'check_out': self.check_out_real,
            })
            self.hr_attendance_id = hr_attendance.id
        else:
            # 更新現有記錄
            self.hr_attendance_id.write({
                'check_in': self.check_in,
                'check_out': self.check_out_real,
            })

    @api.model_create_multi
    def create(self, vals_list):
        """創建時確保與原生 hr.attendance 的一致性"""
        records = super().create(vals_list)
        
        for record in records:
            # 如果沒有關聯的 hr.attendance，嘗試創建
            if not record.hr_attendance_id and record.check_in:
                try:
                    record.sync_to_hr_attendance()
                except Exception as e:
                    # 記錄錯誤但不阻止創建
                    import logging
                    _logger = logging.getLogger('tw.hr.attendance.sync')
                    _logger.warning(f"創建 tw.hr.attendance {record.id} 時同步到 hr.attendance 失敗: {e}")
            
            # 觸發自動審核檢查
            if record.overtime_hours > 0 and record.overtime_status == 'to_approve':
                if record._should_auto_approve():
                    record.overtime_status = 'approved'
        
        return records

    def write(self, vals):
        """修改時同步到原生 hr.attendance"""
        result = super().write(vals)
        
        # 檢查是否跳過同步（避免循環同步）
        if self.env.context.get('skip_sync'):
            return result
        
        # 如果修改了關鍵欄位，同步到 hr.attendance
        sync_fields = ['check_in', 'check_out', 'employee_id']
        if any(field in vals for field in sync_fields):
            for record in self:
                if record.hr_attendance_id:
                    try:
                        record.sync_to_hr_attendance()
                    except Exception as e:
                        import logging
                        _logger = logging.getLogger('tw.hr.attendance.sync')
                        _logger.warning(f"同步 tw.hr.attendance {record.id} 到 hr.attendance 失敗: {e}")
        
        return result

    def unlink(self):
        """刪除時確保不影響原生 hr.attendance 記錄"""
        # 記錄關聯的 hr.attendance 記錄，但不刪除它們
        # 因為 tw.hr.attendance 是從 hr.attendance 同步而來的
        hr_attendance_ids = []
        for record in self:
            if record.hr_attendance_id:
                hr_attendance_ids.append(record.hr_attendance_id.id)
        
        result = super().unlink()
        
        if hr_attendance_ids:
            import logging
            _logger = logging.getLogger('tw.hr.attendance.sync')
            _logger.info(f"刪除了 {len(hr_attendance_ids)} 筆 tw.hr.attendance 記錄，對應的 hr.attendance 記錄保持不變")
        
        return result

    @api.model
    def manual_sync_from_hr_attendance(self):
        """手動同步所有未同步的 hr.attendance 記錄"""
        if not self.env.user.has_group('l10n_tw_hr_payroll.group_hr_manager'):
            raise ValidationError(_('您沒有權限執行手動同步'))
        
        # 查找所有沒有對應 tw.hr.attendance 的 hr.attendance 記錄
        hr_attendances = self.env['hr.attendance'].search([
            ('check_in', '!=', False),
            ('employee_id', '!=', False)
        ])
        
        synced_count = 0
        error_count = 0
        error_messages = []
        
        for hr_attendance in hr_attendances:
            try:
                # 檢查是否已存在對應記錄
                existing_tw = self.search([
                    ('hr_attendance_id', '=', hr_attendance.id)
                ], limit=1)
                
                if not existing_tw:
                    # 創建新的 tw.hr.attendance 記錄
                    new_record = self.create({
                        'employee_id': hr_attendance.employee_id.id,
                        'check_in': hr_attendance.check_in,
                        'check_out_real': hr_attendance.check_out,
                        'hr_attendance_id': hr_attendance.id,
                        'state': 'confirmed',
                    })
                    # 觸發自動審核檢查
                    if new_record.overtime_hours > 0 and new_record.overtime_status == 'to_approve':
                        if new_record._should_auto_approve():
                            new_record.overtime_status = 'approved'
                    synced_count += 1
                    
            except Exception as e:
                error_count += 1
                error_messages.append(f"HR記錄 {hr_attendance.id}: {str(e)}")
        
        # 返回同步結果
        message = f"同步完成：成功 {synced_count} 筆"
        if error_count > 0:
            message += f"，錯誤 {error_count} 筆"
            
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('手動同步完成'),
                'message': message,
                'type': 'success' if error_count == 0 else 'warning',
            }
        }

    @api.model
    def action_batch_refuse_overtime(self):
        """批量拒絕加班"""
        if not self._is_user_manager_or_admin():
            raise UserError(_('您沒有權限批量拒絕加班。'))
        
        active_ids = self.env.context.get('active_ids', [])
        if not active_ids:
            raise UserError(_('請選擇要拒絕的記錄。'))
        
        records = self.browse(active_ids)
        refused_count = 0
        
        for record in records:
            if record.overtime_hours > 0 and record.overtime_status == 'to_approve':
                record.overtime_status = 'refused'
                refused_count += 1
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('批量拒絕完成'),
                'message': _('已拒絕 %d 筆加班記錄') % refused_count,
                'type': 'success',
            }
        }