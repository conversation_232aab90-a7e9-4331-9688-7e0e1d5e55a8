# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
from odoo.osv import expression
from datetime import timedelta
import logging
import pytz

_logger = logging.getLogger(__name__)


class TwHrAttendance(models.Model):
    _name = 'tw.hr.attendance'
    _description = '台灣考勤記錄'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'check_in desc'
    _rec_name = 'display_name'

    # 基本資訊
    employee_id = fields.Many2one(
        'hr.employee', 
        string='員工', 
        required=True, 
        ondelete='cascade',
        tracking=True
    )
    department_id = fields.Many2one(
        'hr.department', 
        string='部門', 
        related='employee_id.department_id',
        readonly=True
    )
    
    # 考勤時間 - 重構為基於權限的動態顯示
    check_in = fields.Datetime(
        string='上班打卡',
        required=True,
        tracking=True
    )
    check_out_real = fields.Datetime(
        string='實際下班打卡',
        tracking=True,
        help='實際的下班打卡時間，只有管理員可見'
    )
    check_out = fields.Datetime(
        string='下班打卡',
        compute='_compute_check_out_display',
        store=False,
        help='根據權限動態顯示的下班時間'
    )
    check_in_date = fields.Date(
        string='打卡日期',
        compute='_compute_check_in_date',
        store=True,
        readonly=True,
        help='從 check_in 提取的日期部分，用於查詢和分組'
    )
    worked_hours = fields.Float(
        string='工作時數',
        compute='_compute_worked_hours',
        store=True,
        readonly=True
    )
    
    # 加班相關欄位 - 基於 business 版本設計
    overtime_hours = fields.Float(
        string='加班時數',
        compute='_compute_overtime_hours',
        store=True,
        help='實際加班時數'
    )
    
    # 隱藏加班功能：顯示時數 vs 隱藏時數
    displayed_overtime_hours = fields.Float(
        string='顯示加班時數',
        compute='_compute_displayed_overtime_hours',
        store=True,
        help='人資專員可見的加班時數'
    )
    
    hidden_overtime_hours = fields.Float(
        string='隱藏加班時數',
        compute='_compute_hidden_overtime_hours',
        store=True,
        help='只有人資主管/管理員可見的隱藏加班時數'
    )
    
    overtime_status = fields.Selection([
        ('to_approve', '待核准'),
        ('approved', '已核准'),
        ('refused', '已拒絕')
    ], string='加班狀態', default='to_approve', tracking=True)
    
    validated_overtime_hours = fields.Float(
        string='核准加班時數',
        compute='_compute_validated_overtime_hours',
        store=True,
        readonly=False,
        tracking=True,
        help='經核准的加班時數，用於薪資計算'
    )
    
    # 權限控制的可見性欄位
    show_overtime_details = fields.Boolean(
        string='顯示加班詳情',
        compute='_compute_show_overtime_details'
    )
    
    show_hidden_overtime = fields.Boolean(
        string='顯示隱藏加班',
        compute='_compute_show_hidden_overtime'
    )
    
    # 原生考勤關聯
    hr_attendance_id = fields.Many2one(
        'hr.attendance', 
        string='原生考勤記錄',
        help='關聯的原生 hr.attendance 記錄'
    )
    
    # 狀態和顯示
    state = fields.Selection([
        ('draft', '草稿'),
        ('confirmed', '已確認'),
        ('validated', '已驗證')
    ], string='狀態', default='draft', tracking=True)
    
    display_name = fields.Char(
        string='顯示名稱', 
        compute='_compute_display_name'
    )
    
    # 備註
    notes = fields.Text(string='備註')

    # 權限檢查方法
    def _is_user_manager_or_admin(self):
        """檢查當前用戶是否為管理員或HR主管"""
        user = self.env.user
        return (user.has_group('l10n_tw_hr_payroll.group_hr_manager') or 
                user.has_group('l10n_tw_hr_payroll.group_system_admin'))
    
    def _is_user_hr_officer_or_above(self):
        """檢查當前用戶是否為HR專員或以上權限"""
        user = self.env.user
        return (user.has_group('l10n_tw_hr_payroll.group_hr_officer') or
                user.has_group('l10n_tw_hr_payroll.group_hr_manager') or
                user.has_group('l10n_tw_hr_payroll.group_system_admin'))
    
    def _can_see_own_record(self, record):
        """檢查用戶是否可以看到自己的記錄"""
        return record.employee_id and record.employee_id.user_id == self.env.user

    @api.model
    def search(self, domain, offset=0, limit=None, order=None, count=False):
        """重寫 search 方法實現權限控制"""
        # 如果是 count 查詢，使用 search_count
        if count:
            return self.search_count(domain, limit=limit)
        
        # 如果是管理員或HR主管，返回完整結果（不過濾）
        if self._is_user_manager_or_admin():
            return super().search(domain, offset=offset, limit=limit, order=order)
        
        # 如果是HR專員，可以看到所有記錄，但需要過濾加班記錄
        if self._is_user_hr_officer_or_above():
            records = super().search(domain, offset=offset, limit=limit, order=order)
            return self._filter_overtime_records(records)
        
        # 一般用戶只能看到自己的記錄，且需要過濾加班記錄
        user_employee = self.env.user.employee_id
        if user_employee:
            # 添加用戶限制條件
            user_domain = [('employee_id.id', '=', user_employee.id)]
            if domain:
                domain = ['&'] + domain + user_domain
            else:
                domain = user_domain
        else:
            # 如果用戶沒有關聯員工，返回空結果
            domain = [('id', '=', False)]
        
        records = super().search(domain, offset=offset, limit=limit, order=order)
        return self._filter_overtime_records(records)
    
    def _filter_overtime_records(self, records):
        """過濾加班記錄：如果是加班時段且 displayed_overtime_hours 為0，則隱藏"""
        filtered_records = self.env['tw.hr.attendance']
        
        for record in records:
            # 檢查是否為加班時段
            if record._is_overtime_period():
                # 如果是加班時段，只有當 displayed_overtime_hours > 0 時才顯示
                if record.displayed_overtime_hours > 0:
                    filtered_records |= record
            else:
                # 正常工作時間，直接顯示
                filtered_records |= record
        
        return filtered_records
    
    @api.model
    def search_count(self, domain, limit=None):
        """重寫 search_count 方法實現權限控制"""
        # 如果是管理員或HR主管，返回完整結果
        if self._is_user_manager_or_admin():
            return super().search_count(domain, limit=limit)
        
        # 對於HR專員和一般用戶，需要實際過濾後計算數量
        records = self.search(domain, count=False)
        return len(records)

    def _is_overtime_period(self):
        """檢查是否為加班時段"""
        self.ensure_one()
        if not self.check_in or not self.employee_id:
            return False
        # 取得員工的工作行事曆

        calendar = self.employee_id.resource_calendar_id or self.employee_id.company_id.resource_calendar_id
        if not calendar:
            return True  # 沒有行事曆設定，假設都是加班
            
        # 取得當地時區

        tz = pytz.timezone(calendar.tz) if calendar.tz else pytz.UTC
        
        
        # 轉換為當地時間
        local_check_in = self.check_in.astimezone(tz)
        local_check_out = self.check_out_real.astimezone(tz) if self.check_out_real else local_check_in
        
        
        # 取得該日的預期工作時間
        start = local_check_in.replace(hour=0, minute=0, second=0)
        stop = start + timedelta(days=1)
        
        try:
            # 使用官方方法取得預期工作時間
            excepted_attendances = self.employee_id._employee_attendance_intervals(start, stop)
            
            if not excepted_attendances:
                return True  # 非工作日，算加班
            # 檢查是否有任何時間在工作時間外
            for excepted_attendance in excepted_attendances:
                planned_start = excepted_attendance[0]
                planned_end = excepted_attendance[1]
                
                # 如果有任何時間在計劃工作時間外，就是加班時段
                if local_check_in >= planned_start and local_check_out <= planned_end:
                    return False
                    
            return True
            
        except Exception as Ex:
            _logger.error(f"取得員工 {self.employee_id.name} 的預期工作時間時發生錯誤 {Ex.__class__.__name__}: {Ex}")
            return True  # 計算失敗時假設是加班

    @api.constrains('check_in', 'check_out_real')
    def _check_validity_check_in_check_out(self):
        """驗證打卡時間的有效性"""
        for record in self:
            if record.check_in and record.check_out_real:
                if record.check_out_real < record.check_in:
                    raise ValidationError(_('下班打卡時間不能早於上班打卡時間。'))

    def _get_normal_work_hours(self):
        """取得正常工作時數"""
        self.ensure_one()
        if not self.employee_id or not self.check_in:
            return 8.0  # 預設8小時
            
        # 取得員工的工作行事曆
        calendar = self.employee_id.resource_calendar_id or self.employee_id.company_id.resource_calendar_id
        if not calendar:
            return 8.0
            
        # 取得當地時區
        tz = pytz.timezone(calendar.tz) if calendar.tz else pytz.UTC
        
        # 轉換為當地時間
        local_check_in = self.check_in.astimezone(tz)
        attendance_date = local_check_in.date()
        
        # 取得該日的預期工作時間
        start = local_check_in.replace(hour=0, minute=0, second=0)
        stop = start + timedelta(days=1)
        
        try:
            # 使用官方方法取得預期工作時間
            expected_attendances = self.employee_id._employee_attendance_intervals(start, stop)
            
            if not expected_attendances:
                return 8.0  # 非工作日預設8小時
                
            # 計算預期工作時數
            planned_work_duration = 0
            for expected_attendance in expected_attendances:
                planned_work_duration += (expected_attendance[1] - expected_attendance[0]).total_seconds() / 3600.0
                
            return planned_work_duration if planned_work_duration > 0 else 8.0
            
        except Exception:
            return 8.0  # 計算失敗時預設8小時