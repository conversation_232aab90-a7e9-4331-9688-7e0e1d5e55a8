# -*- coding: utf-8 -*-
from odoo.tests.common import TransactionCase
from datetime import datetime, date
from unittest.mock import patch


class TestRandomOvertimeDisplay(TransactionCase):
    """測試加班亂數顯示邏輯"""

    def setUp(self):
        super().setUp()
        
        # 建立測試員工
        self.employee = self.env['hr.employee'].create({
            'name': '測試員工',
            'work_email': '<EMAIL>',
        })
        
        # 建立加班限制設定
        self.overtime_limit = self.env['tw.overtime.limit'].create({
            'name': '測試亂數設定',
            'monthly_limit': 46.0,
            'display_limit': 30.0,
            'use_random_display': True,
            'random_hide_probability': 0.3,
            'random_partial_probability': 0.4,
            'apply_to_all': True,
        })

    def test_random_display_logic_consistency(self):
        """測試同一員工同一天的亂數結果一致性"""
        check_in = datetime(2024, 1, 15, 9, 0, 0)
        check_out = datetime(2024, 1, 15, 20, 0, 0)  # 11小時工作，假設3小時加班
        
        # 建立考勤記錄
        attendance = self.env['tw.hr.attendance'].create({
            'employee_id': self.employee.id,
            'check_in': check_in,
            'check_out': check_out,
        })
        
        # 手動設定加班時數進行測試
        attendance.overtime_hours = 3.0
        
        # 多次計算顯示時數，應該得到相同結果
        first_result = attendance._apply_random_display_logic(3.0)
        second_result = attendance._apply_random_display_logic(3.0)
        third_result = attendance._apply_random_display_logic(3.0)
        
        self.assertEqual(first_result, second_result)
        self.assertEqual(second_result, third_result)

    def test_random_display_different_employees(self):
        """測試不同員工同一天的亂數結果可能不同"""
        # 建立第二個員工
        employee2 = self.env['hr.employee'].create({
            'name': '測試員工2',
            'work_email': '<EMAIL>',
        })
        
        check_in = datetime(2024, 1, 15, 9, 0, 0)
        check_out = datetime(2024, 1, 15, 20, 0, 0)
        
        # 建立兩個考勤記錄
        attendance1 = self.env['tw.hr.attendance'].create({
            'employee_id': self.employee.id,
            'check_in': check_in,
            'check_out': check_out,
        })
        
        attendance2 = self.env['tw.hr.attendance'].create({
            'employee_id': employee2.id,
            'check_in': check_in,
            'check_out': check_out,
        })
        
        # 計算顯示時數
        result1 = attendance1._apply_random_display_logic(3.0)
        result2 = attendance2._apply_random_display_logic(3.0)
        
        # 結果可能相同也可能不同，但都應該在合理範圍內
        self.assertTrue(0 <= result1 <= 3.0)
        self.assertTrue(0 <= result2 <= 3.0)

    def test_random_display_different_dates(self):
        """測試同一員工不同日期的亂數結果可能不同"""
        check_in1 = datetime(2024, 1, 15, 9, 0, 0)
        check_out1 = datetime(2024, 1, 15, 20, 0, 0)
        
        check_in2 = datetime(2024, 1, 16, 9, 0, 0)
        check_out2 = datetime(2024, 1, 16, 20, 0, 0)
        
        # 建立兩個不同日期的考勤記錄
        attendance1 = self.env['tw.hr.attendance'].create({
            'employee_id': self.employee.id,
            'check_in': check_in1,
            'check_out': check_out1,
        })
        
        attendance2 = self.env['tw.hr.attendance'].create({
            'employee_id': self.employee.id,
            'check_in': check_in2,
            'check_out': check_out2,
        })
        
        # 計算顯示時數
        result1 = attendance1._apply_random_display_logic(3.5)
        result2 = attendance2._apply_random_display_logic(3.5)
        
        # 結果都應該在合理範圍內且為整數小時
        self.assertTrue(0 <= result1 <= 4.0)
        self.assertTrue(0 <= result2 <= 4.0)
        self.assertEqual(result1, float(int(result1)))  # 檢查是否為整數小時
        self.assertEqual(result2, float(int(result2)))  # 檢查是否為整數小時

    def test_integer_hours_output(self):
        """測試輸出結果為整數小時"""
        check_in = datetime(2024, 1, 15, 9, 0, 0)
        check_out = datetime(2024, 1, 15, 20, 30, 0)  # 11.5小時工作
        
        attendance = self.env['tw.hr.attendance'].create({
            'employee_id': self.employee.id,
            'check_in': check_in,
            'check_out': check_out,
        })
        
        # 測試不同的加班時數輸入
        test_hours = [1.3, 2.7, 3.1, 4.9]
        
        for hours in test_hours:
            result = attendance._apply_random_display_logic(hours)
            # 結果應該是整數小時
            self.assertEqual(result, float(int(result)),
                           f"輸入 {hours} 小時，結果 {result} 不是整數小時")
            # 結果應該在合理範圍內
            self.assertTrue(0 <= result <= round(hours))

    def test_distributed_randomness(self):
        """測試亂數分布的分散性"""
        results = []
        
        # 測試連續30天的結果
        for day in range(1, 31):
            check_in = datetime(2024, 1, day, 9, 0, 0)
            check_out = datetime(2024, 1, day, 20, 0, 0)
            
            attendance = self.env['tw.hr.attendance'].create({
                'employee_id': self.employee.id,
                'check_in': check_in,
                'check_out': check_out,
            })
            
            result = attendance._apply_random_display_logic(3.0)
            results.append(result)
        
        # 檢查結果的多樣性
        unique_results = set(results)
        self.assertGreater(len(unique_results), 1, "亂數結果應該有多樣性")
        
        # 檢查是否有不同的顯示策略（0, 部分顯示, 完整顯示）
        has_zero = 0.0 in results
        has_full = 3.0 in results
        has_partial = any(0 < r < 3.0 for r in results)
        
        # 至少應該有兩種不同的策略出現
        strategies_count = sum([has_zero, has_full, has_partial])
        self.assertGreaterEqual(strategies_count, 2, "應該有多種顯示策略出現")

    def test_disable_random_display(self):
        """測試停用亂數顯示時的行為"""
        # 停用亂數顯示
        self.overtime_limit.use_random_display = False
        
        check_in = datetime(2024, 1, 15, 9, 0, 0)
        check_out = datetime(2024, 1, 15, 20, 0, 0)
        
        attendance = self.env['tw.hr.attendance'].create({
            'employee_id': self.employee.id,
            'check_in': check_in,
            'check_out': check_out,
        })
        
        # 停用亂數時應該返回完整時數
        result = attendance._apply_random_display_logic(3.0)
        self.assertEqual(result, 3.0)

    def test_zero_overtime_hours(self):
        """測試零加班時數的處理"""
        check_in = datetime(2024, 1, 15, 9, 0, 0)
        check_out = datetime(2024, 1, 15, 17, 0, 0)  # 8小時正常工作
        
        attendance = self.env['tw.hr.attendance'].create({
            'employee_id': self.employee.id,
            'check_in': check_in,
            'check_out': check_out,
        })
        
        # 零時數應該返回零
        result = attendance._apply_random_display_logic(0.0)
        self.assertEqual(result, 0.0)