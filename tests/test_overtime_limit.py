from odoo.tests.common import TransactionCase
from odoo import fields

class TestTwOvertimeLimit(TransactionCase):

    def setUp(self):
        super().setUp()
        self.company = self.env.company
        self.department = self.env['hr.department'].create({
            'name': 'Test Department',
            'company_id': self.company.id,
        })
        # Employees
        self.employee_specific = self.env['hr.employee'].create({
            'name': 'Employee Specific',
            'department_id': self.department.id,
            'company_id': self.company.id,
        })
        self.employee_dept = self.env['hr.employee'].create({
            'name': 'Employee Department',
            'department_id': self.department.id,
            'company_id': self.company.id,
        })
        self.employee_default = self.env['hr.employee'].create({
            'name': 'Employee Default',
            'company_id': self.company.id,
        })
        # Limits
        self.company_limit = self.env['tw.overtime.limit'].create({
            'name': 'Company Limit',
            'monthly_limit': 46.0,
            'display_limit': 30.0,
            'apply_to_all': True,
            'company_id': self.company.id,
        })
        self.department_limit = self.env['tw.overtime.limit'].create({
            'name': 'Department Limit',
            'monthly_limit': 40.0,
            'display_limit': 25.0,
            'department_ids': [(6, 0, [self.department.id])],
            'company_id': self.company.id,
        })
        self.employee_limit = self.env['tw.overtime.limit'].create({
            'name': 'Employee Limit',
            'monthly_limit': 50.0,
            'display_limit': 35.0,
            'employee_ids': [(6, 0, [self.employee_specific.id])],
            'company_id': self.company.id,
        })

    def test_employee_specific_limit(self):
        limit = self.env['tw.overtime.limit'].get_applicable_limit_for_employee(
            self.employee_specific.id
        )
        self.assertEqual(limit, self.employee_limit)

    def test_department_limit(self):
        limit = self.env['tw.overtime.limit'].get_applicable_limit_for_employee(
            self.employee_dept.id
        )
        self.assertEqual(limit, self.department_limit)

    def test_company_default_limit(self):
        limit = self.env['tw.overtime.limit'].get_applicable_limit_for_employee(
            self.employee_default.id
        )
        self.assertEqual(limit, self.company_limit)
